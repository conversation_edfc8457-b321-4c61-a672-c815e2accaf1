"use client"

import type React from "react"

import { useState, useRef } from "react"
import { motion, useMotionValue, useSpring, useTransform } from "framer-motion"

interface LunarGlowCardProps {
  children: React.ReactNode
  className?: string
  glowColor?: "blue" | "purple" | "cyan" | "silver"
  intensity?: "low" | "medium" | "high"
}

export default function LunarGlowCard({
  children,
  className = "",
  glowColor = "blue",
  intensity = "medium",
}: LunarGlowCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  // Mouse position for 3D effect
  const x = useMotionValue(0)
  const y = useMotionValue(0)

  // Spring animations for smoother movement
  const rotateX = useSpring(useTransform(y, [-100, 100], [5, -5]), { stiffness: 200, damping: 25 })
  const rotateY = useSpring(useTransform(x, [-100, 100], [-5, 5]), { stiffness: 200, damping: 25 })

  // Handle mouse move for 3D effect
  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = cardRef.current?.getBoundingClientRect()
    if (!rect) return

    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    x.set(e.clientX - centerX)
    y.set(e.clientY - centerY)
  }

  // Reset on mouse leave
  const handleMouseLeave = () => {
    setIsHovered(false)
    x.set(0)
    y.set(0)
  }

  // Update glow colors to black and white
  const getGlowColor = () => {
    const colors = {
      blue: "from-gray-300/20 via-gray-300/10 to-transparent",
      purple: "from-gray-300/20 via-gray-300/10 to-transparent",
      cyan: "from-gray-300/20 via-gray-300/10 to-transparent",
      silver: "from-gray-300/20 via-gray-300/10 to-transparent",
    }

    return colors[glowColor]
  }

  // Get glow intensity
  const getGlowIntensity = () => {
    const intensities = {
      low: { blur: "blur-md", opacity: 0.3 },
      medium: { blur: "blur-lg", opacity: 0.5 },
      high: { blur: "blur-xl", opacity: 0.7 },
    }

    return intensities[intensity]
  }

  const { blur, opacity } = getGlowIntensity()

  return (
    <motion.div
      ref={cardRef}
      className={`relative rounded-2xl overflow-hidden ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      style={{
        rotateX,
        rotateY,
        transformStyle: "preserve-3d",
      }}
    >
      {/* Lunar glow effect */}
      <motion.div
        className={`absolute -inset-0.5 bg-gradient-radial ${getGlowColor()} ${blur} z-0`}
        animate={{
          opacity: isHovered ? opacity * 1.5 : opacity,
          scale: isHovered ? 1.05 : 1,
        }}
        transition={{ duration: 0.3 }}
      />

      {/* Content */}
      <div className="relative z-10 h-full">{children}</div>

      {/* Subtle animated stars */}
      {isHovered && (
        <>
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 rounded-full bg-white"
              initial={{
                x: Math.random() * 100 - 50 + "%",
                y: Math.random() * 100 - 50 + "%",
                opacity: 0,
              }}
              animate={{
                opacity: [0, 0.8, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 1.5,
                delay: i * 0.3,
                repeat: Number.POSITIVE_INFINITY,
                repeatDelay: Math.random() * 2,
              }}
            />
          ))}
        </>
      )}
    </motion.div>
  )
}
