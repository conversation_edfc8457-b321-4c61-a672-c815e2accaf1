"use client"

import { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"
import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ArrowUpRight } from "lucide-react"

export default function DataVisualization() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [activeChart, setActiveChart] = useState("bar")
  const [isHovered, setIsHovered] = useState(false)

  const chartData = {
    labels: ["Q1", "Q2", "Q3", "Q4"],
    datasets: [
      {
        label: "Revenue",
        data: [120, 150, 180, 210],
        color: "#3b82f6",
      },
      {
        label: "Expenses",
        data: [80, 100, 110, 130],
        color: "#ef4444",
      },
      {
        label: "Profit",
        data: [40, 50, 70, 80],
        color: "#10b981",
      },
    ],
  }

  const pieData = [
    { label: "Product A", value: 35, color: "#3b82f6" },
    { label: "Product B", value: 25, color: "#ef4444" },
    { label: "Product C", value: 20, color: "#10b981" },
    { label: "Product D", value: 15, color: "#f59e0b" },
    { label: "Other", value: 5, color: "#8b5cf6" },
  ]

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // Draw chart based on active type
    const drawChart = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      const padding = 40
      const chartWidth = canvas.width - padding * 2
      const chartHeight = canvas.height - padding * 2

      // Draw axes
      ctx.strokeStyle = "rgba(255, 255, 255, 0.2)"
      ctx.lineWidth = 1

      // Y-axis
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, padding + chartHeight)
      ctx.stroke()

      // X-axis
      ctx.beginPath()
      ctx.moveTo(padding, padding + chartHeight)
      ctx.lineTo(padding + chartWidth, padding + chartHeight)
      ctx.stroke()

      // Draw labels
      ctx.fillStyle = "rgba(255, 255, 255, 0.7)"
      ctx.font = "12px Arial"
      ctx.textAlign = "center"

      // X-axis labels
      chartData.labels.forEach((label, index) => {
        const x = padding + (chartWidth / (chartData.labels.length - 1)) * index
        ctx.fillText(label, x, padding + chartHeight + 20)
      })

      if (activeChart === "bar") {
        drawBarChart(ctx, padding, chartWidth, chartHeight)
      } else if (activeChart === "line") {
        drawLineChart(ctx, padding, chartWidth, chartHeight)
      } else if (activeChart === "pie") {
        drawPieChart(ctx, canvas.width / 2, canvas.height / 2, Math.min(chartWidth, chartHeight) / 2)
      }
    }

    const drawBarChart = (ctx: CanvasRenderingContext2D, padding: number, chartWidth: number, chartHeight: number) => {
      // Find max value for scaling
      const allValues = chartData.datasets.flatMap((dataset) => dataset.data)
      const maxValue = Math.max(...allValues) * 1.2 // Add 20% padding

      // Calculate bar width
      const datasetCount = chartData.datasets.length
      const groupCount = chartData.labels.length
      const groupWidth = chartWidth / groupCount
      const barWidth = (groupWidth * 0.7) / datasetCount
      const groupPadding = groupWidth * 0.15

      // Draw bars
      chartData.datasets.forEach((dataset, datasetIndex) => {
        ctx.fillStyle = dataset.color

        dataset.data.forEach((value, valueIndex) => {
          const x = padding + groupPadding + valueIndex * groupWidth + datasetIndex * barWidth
          const barHeight = (value / maxValue) * chartHeight
          const y = padding + chartHeight - barHeight

          // Draw bar with rounded top
          ctx.beginPath()
          ctx.moveTo(x, padding + chartHeight)
          ctx.lineTo(x, y + 4)
          ctx.arc(x + barWidth / 2, y + 4, barWidth / 2, Math.PI, 0, true)
          ctx.lineTo(x + barWidth, padding + chartHeight)
          ctx.closePath()
          ctx.fill()

          // Add value label
          ctx.fillStyle = "rgba(255, 255, 255, 0.9)"
          ctx.font = "10px Arial"
          ctx.textAlign = "center"
          ctx.fillText(value.toString(), x + barWidth / 2, y - 5)
          ctx.fillStyle = dataset.color
        })
      })

      // Draw legend
      drawLegend(ctx, padding, chartHeight)
    }

    const drawLineChart = (ctx: CanvasRenderingContext2D, padding: number, chartWidth: number, chartHeight: number) => {
      // Find max value for scaling
      const allValues = chartData.datasets.flatMap((dataset) => dataset.data)
      const maxValue = Math.max(...allValues) * 1.2 // Add 20% padding

      // Draw grid lines
      ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
      ctx.lineWidth = 1

      // Horizontal grid lines
      for (let i = 0; i <= 4; i++) {
        const y = padding + (chartHeight / 4) * i
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(padding + chartWidth, y)
        ctx.stroke()
      }

      // Draw lines and points
      chartData.datasets.forEach((dataset) => {
        const points = dataset.data.map((value, index) => {
          const x = padding + (chartWidth / (dataset.data.length - 1)) * index
          const y = padding + chartHeight - (value / maxValue) * chartHeight
          return { x, y }
        })

        // Draw line
        ctx.strokeStyle = dataset.color
        ctx.lineWidth = 3
        ctx.beginPath()

        points.forEach((point, index) => {
          if (index === 0) {
            ctx.moveTo(point.x, point.y)
          } else {
            // Create curved line
            const prevPoint = points[index - 1]
            const cpx1 = prevPoint.x + (point.x - prevPoint.x) / 2
            const cpx2 = prevPoint.x + (point.x - prevPoint.x) / 2
            const cpy1 = prevPoint.y
            const cpy2 = point.y

            ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, point.x, point.y)
          }
        })

        ctx.stroke()

        // Draw gradient area under the line
        const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight)
        gradient.addColorStop(0, `${dataset.color}40`) // 25% opacity
        gradient.addColorStop(1, `${dataset.color}00`) // 0% opacity

        ctx.fillStyle = gradient
        ctx.beginPath()

        // Start from bottom left
        ctx.moveTo(points[0].x, padding + chartHeight)

        // Draw line to first point
        ctx.lineTo(points[0].x, points[0].y)

        // Draw curved line through all points
        for (let i = 1; i < points.length; i++) {
          const prevPoint = points[i - 1]
          const point = points[i]

          const cpx1 = prevPoint.x + (point.x - prevPoint.x) / 2
          const cpx2 = prevPoint.x + (point.x - prevPoint.x) / 2
          const cpy1 = prevPoint.y
          const cpy2 = point.y

          ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, point.x, point.y)
        }

        // Complete the shape
        ctx.lineTo(points[points.length - 1].x, padding + chartHeight)
        ctx.closePath()
        ctx.fill()

        // Draw points
        points.forEach((point) => {
          ctx.fillStyle = dataset.color
          ctx.beginPath()
          ctx.arc(point.x, point.y, 5, 0, Math.PI * 2)
          ctx.fill()

          ctx.fillStyle = "#ffffff"
          ctx.beginPath()
          ctx.arc(point.x, point.y, 2, 0, Math.PI * 2)
          ctx.fill()
        })
      })

      // Draw legend
      drawLegend(ctx, padding, chartHeight)
    }

    const drawPieChart = (ctx: CanvasRenderingContext2D, centerX: number, centerY: number, radius: number) => {
      // Calculate total value
      const total = pieData.reduce((sum, item) => sum + item.value, 0)

      // Draw pie slices
      let startAngle = -Math.PI / 2 // Start from top

      pieData.forEach((item) => {
        const sliceAngle = (item.value / total) * Math.PI * 2

        ctx.fillStyle = item.color
        ctx.beginPath()
        ctx.moveTo(centerX, centerY)
        ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle)
        ctx.closePath()
        ctx.fill()

        // Draw slice border
        ctx.strokeStyle = "rgba(0, 0, 0, 0.1)"
        ctx.lineWidth = 1
        ctx.stroke()

        // Calculate label position
        const labelAngle = startAngle + sliceAngle / 2
        const labelRadius = radius * 0.7
        const labelX = centerX + Math.cos(labelAngle) * labelRadius
        const labelY = centerY + Math.sin(labelAngle) * labelRadius

        // Draw percentage label
        const percentage = Math.round((item.value / total) * 100)
        ctx.fillStyle = "#ffffff"
        ctx.font = "bold 12px Arial"
        ctx.textAlign = "center"
        ctx.textBaseline = "middle"
        ctx.fillText(`${percentage}%`, labelX, labelY)

        startAngle += sliceAngle
      })

      // Draw legend for pie chart
      const legendX = padding
      const legendY = canvas.height - 30

      pieData.forEach((item, index) => {
        const x = legendX + (index * (canvas.width - padding * 2)) / pieData.length

        ctx.fillStyle = item.color
        ctx.beginPath()
        ctx.rect(x, legendY, 10, 10)
        ctx.fill()

        ctx.fillStyle = "rgba(255, 255, 255, 0.7)"
        ctx.font = "10px Arial"
        ctx.textAlign = "left"
        ctx.fillText(item.label, x + 15, legendY + 5)
      })
    }

    const drawLegend = (ctx: CanvasRenderingContext2D, padding: number, chartHeight: number) => {
      const legendY = padding + chartHeight + 40

      chartData.datasets.forEach((dataset, index) => {
        const x = padding + (index * (canvas.width - padding * 2)) / chartData.datasets.length

        ctx.fillStyle = dataset.color
        ctx.beginPath()
        ctx.rect(x, legendY, 10, 10)
        ctx.fill()

        ctx.fillStyle = "rgba(255, 255, 255, 0.7)"
        ctx.font = "10px Arial"
        ctx.textAlign = "left"
        ctx.fillText(dataset.label, x + 15, legendY + 5)
      })
    }

    drawChart()

    // Handle window resize
    const handleResize = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
      drawChart()
    }

    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [activeChart])

  return (
    <motion.div
      className="relative w-full bg-gradient-to-br from-gray-900 to-black rounded-xl overflow-hidden shadow-2xl"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h3 className="text-xl font-bold text-white">Data Analysis</h3>
            <p className="text-gray-400 text-sm">Interactive data visualization</p>
          </div>

          <div className="flex space-x-2">
            <button
              className={`p-2 rounded-lg transition-all duration-300 ${
                activeChart === "bar" ? "bg-white/10 text-white" : "bg-transparent text-gray-400 hover:bg-white/5"
              }`}
              onClick={() => setActiveChart("bar")}
            >
              <BarChart3 className="h-5 w-5" />
            </button>
            <button
              className={`p-2 rounded-lg transition-all duration-300 ${
                activeChart === "line" ? "bg-white/10 text-white" : "bg-transparent text-gray-400 hover:bg-white/5"
              }`}
              onClick={() => setActiveChart("line")}
            >
              <LineChart className="h-5 w-5" />
            </button>
            <button
              className={`p-2 rounded-lg transition-all duration-300 ${
                activeChart === "pie" ? "bg-white/10 text-white" : "bg-transparent text-gray-400 hover:bg-white/5"
              }`}
              onClick={() => setActiveChart("pie")}
            >
              <PieChart className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="h-64">
          <canvas ref={canvasRef} className="w-full h-full"></canvas>
        </div>

        <div className="mt-6 flex justify-between items-center">
          <div className="text-sm text-gray-400">
            Data updated: <span className="text-white">Today, 10:30 AM</span>
          </div>

          <button className="flex items-center text-blue-400 text-sm hover:text-blue-300 transition-colors">
            View Full Report
            <ArrowUpRight className="h-4 w-4 ml-1" />
          </button>
        </div>
      </div>

      <motion.div
        className="absolute inset-0 border-2 border-transparent z-30 rounded-xl pointer-events-none"
        animate={{
          borderColor: isHovered ? "rgba(59, 130, 246, 0.5)" : "rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  )
}
