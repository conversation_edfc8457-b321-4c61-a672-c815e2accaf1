"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"

interface AnimatedTextProps {
  text: string
  className?: string
  delay?: number
  duration?: number
  once?: boolean
}

export default function AnimatedText({
  text,
  className = "",
  delay = 0,
  duration = 0.05,
  once = false,
}: AnimatedTextProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, delay * 1000)

    return () => clearTimeout(timer)
  }, [delay])

  // Split text into words and characters
  const words = text.split(" ")

  return (
    <span className={className}>
      {words.map((word, wordIndex) => (
        <span key={wordIndex} className="inline-block mr-1.5">
          {word.split("").map((char, charIndex) => (
            <motion.span
              key={`${wordIndex}-${charIndex}`}
              initial={{ opacity: 0, y: 15 }}
              animate={isVisible ? { opacity: 1, y: 0 } : {}}
              transition={{
                duration: 0.3,
                delay: delay + wordIndex * 0.1 + charIndex * duration,
                ease: "easeOut",
              }}
              className="inline-block"
              whileHover={{ scale: 1.2, color: "#ffffff" }}
            >
              {char}
            </motion.span>
          ))}
        </span>
      ))}
    </span>
  )
}
