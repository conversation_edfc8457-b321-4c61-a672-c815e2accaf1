"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
  ArrowRight,
  Lightbulb,
  Zap,
  LineChart,
  Sparkles,
  Check,
  Star,
  Clock,
  Target,
  Rocket,
  <PERSON>Chart,
} from "lucide-react"
import AnimatedText from "@/components/animated-text"
import ThreeDCard from "@/components/3d-card"

export default function MvpDevelopmentPage() {
  // Case studies
  const caseStudies = [
    {
      title: "Fintech Payment Platform MVP",
      description: "A streamlined payment solution for high-net-worth individuals with advanced security features.",
      image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?q=80&w=600&auto=format&fit=crop",
      results: "Secured $2.5M in seed funding within 3 months",
    },
    {
      title: "Luxury Travel Concierge App",
      description:
        "An exclusive travel planning application for discerning travelers seeking personalized experiences.",
      image: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?q=80&w=600&auto=format&fit=crop",
      results: "Acquired 500 premium users in first month",
    },
    {
      title: "AI-Powered Investment Advisor",
      description:
        "A sophisticated investment platform that leverages AI to provide personalized portfolio recommendations.",
      image: "https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=600&auto=format&fit=crop",
      results: "Reduced development time by 65%",
    },
  ]

  // Services
  const services = [
    {
      title: "Concept Validation",
      description:
        "Rapidly transform your vision into a functional prototype that captures the essence of your product idea.",
      icon: <Lightbulb className="h-10 w-10 text-yellow-500" />,
      features: ["Rapid prototyping", "User testing frameworks", "Feedback integration", "Iterative refinement"],
    },
    {
      title: "Accelerated MVP Development",
      description:
        "Streamlined development process that delivers a market-ready product with core functionality in record time.",
      icon: <Zap className="h-10 w-10 text-yellow-500" />,
      features: [
        "Agile development methodology",
        "Core feature prioritization",
        "Scalable foundation",
        "Launch preparation",
      ],
    },
    {
      title: "Growth-Ready Architecture",
      description:
        "Technical foundations designed for seamless scaling as your product evolves and your user base grows.",
      icon: <LineChart className="h-10 w-10 text-yellow-500" />,
      features: [
        "Future-proof technology stack",
        "Modular component design",
        "Analytics integration",
        "Expansion planning",
      ],
    },
  ]

  // MVP process
  const mvpProcess = [
    {
      number: "01",
      title: "Ideation & Strategy",
      description: "We refine your concept, identify core features, and develop a strategic roadmap for your MVP.",
      icon: <Lightbulb className="h-6 w-6 text-yellow-500" />,
    },
    {
      number: "02",
      title: "Rapid Development",
      description:
        "Our agile team builds your MVP with a focus on essential functionality and elegant user experience.",
      icon: <Zap className="h-6 w-6 text-yellow-500" />,
    },
    {
      number: "03",
      title: "Testing & Validation",
      description: "We rigorously test your product and gather user feedback to validate your concept in the market.",
      icon: <Target className="h-6 w-6 text-yellow-500" />,
    },
    {
      number: "04",
      title: "Launch & Iteration",
      description: "We support your market entry and help you refine your product based on real-world performance.",
      icon: <Rocket className="h-6 w-6 text-yellow-500" />,
    },
  ]

  // Benefits
  const benefits = [
    {
      title: "Accelerated Time-to-Market",
      description: "Launch your product in a fraction of the time required for traditional development approaches.",
      icon: <Clock className="h-10 w-10 text-yellow-500" />,
    },
    {
      title: "Reduced Initial Investment",
      description: "Focus resources on essential features, minimizing upfront costs while maintaining premium quality.",
      icon: <PieChart className="h-10 w-10 text-yellow-500" />,
    },
    {
      title: "Market Validation",
      description: "Test your concept with real users to validate assumptions and refine your product strategy.",
      icon: <Target className="h-10 w-10 text-yellow-500" />,
    },
    {
      title: "Investor-Ready Product",
      description: "Create a compelling, functional product that demonstrates your vision to potential investors.",
      icon: <Rocket className="h-10 w-10 text-yellow-500" />,
    },
  ]

  return (
    <main className="pt-24 relative">
      {/* Hero Section */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1572177812156-58036aae439c?q=80&w=1920&auto=format&fit=crop"
            alt="MVP Development"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Accelerated Innovation
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="MVP Product Development"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              Rapid development of elegant minimum viable products that validate your concept while maintaining premium
              quality.
            </p>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=1920&auto=format&fit=crop"
            alt="Services background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Services
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Accelerated Product Development
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We help innovative brands bring their ideas to market quickly without compromising on quality or user
              experience.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ThreeDCard className="p-6 h-full">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full">
                    <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 shadow-lg">
                      {service.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-white">{service.title}</h3>
                    <p className="text-gray-300 mb-6">{service.description}</p>

                    <h4 className="text-sm font-semibold text-white mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <span className="bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 rounded-full p-1 mr-3 mt-1 shadow-sm">
                            <svg className="h-2 w-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                              <circle cx="4" cy="4" r="3" />
                            </svg>
                          </span>
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </ThreeDCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* MVP Process */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?q=80&w=1920&auto=format&fit=crop"
            alt="MVP Process"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Process
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              From Concept to Market
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our streamlined approach ensures rapid development without compromising on quality or user experience.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {mvpProcess.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ThreeDCard className="p-6 h-full">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 flex items-center justify-center mr-4 shadow-lg">
                        {step.icon}
                      </div>
                      <div className="text-2xl font-bold text-yellow-500">{step.number}</div>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-white">{step.title}</h3>
                    <p className="text-gray-300">{step.description}</p>
                  </div>
                </ThreeDCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=1920&auto=format&fit=crop"
            alt="Benefits"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Key Advantages
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              The MVP Advantage
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover how our MVP approach can accelerate your path to market while minimizing risk and maximizing
              impact.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ThreeDCard className="p-6 h-full">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full">
                    <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 shadow-lg">
                      {benefit.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-white">{benefit.title}</h3>
                    <p className="text-gray-300">{benefit.description}</p>
                  </div>
                </ThreeDCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1920&auto=format&fit=crop"
            alt="Case Studies background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Success Stories
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              MVP Success Stories
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Explore how our MVP development approach has helped innovative brands bring their ideas to market quickly
              and successfully.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {caseStudies.map((study, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/70 backdrop-blur-sm rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{study.title}</h3>
                  <p className="text-gray-300 mb-4">{study.description}</p>
                  <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3 mb-4">
                    <div className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-400 font-medium">{study.results}</span>
                    </div>
                  </div>
                  <Link
                    href="/portfolio"
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    View Case Study
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1920&auto=format&fit=crop"
            alt="FAQ"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Common Questions
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Frequently Asked Questions
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Learn more about our MVP development approach and how it can benefit your business.
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            {[
              {
                question: "What exactly is an MVP?",
                answer:
                  "An MVP (Minimum Viable Product) is a version of a product with just enough features to satisfy early customers and provide feedback for future development. At Lunar Studio, we create MVPs that maintain premium quality while focusing on core functionality.",
              },
              {
                question: "How long does it take to develop an MVP?",
                answer:
                  "Our accelerated MVP development process typically takes 6-12 weeks, depending on the complexity of your product. We prioritize speed without compromising on quality or user experience.",
              },
              {
                question: "Will my MVP be scalable for future growth?",
                answer:
                  "Absolutely. We design all our MVPs with growth in mind, using scalable architecture and future-proof technology stacks that can evolve as your product and user base grow.",
              },
              {
                question: "How do you maintain quality while accelerating development?",
                answer:
                  "We focus on core features that deliver the most value, use proven technology stacks, leverage reusable components, and employ efficient development methodologies—all while maintaining our exacting standards for code quality and user experience.",
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                className="mb-6 bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-xl font-bold mb-3 text-white">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50 backdrop-blur-sm">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Accelerate Your Product Journey?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how Lunar Studio can help you bring your product to market quickly without compromising
                  on quality.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg shadow-lg shadow-yellow-600/20"
              >
                Schedule a Strategy Session
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
