"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
  <PERSON>Right,
  Send,
  MessageSquare,
  Bot,
  Shield,
  Check,
  Sparkles,
  Star,
  Zap,
  Users,
  Settings,
  Bell,
  FileText,
  Layers,
  Cpu,
} from "lucide-react"
import AnimatedText from "@/components/animated-text"
import ThreeDCard from "@/components/3d-card"
import LunarGlowCard from "@/components/lunar-glow-card"

export default function MessagingBotsPage() {
  return (
    <main className="pt-24 relative">
      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1611606063065-ee7946f0787a?q=80&w=1920&auto=format&fit=crop"
            alt="Messaging Bots header background"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Send className="h-4 w-4 mr-2" />
                Conversational AI
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Telegram & Discord Bots"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              Custom messaging platform bots that automate workflows, enhance community engagement, and deliver
              personalized experiences.
            </p>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1614064641938-3bbee52942c7?q=80&w=1920&auto=format&fit=crop"
            alt="Messaging Bots overview background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
                <span className="text-red-300 text-sm font-medium flex items-center">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Platform-Specific Excellence
                </span>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
                Elevate Your Digital Presence with Custom Messaging Bots
              </h2>
              <p className="text-gray-300 text-lg mb-6">
                Our custom Telegram and Discord bots are meticulously crafted to enhance your brand's digital presence,
                automate routine tasks, and create engaging experiences for your community.
              </p>
              <p className="text-gray-300 mb-8">
                Each bot is tailored to your specific requirements, leveraging the unique capabilities of each platform
                while maintaining your brand's distinctive voice and aesthetic.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Telegram Integration</span>
                  </div>
                  <p className="text-gray-300 text-sm">
                    Custom bots that leverage Telegram's robust API for seamless user interactions
                  </p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Discord Functionality</span>
                  </div>
                  <p className="text-gray-300 text-sm">
                    Feature-rich Discord bots for community management and engagement
                  </p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">AI-Powered Responses</span>
                  </div>
                  <p className="text-gray-300 text-sm">Natural language understanding for intelligent conversations</p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Secure Implementation</span>
                  </div>
                  <p className="text-gray-300 text-sm">Enterprise-grade security for all bot interactions and data</p>
                </div>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              <ThreeDCard className="p-6">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800">
                  <div className="relative h-[400px] rounded-lg overflow-hidden">
                    <Image
                      src="https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?q=80&w=800&auto=format&fit=crop"
                      alt="Messaging Bot Interface"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6">
                      <div className="bg-gray-900/80 backdrop-blur-md p-4 rounded-lg border border-gray-700">
                        <div className="flex items-center mb-3">
                          <div className="w-10 h-10 rounded-full bg-red-600 flex items-center justify-center mr-3">
                            <Bot className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="text-white font-bold">LunarBot</h3>
                            <p className="text-gray-300 text-xs">Online • Responding in 0.3s</p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="bg-gray-800/50 p-2 rounded-lg text-gray-300 text-sm">
                            How can I help you today?
                          </div>
                          <div className="bg-red-600/20 p-2 rounded-lg text-gray-200 text-sm">
                            I need information about your enterprise security solutions.
                          </div>
                          <div className="bg-gray-800/50 p-2 rounded-lg text-gray-300 text-sm">
                            I'd be happy to provide details about our enterprise security solutions. We offer
                            comprehensive protection including advanced threat detection, zero trust architecture, and
                            AI-powered monitoring. Would you like me to elaborate on any specific aspect?
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ThreeDCard>
              <div className="absolute -bottom-10 -right-10 z-10">
                <div className="bg-gray-900/80 backdrop-blur-lg p-4 rounded-lg border border-gray-800 shadow-xl">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-green-600 flex items-center justify-center mr-2">
                      <Check className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-white text-sm">99.9% Response Accuracy</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Bot Types Section */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1563986768609-322da13575f3?q=80&w=1920&auto=format&fit=crop"
            alt="Bot Types background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Bot Solutions
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Tailored Bot Solutions
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our custom bots are designed to meet the specific needs of your organization, whether you're looking to
              enhance customer service, streamline operations, or build community engagement.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Telegram Bot Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <ThreeDCard className="p-6 h-full">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full border border-gray-800">
                  <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-blue-600/80 to-blue-800/80 shadow-lg">
                    <Send className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white">Telegram Bots</h3>
                  <p className="text-gray-300 mb-6">
                    Custom Telegram bots that leverage the platform's robust API to create seamless, engaging
                    experiences for your users.
                  </p>

                  <h4 className="text-lg font-semibold text-white mb-4">Key Features:</h4>
                  <div className="space-y-4 mb-6">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Users className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Community Management</h5>
                        <p className="text-gray-300 text-sm">
                          Automated moderation, user onboarding, and engagement tools
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Bot className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">AI-Powered Conversations</h5>
                        <p className="text-gray-300 text-sm">
                          Natural language understanding for intelligent interactions
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Bell className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Notifications & Alerts</h5>
                        <p className="text-gray-300 text-sm">Customized alerts for important events and information</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Shield className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Enterprise-Grade Security</h5>
                        <p className="text-gray-300 text-sm">Secure authentication and data protection</p>
                      </div>
                    </div>
                  </div>

                  <Link
                    href="/contact"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors shadow-lg shadow-blue-600/20"
                  >
                    Discuss Your Telegram Bot
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </ThreeDCard>
            </motion.div>

            {/* Discord Bot Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <ThreeDCard className="p-6 h-full">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full border border-gray-800">
                  <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-indigo-600/80 to-indigo-800/80 shadow-lg">
                    <MessageSquare className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white">Discord Bots</h3>
                  <p className="text-gray-300 mb-6">
                    Feature-rich Discord bots designed to enhance server management, user engagement, and community
                    building.
                  </p>

                  <h4 className="text-lg font-semibold text-white mb-4">Key Features:</h4>
                  <div className="space-y-4 mb-6">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-indigo-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Settings className="h-4 w-4 text-indigo-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Server Management</h5>
                        <p className="text-gray-300 text-sm">
                          Role management, moderation tools, and administrative functions
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-indigo-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Layers className="h-4 w-4 text-indigo-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Custom Commands</h5>
                        <p className="text-gray-300 text-sm">
                          Tailored commands and interactions specific to your community
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-indigo-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Cpu className="h-4 w-4 text-indigo-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Integration Capabilities</h5>
                        <p className="text-gray-300 text-sm">
                          Connect with external services and APIs for enhanced functionality
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-indigo-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <FileText className="h-4 w-4 text-indigo-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Analytics & Reporting</h5>
                        <p className="text-gray-300 text-sm">Insights into user engagement and community activity</p>
                      </div>
                    </div>
                  </div>

                  <Link
                    href="/contact"
                    className="bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors shadow-lg shadow-indigo-600/20"
                  >
                    Discuss Your Discord Bot
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </ThreeDCard>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=1920&auto=format&fit=crop"
            alt="Use Cases background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Applications
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Strategic Use Cases
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover how our custom messaging bots can transform your operations and enhance your digital presence.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Customer Support",
                description:
                  "Automated support systems that handle common inquiries, freeing your team to focus on complex issues.",
                icon: <Users className="h-6 w-6 text-red-500" />,
                metrics: "Reduce response time by 85%",
              },
              {
                title: "Community Management",
                description:
                  "Engage and grow your community with interactive features, moderation tools, and personalized experiences.",
                icon: <MessageSquare className="h-6 w-6 text-red-500" />,
                metrics: "Increase engagement by 67%",
              },
              {
                title: "Internal Operations",
                description:
                  "Streamline workflows and automate routine tasks to enhance productivity and reduce operational costs.",
                icon: <Settings className="h-6 w-6 text-red-500" />,
                metrics: "Improve efficiency by 42%",
              },
              {
                title: "Marketing & Engagement",
                description:
                  "Create interactive campaigns and personalized experiences that drive user engagement and conversion.",
                icon: <Zap className="h-6 w-6 text-red-500" />,
                metrics: "Boost conversion rates by 35%",
              },
              {
                title: "Event Management",
                description:
                  "Simplify event coordination with automated reminders, registrations, and attendee communication.",
                icon: <Bell className="h-6 w-6 text-red-500" />,
                metrics: "Increase attendance by 28%",
              },
              {
                title: "Secure Communications",
                description: "Facilitate secure, encrypted communications for sensitive information and transactions.",
                icon: <Shield className="h-6 w-6 text-red-500" />,
                metrics: "99.9% security compliance",
              },
            ].map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <LunarGlowCard glowColor="red" intensity="low">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800 h-full">
                    <div className="w-12 h-12 rounded-xl mb-6 flex items-center justify-center bg-gradient-to-br from-red-600/30 to-red-800/30">
                      {useCase.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-white">{useCase.title}</h3>
                    <p className="text-gray-300 mb-4">{useCase.description}</p>
                    <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3">
                      <div className="flex items-center">
                        <Check className="h-5 w-5 text-green-500 mr-2" />
                        <span className="text-green-400 font-medium">{useCase.metrics}</span>
                      </div>
                    </div>
                  </div>
                </LunarGlowCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-gradient-to-r from-red-900/30 to-red-800/30 rounded-lg p-8 md:p-12 border border-red-800/50 backdrop-blur-sm">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Transform Your Digital Engagement?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how our custom messaging bots can elevate your brand's presence and streamline your
                  operations.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg shadow-lg shadow-red-600/20"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
