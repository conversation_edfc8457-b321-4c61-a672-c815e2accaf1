"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { ArrowRight, Check, Server, Cloud, Database, Lock, RefreshCw, Zap } from "lucide-react"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"

export default function CloudServicesPage() {
  return (
    <main className="pt-24 relative">
      <AnimatedBackground particleCount={50} className="opacity-30" />

      {/* Hero Section */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Enterprise Cloud Services</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Optimize Your Infrastructure with Cloud Solutions"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg mb-8">
              Transform your business with scalable, secure, and cost-effective cloud solutions designed for enterprise
              needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/contact"
                className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center transition-colors group"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
              <Link
                href="#cloud-solutions"
                className="border border-gray-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center hover:bg-gray-800 transition-colors"
              >
                Explore Cloud Solutions
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Cloud Solutions */}
      <section id="cloud-solutions" className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Our Solutions</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Comprehensive Cloud Solutions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We offer a full spectrum of cloud services designed to optimize your infrastructure and accelerate your
              digital transformation.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Cloud Migration",
                description:
                  "Seamlessly transition your applications and data to the cloud with minimal disruption to your business operations.",
                icon: <Cloud className="h-8 w-8" />,
                image: "https://images.unsplash.com/photo-1544197150-b99a580bb7a8?q=80&w=500&auto=format&fit=crop",
                features: [
                  "Comprehensive migration assessment",
                  "Workload optimization for cloud",
                  "Phased migration approach",
                  "Post-migration support",
                ],
              },
              {
                title: "Cloud Infrastructure Management",
                description:
                  "Optimize and manage your cloud infrastructure to ensure performance, security, and cost-efficiency.",
                icon: <Server className="h-8 w-8" />,
                image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?q=80&w=500&auto=format&fit=crop",
                features: [
                  "24/7 monitoring and management",
                  "Performance optimization",
                  "Cost optimization",
                  "Capacity planning",
                ],
              },
              {
                title: "Hybrid Cloud Solutions",
                description:
                  "Create a seamless integration between your on-premises infrastructure and cloud environments for maximum flexibility.",
                icon: <RefreshCw className="h-8 w-8" />,
                image: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=500&auto=format&fit=crop",
                features: [
                  "Unified management interface",
                  "Seamless data integration",
                  "Workload portability",
                  "Consistent security policies",
                ],
              },
              {
                title: "Cloud Security",
                description:
                  "Protect your cloud infrastructure and data with comprehensive security solutions designed for modern cloud environments.",
                icon: <Lock className="h-8 w-8" />,
                image: "https://images.unsplash.com/photo-1563986768609-322da13575f3?q=80&w=500&auto=format&fit=crop",
                features: [
                  "Cloud security posture management",
                  "Data protection and encryption",
                  "Identity and access management",
                  "Threat detection and response",
                ],
              },
              {
                title: "Cloud Database Services",
                description:
                  "Implement and manage scalable, high-performance database solutions in the cloud to support your applications.",
                icon: <Database className="h-8 w-8" />,
                image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?q=80&w=500&auto=format&fit=crop",
                features: [
                  "Database migration",
                  "Performance optimization",
                  "High availability configuration",
                  "Automated backup and recovery",
                ],
              },
              {
                title: "Cloud Cost Optimization",
                description:
                  "Maximize the value of your cloud investment with strategies to reduce costs while maintaining performance and security.",
                icon: <Zap className="h-8 w-8" />,
                image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=500&auto=format&fit=crop",
                features: [
                  "Cost analysis and reporting",
                  "Resource right-sizing",
                  "Reserved instance planning",
                  "Automated cost control policies",
                ],
              },
            ].map((solution, index) => (
              <motion.div
                key={solution.title}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-red-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image
                    src={solution.image || "/placeholder.svg"}
                    alt={solution.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 bg-red-600/80 w-12 h-12 rounded-full flex items-center justify-center text-white">
                    {solution.icon}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{solution.title}</h3>
                  <p className="text-gray-300 mb-6">{solution.description}</p>

                  <div className="bg-gray-800 p-4 rounded-lg mb-6">
                    <h4 className="text-white font-semibold mb-2">Key Features:</h4>
                    <ul className="space-y-2">
                      {solution.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <Check className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link
                    href={`/contact?solution=${encodeURIComponent(solution.title)}`}
                    className="text-red-400 inline-flex items-center font-medium hover:text-red-300 transition-colors"
                  >
                    Learn more
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Cloud Platforms */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Cloud Platforms We Support</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We have expertise across all major cloud platforms to provide you with the best solution for your specific
              needs.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Amazon Web Services (AWS)",
                logo: "https://images.unsplash.com/photo-1549605659-32d82da3a059?q=80&w=300&auto=format&fit=crop",
                description:
                  "Comprehensive cloud services platform offering compute power, storage, databases, machine learning, and more.",
              },
              {
                name: "Microsoft Azure",
                logo: "https://images.unsplash.com/photo-1633419461186-7d40a38105ec?q=80&w=300&auto=format&fit=crop",
                description:
                  "Integrated cloud services for computing, analytics, storage, and networking with seamless Microsoft integration.",
              },
              {
                name: "Google Cloud Platform",
                logo: "https://images.unsplash.com/photo-1573164713988-8665fc963095?q=80&w=300&auto=format&fit=crop",
                description:
                  "Suite of cloud computing services running on the same infrastructure that Google uses for its products.",
              },
            ].map((platform, index) => (
              <motion.div
                key={platform.name}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800 hover:border-red-500 transition-colors"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-white rounded-lg overflow-hidden mr-4">
                    <Image
                      src={platform.logo || "/placeholder.svg"}
                      alt={platform.name}
                      width={64}
                      height={64}
                      className="object-cover"
                    />
                  </div>
                  <h3 className="text-xl font-bold text-white">{platform.name}</h3>
                </div>
                <p className="text-gray-300">{platform.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Success Stories</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Cloud Implementation Case Studies</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover how our cloud solutions have helped organizations optimize their infrastructure and accelerate
              their digital transformation.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Secure Cloud Migration for Healthcare Provider",
                category: "Healthcare",
                image: "https://images.unsplash.com/photo-*************-2173dba999ef?q=80&w=600&auto=format&fit=crop",
                results: "99.99% uptime with zero security breaches while ensuring HIPAA compliance",
              },
              {
                title: "Hybrid Cloud Implementation for Financial Services",
                category: "Banking & Finance",
                image: "https://images.unsplash.com/photo-*************-2fceff292cdc?q=80&w=600&auto=format&fit=crop",
                results: "40% reduction in infrastructure costs and 60% faster application deployment",
              },
              {
                title: "Cloud-Based Data Platform for Retail Chain",
                category: "Retail",
                image: "https://images.unsplash.com/photo-*************-062f824d29cc?q=80&w=600&auto=format&fit=crop",
                results: "Real-time analytics capabilities and 75% improvement in data processing speed",
              },
            ].map((study, index) => (
              <motion.div
                key={study.title}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-red-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                  <div className="absolute top-0 left-0 bg-red-600 text-white text-xs px-3 py-1">{study.category}</div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{study.title}</h3>
                  <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3 mb-4">
                    <div className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-400 font-medium">{study.results}</span>
                    </div>
                  </div>
                  <Link
                    href="/case-studies"
                    className="text-red-400 inline-flex items-center font-medium hover:text-red-300 transition-colors"
                  >
                    Read case study
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/case-studies"
              className="bg-transparent border border-red-500 text-red-400 hover:bg-red-900/20 px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              View All Case Studies
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Benefits of Cloud Solutions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Cloud solutions deliver tangible benefits that drive business growth and competitive advantage.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Scalability & Flexibility",
                description:
                  "Easily scale resources up or down based on demand, ensuring optimal performance during peak periods and cost efficiency during slower times.",
              },
              {
                title: "Cost Optimization",
                description:
                  "Reduce capital expenditure on hardware and infrastructure while paying only for the resources you actually use.",
              },
              {
                title: "Enhanced Security",
                description:
                  "Leverage enterprise-grade security measures and compliance certifications provided by major cloud platforms.",
              },
              {
                title: "Business Continuity",
                description:
                  "Ensure your business can continue operating even in the face of disasters with robust backup and recovery solutions.",
              },
              {
                title: "Accelerated Innovation",
                description:
                  "Access cutting-edge technologies like AI, machine learning, and IoT without significant upfront investment.",
              },
              {
                title: "Global Reach",
                description:
                  "Deploy applications and services globally with minimal latency, providing better experiences for customers worldwide.",
              },
            ].map((benefit, index) => (
              <motion.div
                key={benefit.title}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-xl font-bold mb-3 text-white">{benefit.title}</h3>
                <p className="text-gray-300">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">FAQ</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Frequently Asked Questions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Get answers to common questions about our cloud solutions and implementation process.
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            {[
              {
                question: "How do I know if my organization is ready for cloud migration?",
                answer:
                  "Cloud readiness depends on several factors, including your current infrastructure, application architecture, data sensitivity, compliance requirements, and business objectives. We offer a comprehensive cloud readiness assessment that evaluates these factors and provides a roadmap for successful migration.",
              },
              {
                question: "What is the typical timeline for cloud migration?",
                answer:
                  "Migration timelines vary based on the complexity of your environment, the number of applications, data volume, and your organization's readiness. Simple migrations can be completed in weeks, while complex enterprise migrations may take several months. We use a phased approach to minimize disruption and deliver value incrementally.",
              },
              {
                question: "How do you ensure security and compliance in the cloud?",
                answer:
                  "We implement a comprehensive security framework that includes identity and access management, encryption, network security, monitoring, and compliance controls. We also leverage the security capabilities of major cloud providers and ensure alignment with industry standards and regulations relevant to your business.",
              },
              {
                question: "How do you help optimize cloud costs?",
                answer:
                  "Our cloud cost optimization approach includes right-sizing resources, reserved instance planning, spot instance utilization, storage optimization, and automated policies for resource management. We provide regular cost analysis reports and recommendations to ensure you're getting the maximum value from your cloud investment.",
              },
              {
                question: "Can we maintain a hybrid cloud environment?",
                answer:
                  "Yes, we specialize in designing and implementing hybrid cloud solutions that integrate your on-premises infrastructure with cloud environments. This approach allows you to maintain certain workloads locally while leveraging the cloud for others, providing flexibility, compliance, and optimal resource allocation.",
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800 mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-xl font-bold mb-3 text-white">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-red-900/30 to-red-800/30 rounded-lg p-8 md:p-12 border border-red-800/50">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Transform Your Infrastructure?
                </h2>
                <p className="text-gray-300 text-lg">
                  Contact us today to discuss how our cloud solutions can help you achieve your business objectives.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg whitespace-nowrap"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
