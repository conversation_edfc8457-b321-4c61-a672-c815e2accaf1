import <PERSON> from "next/link"
import { ArrowR<PERSON> } from "lucide-react"
import <PERSON><PERSON>ex<PERSON> from "@/components/animated-text"
import Three<PERSON><PERSON> from "@/components/3d-card"
import <PERSON><PERSON><PERSON>ground from "@/components/animated-background"

export default function CareersPage() {
  // Job listings data
  const jobListings = [
    {
      id: 1,
      title: "Senior AI Engineer",
      department: "Artificial Intelligence",
      location: "San Francisco, CA (Hybrid)",
      type: "Full-time",
      description:
        "We're looking for an experienced AI Engineer to join our team and help develop cutting-edge AI solutions for our clients.",
      requirements: [
        "5+ years of experience in machine learning and AI development",
        "Strong background in Python, TensorFlow, and PyTorch",
        "Experience with large language models and RAG systems",
        "Excellent problem-solving skills and attention to detail",
      ],
    },
    {
      id: 2,
      title: "Cybersecurity Analyst",
      department: "Security",
      location: "Remote",
      type: "Full-time",
      description:
        "Join our security team to help protect our clients' digital assets and implement robust security measures.",
      requirements: [
        "3+ years of experience in cybersecurity",
        "Knowledge of security frameworks and compliance standards",
        "Experience with security tools and vulnerability assessment",
        "Security certifications (CISSP, CEH, or equivalent) preferred",
      ],
    },
    {
      id: 3,
      title: "Frontend Developer",
      department: "Engineering",
      location: "San Francisco, CA (On-site)",
      type: "Full-time",
      description:
        "We're seeking a talented Frontend Developer to create stunning user interfaces and experiences for our clients.",
      requirements: [
        "3+ years of experience with React and Next.js",
        "Strong understanding of modern CSS and responsive design",
        "Experience with state management and API integration",
        "Eye for design and attention to detail",
      ],
    },
    {
      id: 4,
      title: "UX/UI Designer",
      department: "Design",
      location: "Remote",
      type: "Full-time",
      description:
        "Help us create beautiful, intuitive, and user-friendly interfaces for our clients' digital products.",
      requirements: [
        "3+ years of experience in UX/UI design",
        "Proficiency in design tools like Figma and Adobe Creative Suite",
        "Strong portfolio demonstrating user-centered design approach",
        "Experience with design systems and component libraries",
      ],
    },
    {
      id: 5,
      title: "Marketing Specialist",
      department: "Marketing",
      location: "Hybrid",
      type: "Full-time",
      description:
        "Join our marketing team to help promote our services and grow our client base through strategic marketing initiatives.",
      requirements: [
        "3+ years of experience in digital marketing",
        "Experience with content creation and social media management",
        "Knowledge of SEO and analytics tools",
        "Strong communication and writing skills",
      ],
    },
  ]

  return (
    <main className="pt-24 relative">
      <AnimatedBackground particleCount={50} className="opacity-30" />

      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Join Our Team"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              We're always looking for talented individuals to help us push the boundaries of what's possible.
            </p>
          </div>
        </div>
      </section>

      {/* Why Join Us */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-center text-white">Why Join Lunar Studio</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <ThreeDCard className="p-6">
              <div className="bg-gray-900/70 p-6 rounded-lg h-full">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center text-black mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                    <path d="M2 17l10 5 10-5"></path>
                    <path d="M2 12l10 5 10-5"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2 text-white">Cutting-Edge Projects</h3>
                <p className="text-gray-300">
                  Work on innovative projects using the latest technologies in AI, cybersecurity, and digital
                  transformation.
                </p>
              </div>
            </ThreeDCard>

            <ThreeDCard className="p-6">
              <div className="bg-gray-900/70 p-6 rounded-lg h-full">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center text-black mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2 text-white">Collaborative Culture</h3>
                <p className="text-gray-300">
                  Join a diverse team of experts who collaborate, share knowledge, and support each other's growth.
                </p>
              </div>
            </ThreeDCard>

            <ThreeDCard className="p-6">
              <div className="bg-gray-900/70 p-6 rounded-lg h-full">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center text-black mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="12" y1="20" x2="12" y2="10"></line>
                    <line x1="18" y1="20" x2="18" y2="4"></line>
                    <line x1="6" y1="20" x2="6" y2="16"></line>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2 text-white">Growth Opportunities</h3>
                <p className="text-gray-300">
                  Continuous learning, professional development, and clear career advancement paths for all team
                  members.
                </p>
              </div>
            </ThreeDCard>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            <ThreeDCard className="p-6">
              <div className="bg-gray-900/70 p-6 rounded-lg h-full">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center text-black mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2 text-white">Comprehensive Benefits</h3>
                <p className="text-gray-300">
                  Competitive salary, health insurance, retirement plans, flexible work arrangements, and generous paid
                  time off.
                </p>
              </div>
            </ThreeDCard>

            <ThreeDCard className="p-6">
              <div className="bg-gray-900/70 p-6 rounded-lg h-full">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center text-black mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 8v4l3 3"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2 text-white">Work-Life Balance</h3>
                <p className="text-gray-300">
                  We value your personal time and well-being, with flexible schedules and remote work options available
                  for many positions.
                </p>
              </div>
            </ThreeDCard>
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-center text-white">Open Positions</h2>

          <div className="space-y-6">
            {jobListings.map((job) => (
              <ThreeDCard key={job.id} className="p-6">
                <div className="bg-gray-900/70 p-6 rounded-lg">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl font-bold text-white">{job.title}</h3>
                      <p className="text-gray-400">
                        {job.department} • {job.location} • {job.type}
                      </p>
                    </div>
                    <Link
                      href={`/careers/${job.id}`}
                      className="mt-4 md:mt-0 bg-white text-black px-4 py-2 rounded-md font-medium inline-flex items-center hover:bg-gray-200 transition-colors"
                    >
                      Apply Now
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </div>

                  <p className="text-gray-300 mb-4">{job.description}</p>

                  <div>
                    <h4 className="font-semibold text-white mb-2">Requirements:</h4>
                    <ul className="list-disc list-inside text-gray-300 space-y-1">
                      {job.requirements.map((req, index) => (
                        <li key={index}>{req}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </ThreeDCard>
            ))}
          </div>

          <div className="mt-12 text-center">
            <p className="text-gray-300 mb-6">Don't see a position that matches your skills?</p>
            <Link
              href="/contact"
              className="bg-white text-black px-6 py-3 rounded-md font-medium inline-flex items-center hover:bg-gray-200 transition-colors"
            >
              Send Us Your Resume
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Employee Testimonials */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-center text-white">Life at Lunar Studio</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <ThreeDCard className="p-6">
              <div className="bg-gray-900/70 p-6 rounded-lg h-full">
                <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center text-black font-bold text-lg mb-4">
                  A
                </div>
                <p className="text-gray-300 italic mb-4">
                  "Working at Lunar Studio has been the highlight of my career. The collaborative environment and
                  cutting-edge projects keep me engaged and excited to come to work every day."
                </p>
                <p className="text-white font-semibold">Alex Chen</p>
                <p className="text-gray-400 text-sm">Senior AI Engineer, 3 years</p>
              </div>
            </ThreeDCard>

            <ThreeDCard className="p-6">
              <div className="bg-gray-900/70 p-6 rounded-lg h-full">
                <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center text-black font-bold text-lg mb-4">
                  M
                </div>
                <p className="text-gray-300 italic mb-4">
                  "The growth opportunities here are unmatched. I started as a junior developer and have been able to
                  advance my career while learning from some of the best minds in the industry."
                </p>
                <p className="text-white font-semibold">Maria Rodriguez</p>
                <p className="text-gray-400 text-sm">Lead Developer, 4 years</p>
              </div>
            </ThreeDCard>

            <ThreeDCard className="p-6">
              <div className="bg-gray-900/70 p-6 rounded-lg h-full">
                <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center text-black font-bold text-lg mb-4">
                  J
                </div>
                <p className="text-gray-300 italic mb-4">
                  "The work-life balance at Lunar Studio is refreshing. The company truly values its employees and
                  creates an environment where we can do our best work without burning out."
                </p>
                <p className="text-white font-semibold">James Wilson</p>
                <p className="text-gray-400 text-sm">UX Designer, 2 years</p>
              </div>
            </ThreeDCard>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-b from-gray-900 to-black text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <ThreeDCard className="p-8 max-w-3xl mx-auto">
            <div className="bg-gray-900/70 p-8 rounded-lg text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Join Our Team?</h2>
              <p className="text-gray-300 text-lg mb-8">
                We're looking for passionate individuals who want to make an impact and grow with us.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/careers#open-positions"
                  className="bg-white text-black px-8 py-3 rounded-md font-medium inline-flex items-center justify-center hover:bg-gray-200 transition-colors"
                >
                  View Open Positions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
                <Link
                  href="/contact"
                  className="border border-white text-white px-8 py-3 rounded-md font-medium inline-flex items-center justify-center hover:bg-white/10 transition-colors"
                >
                  Contact Us
                </Link>
              </div>
            </div>
          </ThreeDCard>
        </div>
      </section>
    </main>
  )
}
