"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { ArrowRight, type LucideIcon } from "lucide-react"

interface MobileServiceCardProps {
  title: string
  description: string
  icon: LucideIcon
  image?: string
  link: string
  index?: number
}

export default function MobileServiceCard({
  title,
  description,
  icon: Icon,
  image,
  link,
  index = 0,
}: MobileServiceCardProps) {
  const [isTapped, setIsTapped] = useState(false)

  const handleTap = () => {
    setIsTapped(true)
    setTimeout(() => setIsTapped(false), 300)
  }

  return (
    <Link href={link}>
      <motion.div
        className="relative bg-gray-900 rounded-xl overflow-hidden h-[180px] border border-gray-800 touch-active"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.05 }}
        viewport={{ once: true, margin: "-50px" }}
        onTap={handleTap}
        whileTap={{ scale: 0.98 }}
      >
        {/* Background image */}
        <div className="absolute inset-0 z-0">
          {image && (
            <Image
              src={image || "/placeholder.svg"}
              alt={title}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
              loading="lazy"
            />
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent z-10"></div>
        </div>

        {/* Content */}
        <div className="absolute inset-0 z-20 flex flex-col justify-end p-4">
          <div className="mb-3 bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 w-10 h-10 rounded-full flex items-center justify-center shadow-lg">
            <Icon className="h-5 w-5 text-white" />
          </div>
          <h3 className="text-lg font-bold mb-1 text-white">{title}</h3>
          <p className="text-gray-300 text-sm line-clamp-2 mb-2">{description}</p>
          <div className="flex items-center text-yellow-500 text-sm font-medium">
            Learn more
            <ArrowRight className="ml-1 h-4 w-4" />
          </div>
        </div>

        {/* Ripple effect on tap */}
        {isTapped && (
          <motion.div
            className="absolute inset-0 bg-yellow-500/10 z-30"
            initial={{ opacity: 0.5, scale: 0.8 }}
            animate={{ opacity: 0, scale: 1.2 }}
            transition={{ duration: 0.4 }}
          />
        )}
      </motion.div>
    </Link>
  )
}
