"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
  ArrowRight,
  Code,
  Shield,
  Bot,
  Zap,
  LineChart,
  Lock,
  Cpu,
  BarChart,
  Smartphone,
  Sparkles,
  Star,
  Database,
  FileText,
  Search,
  Server,
  MessageSquare,
  Send,
  Brain,
  Globe,
  Lightbulb,
  CreditCard,
  Layers,
  Cloud,
} from "lucide-react"
import AnimatedText from "@/components/animated-text"
import ThreeDCard from "@/components/3d-card"
import LunarGlowCard from "@/components/lunar-glow-card"

export default function ServicesPage() {
  // Service categories with their respective services
  const serviceCategories = [
    {
      id: "agentic-ai",
      title: "Agentic AI Systems",
      description:
        "Autonomous AI agents that operate with intelligence and purpose, transforming how your organization makes decisions and solves complex problems.",
      icon: <Bot className="h-8 w-8 text-yellow-500" />,
      color: "red",
      services: [
        {
          title: "Autonomous AI Agents",
          description:
            "AI systems that operate with a degree of autonomy to achieve goals, make decisions, and take actions with minimal human supervision.",
          icon: <Bot className="h-6 w-6 text-yellow-500" />,
          features: [
            "Goal-oriented autonomous systems",
            "Multi-agent collaboration frameworks",
            "Adaptive learning capabilities",
            "Human-in-the-loop oversight",
          ],
        },
        {
          title: "RAG-Based AI Solutions",
          description:
            "Retrieval-Augmented Generation systems that combine the power of large language models with your proprietary data for accurate, context-aware responses.",
          icon: <Database className="h-6 w-6 text-yellow-500" />,
          features: [
            "Knowledge base integration",
            "Context-aware responses",
            "Factual accuracy verification",
            "Domain-specific customization",
          ],
        },
        {
          title: "Intelligent Process Automation",
          description:
            "Transform your operations with AI-driven automation that enhances efficiency while maintaining security and compliance.",
          icon: <Cpu className="h-6 w-6 text-yellow-500" />,
          features: [
            "Process analysis and optimization",
            "Custom workflow design",
            "Integration with existing systems",
            "Continuous improvement mechanisms",
          ],
        },
      ],
    },
    {
      id: "conversational-ai",
      title: "Conversational AI & Bots",
      description:
        "Sophisticated AI-powered conversational interfaces that enhance customer experiences and streamline operations across multiple platforms.",
      icon: <MessageSquare className="h-8 w-8 text-yellow-500" />,
      color: "blue",
      services: [
        {
          title: "Custom GPTs & AI Assistants",
          description:
            "Tailored AI assistants and custom GPTs designed specifically for your organization's unique knowledge base and workflows.",
          icon: <Brain className="h-6 w-6 text-yellow-500" />,
          features: [
            "Proprietary knowledge integration",
            "Brand voice customization",
            "Specialized domain expertise",
            "Continuous learning capabilities",
          ],
        },
        {
          title: "Enterprise Chatbots",
          description:
            "Sophisticated conversational AI solutions that enhance customer experience and streamline internal operations.",
          icon: <MessageSquare className="h-6 w-6 text-yellow-500" />,
          features: [
            "Omnichannel deployment",
            "Natural language understanding",
            "Seamless human handoff",
            "Analytics and performance tracking",
          ],
        },
        {
          title: "Telegram & Discord Bots",
          description:
            "Custom messaging platform bots that automate workflows, enhance community engagement, and deliver personalized experiences.",
          icon: <Send className="h-6 w-6 text-yellow-500" />,
          features: [
            "Platform-specific optimizations",
            "Community management tools",
            "Interactive commands and features",
            "Moderation and security controls",
          ],
        },
      ],
    },
    {
      id: "enterprise-cybersecurity",
      title: "Enterprise Cybersecurity",
      description:
        "Comprehensive protection for your digital assets with advanced threat detection, prevention, and response capabilities.",
      icon: <Shield className="h-8 w-8 text-yellow-500" />,
      color: "purple",
      services: [
        {
          title: "Advanced Threat Protection",
          description:
            "Proactive security measures that identify and neutralize threats before they impact your business operations or data.",
          icon: <Shield className="h-6 w-6 text-yellow-500" />,
          features: [
            "24/7 security monitoring",
            "Threat intelligence integration",
            "Incident response planning",
            "Vulnerability management",
          ],
        },
        {
          title: "Zero Trust Architecture",
          description:
            "Implementation of security models that require verification for every person and device attempting to access resources in your network.",
          icon: <Lock className="h-6 w-6 text-yellow-500" />,
          features: [
            "Identity and access management",
            "Micro-segmentation",
            "Least privilege access",
            "Continuous monitoring and validation",
          ],
        },
        {
          title: "Security Compliance & Governance",
          description:
            "Ensure your organization meets industry standards and regulatory requirements for data protection and privacy.",
          icon: <FileText className="h-6 w-6 text-yellow-500" />,
          features: [
            "GDPR, HIPAA, and PCI DSS compliance",
            "Security policy development",
            "Regular compliance audits",
            "Staff security awareness training",
          ],
        },
      ],
    },
    {
      id: "ai-security-integration",
      title: "AI-Enhanced Security",
      description:
        "Cutting-edge solutions that combine artificial intelligence with cybersecurity to create more intelligent and adaptive protection systems.",
      icon: <Cpu className="h-8 w-8 text-yellow-500" />,
      color: "red",
      services: [
        {
          title: "AI-Powered Threat Intelligence",
          description:
            "Leverage artificial intelligence to identify patterns, predict threats, and enhance your security posture with advanced analytics.",
          icon: <Search className="h-6 w-6 text-yellow-500" />,
          features: [
            "Predictive threat modeling",
            "Behavioral analytics",
            "Anomaly detection",
            "Automated threat hunting",
          ],
        },
        {
          title: "Secure AI Development",
          description:
            "Implement security best practices throughout the AI development lifecycle to ensure your AI systems are robust against attacks.",
          icon: <Code className="h-6 w-6 text-yellow-500" />,
          features: [
            "Secure model development",
            "Adversarial testing",
            "Model vulnerability assessment",
            "AI ethics and governance",
          ],
        },
        {
          title: "Autonomous Security Response",
          description:
            "Deploy AI systems that can automatically detect, analyze, and respond to security incidents without human intervention.",
          icon: <Zap className="h-6 w-6 text-yellow-500" />,
          features: [
            "Automated incident response",
            "Self-healing systems",
            "Continuous security adaptation",
            "Rapid threat containment",
          ],
        },
      ],
    },
    {
      id: "web-app-development",
      title: "Web & App Development",
      description:
        "Sophisticated web applications and mobile experiences crafted with meticulous attention to detail and performance.",
      icon: <Code className="h-8 w-8 text-yellow-500" />,
      color: "emerald",
      services: [
        {
          title: "Luxury Web Applications",
          description:
            "Bespoke web applications with sophisticated interfaces, seamless performance, and enterprise-grade security.",
          icon: <Globe className="h-6 w-6 text-yellow-500" />,
          features: [
            "Custom frontend architecture",
            "Responsive design excellence",
            "Performance optimization",
            "Seamless third-party integrations",
          ],
        },
        {
          title: "Premium Mobile Experiences",
          description:
            "Native and cross-platform mobile applications that deliver exceptional user experiences across all devices.",
          icon: <Smartphone className="h-6 w-6 text-yellow-500" />,
          features: [
            "iOS and Android development",
            "Cross-platform solutions",
            "Offline functionality",
            "Biometric authentication",
          ],
        },
        {
          title: "Enterprise Web Platforms",
          description:
            "Scalable, secure web platforms designed for complex business requirements and high-volume traffic.",
          icon: <Server className="h-6 w-6 text-yellow-500" />,
          features: [
            "Microservices architecture",
            "Global content delivery",
            "Enterprise authentication",
            "High-availability infrastructure",
          ],
        },
      ],
    },
    {
      id: "mvp-development",
      title: "MVP Product Development",
      description:
        "Rapid development of elegant minimum viable products that validate your concept while maintaining premium quality.",
      icon: <BarChart className="h-8 w-8 text-yellow-500" />,
      color: "orange",
      services: [
        {
          title: "Concept Validation",
          description:
            "Rapidly transform your vision into a functional prototype that captures the essence of your product idea.",
          icon: <Lightbulb className="h-6 w-6 text-yellow-500" />,
          features: ["Rapid prototyping", "User testing frameworks", "Feedback integration", "Iterative refinement"],
        },
        {
          title: "Accelerated MVP Development",
          description:
            "Streamlined development process that delivers a market-ready product with core functionality in record time.",
          icon: <Zap className="h-6 w-6 text-yellow-500" />,
          features: [
            "Agile development methodology",
            "Core feature prioritization",
            "Scalable foundation",
            "Launch preparation",
          ],
        },
        {
          title: "Growth-Ready Architecture",
          description:
            "Technical foundations designed for seamless scaling as your product evolves and your user base grows.",
          icon: <LineChart className="h-6 w-6 text-yellow-500" />,
          features: [
            "Future-proof technology stack",
            "Modular component design",
            "Analytics integration",
            "Expansion planning",
          ],
        },
      ],
    },
    {
      id: "saas-development",
      title: "SaaS Product Development",
      description:
        "Bespoke software-as-a-service solutions built with scalability, security, and sophistication for discerning enterprises.",
      icon: <Cloud className="h-8 w-8 text-yellow-500" />,
      color: "indigo",
      services: [
        {
          title: "Enterprise SaaS Platforms",
          description:
            "Sophisticated cloud-based software solutions designed specifically for enterprise needs with scalability and security at the core.",
          icon: <Cloud className="h-6 w-6 text-yellow-500" />,
          features: [
            "Multi-tenant architecture",
            "Enterprise-grade security",
            "Seamless integration capabilities",
            "Global availability and scalability",
          ],
        },
        {
          title: "Custom Subscription Models",
          description:
            "Flexible and optimized subscription and monetization strategies tailored to your unique business model and customer base.",
          icon: <CreditCard className="h-6 w-6 text-yellow-500" />,
          features: [
            "Tiered subscription management",
            "Usage-based pricing models",
            "Secure payment processing",
            "Revenue optimization strategies",
          ],
        },
        {
          title: "White-Label SaaS Solutions",
          description:
            "Elegant white-label platforms that can be customized with your branding while maintaining the highest standards of quality.",
          icon: <Layers className="h-6 w-6 text-yellow-500" />,
          features: [
            "Comprehensive branding control",
            "Customizable user experience",
            "Robust admin capabilities",
            "Dedicated deployment options",
          ],
        },
      ],
    },
  ]

  return (
    <main className="pt-24 relative">
      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop"
            alt="Services header background"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Our Expertise
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="AI, Bots & Cybersecurity"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              Advanced AI systems, custom bots, and comprehensive security solutions for organizations that demand
              uncompromising protection and intelligence.
            </p>
          </div>
        </div>
      </section>

      {/* Service Categories Overview */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1920&auto=format&fit=crop"
            alt="Services overview background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {serviceCategories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Link href={`#${category.id}`}>
                  <LunarGlowCard glowColor="silver" intensity="medium">
                    <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full hover:bg-gray-800/70 transition-colors">
                      <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 shadow-lg">
                        {category.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-3 text-white">{category.title}</h3>
                      <p className="text-gray-300">{category.description}</p>
                      <div className="mt-4 flex items-center text-yellow-400">
                        <span>Learn more</span>
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </div>
                    </div>
                  </LunarGlowCard>
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Service Sections */}
      {serviceCategories.map((category, categoryIndex) => (
        <section
          key={category.id}
          id={category.id}
          className={`py-24 ${categoryIndex % 2 === 0 ? "bg-black" : "bg-gray-950"} relative overflow-hidden`}
        >
          <div className="absolute inset-0 opacity-10">
            <Image
              src={`https://images.unsplash.com/photo-${categoryIndex % 2 === 0 ? "1497366754035-f200968a6e72" : "1497366811353-6870744d04b2"}?q=80&w=1920&auto=format&fit=crop`}
              alt={`${category.title} background`}
              fill
              className="object-cover"
            />
          </div>
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.div
              className="mb-16 text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="inline-block">
                <motion.div
                  className="bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 rounded-full px-4 py-1 text-sm font-medium mb-4"
                  initial={{ opacity: 0, y: -20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <span className="text-yellow-300 flex items-center">
                    <Sparkles className="h-4 w-4 mr-2" />
                    {category.title}
                  </span>
                </motion.div>
              </div>
              <h2 className="text-3xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
                {category.title}
              </h2>
              <p className="text-gray-300 max-w-3xl mx-auto text-lg">{category.description}</p>
            </motion.div>

            {/* Visual Section */}
            <div className="mb-16">
              <ThreeDCard className="p-8">
                <div className="bg-gray-900/70 backdrop-blur-sm p-8 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
                    <div className="col-span-1 md:col-span-2">
                      <h3 className="text-2xl font-bold mb-4 text-white">
                        Transform Your Business with {category.title}
                      </h3>
                      <p className="text-gray-300 mb-6">
                        Our {category.title.toLowerCase()} solutions are designed to help discerning businesses elevate
                        their digital presence, improve operational efficiency, and create exceptional experiences for
                        their clients.
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="bg-white/10 p-4 rounded-lg">
                          <div className="flex items-center mb-2">
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 flex items-center justify-center mr-3 shadow-lg">
                              <Star className="h-4 w-4 text-white" />
                            </div>
                            <h4 className="font-semibold text-white">Unparalleled Quality</h4>
                          </div>
                          <p className="text-gray-300 text-sm">Meticulously crafted with attention to every detail</p>
                        </div>
                        <div className="bg-white/10 p-4 rounded-lg">
                          <div className="flex items-center mb-2">
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 flex items-center justify-center mr-3 shadow-lg">
                              <Sparkles className="h-4 w-4 text-white" />
                            </div>
                            <h4 className="font-semibold text-white">Bespoke Solutions</h4>
                          </div>
                          <p className="text-gray-300 text-sm">Tailored specifically to your unique requirements</p>
                        </div>
                      </div>
                    </div>
                    <div className="relative h-64 md:h-80">
                      <Image
                        src={`https://images.unsplash.com/photo-${categoryIndex % 2 === 0 ? "1551434678-e076c223a692" : "1507679799987-c73779587ccf"}?q=80&w=600&auto=format&fit=crop`}
                        alt={category.title}
                        fill
                        className="object-cover rounded-lg"
                      />
                    </div>
                  </div>
                </div>
              </ThreeDCard>
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {category.services.map((service, serviceIndex) => (
                <motion.div
                  key={serviceIndex}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: serviceIndex * 0.1 }}
                  viewport={{ once: true }}
                >
                  <ThreeDCard className="p-6 h-full">
                    <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full">
                      <div className="w-12 h-12 rounded-xl mb-6 flex items-center justify-center bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 shadow-lg">
                        {service.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-3 text-white">{service.title}</h3>
                      <p className="text-gray-300 mb-6">{service.description}</p>

                      <h4 className="text-sm font-semibold text-white mb-3">Key Features:</h4>
                      <ul className="space-y-2 mb-6">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start">
                            <span className="bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 rounded-full p-1 mr-3 mt-1 shadow-sm">
                              <svg className="h-2 w-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                                <circle cx="4" cy="4" r="3" />
                              </svg>
                            </span>
                            <span className="text-gray-300 text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </ThreeDCard>
                </motion.div>
              ))}
            </div>

            {/* CTA */}
            <motion.div
              className="mt-16 text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Link
                href={`/services/${category.id}`}
                className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors shadow-lg shadow-yellow-600/20"
              >
                Explore {category.title} Solutions
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </motion.div>
          </div>
        </section>
      ))}

      {/* Process Section */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1920&auto=format&fit=crop"
            alt="Process background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Approach
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              The Lunar Studio Process
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our meticulous approach ensures that every project we undertake meets our exacting standards of
              excellence.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                number: 1,
                title: "Discovery",
                description:
                  "We begin with a comprehensive exploration of your brand, objectives, and requirements to create a tailored solution.",
              },
              {
                number: 2,
                title: "Strategy",
                description:
                  "Our experts develop a detailed roadmap that outlines the approach, technologies, and timeline for your project.",
              },
              {
                number: 3,
                title: "Creation",
                description:
                  "Our skilled team brings your vision to life with meticulous attention to detail and craftsmanship.",
              },
              {
                number: 4,
                title: "Refinement",
                description:
                  "We continuously test, refine, and perfect your solution to ensure it meets our exacting standards.",
              },
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ThreeDCard className="p-6">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg text-center h-full">
                    <div className="bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 text-white rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4 shadow-lg">
                      {step.number}
                    </div>
                    <h3 className="text-xl font-semibold mb-2 text-white">{step.title}</h3>
                    <p className="text-gray-300">{step.description}</p>
                  </div>
                </ThreeDCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <ThreeDCard className="p-8 max-w-4xl mx-auto">
            <div className="bg-gray-900/70 backdrop-blur-sm p-8 rounded-lg text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Ready to Begin Your Journey?</h2>
              <p className="text-gray-300 text-lg mb-8">
                Contact us today to discuss how we can create a bespoke digital solution that elevates your brand and
                exceeds your expectations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-8 py-3 rounded-md font-medium inline-flex items-center justify-center transition-colors shadow-lg shadow-yellow-600/20"
                >
                  Schedule a Consultation
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
                <Link
                  href="/portfolio"
                  className="border border-gray-700 bg-gray-900/50 backdrop-blur-sm text-white px-8 py-3 rounded-md font-medium inline-flex items-center justify-center hover:bg-gray-800/50 transition-colors"
                >
                  View Our Portfolio
                </Link>
              </div>
            </div>
          </ThreeDCard>
        </div>
      </section>
    </main>
  )
}
