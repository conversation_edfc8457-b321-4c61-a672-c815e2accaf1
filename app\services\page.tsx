"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
  ArrowRight,
  Code,
  Shield,
  Bot,
  Zap,
  Smartphone,
  Sparkles,
  Database,
  MessageSquare,
  Brain,
  Globe,
  Lightbulb,
  Cloud,
  Layers,
  Target,
} from "lucide-react"

export default function ServicesPage() {
  // Main services data - simplified and professional
  const mainServices = [
    {
      title: "Mobile App Development",
      description: "We build high-performance mobile apps for iOS and Android, focusing on custom mobile application development and user engagement.",
      icon: <Smartphone className="h-12 w-12" />,
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?q=80&w=800&auto=format&fit=crop",
      link: "/services/web-app-development",
      features: ["iOS & Android Development", "Cross-platform Solutions", "UI/UX Design", "App Store Optimization"]
    },
    {
      title: "Website Development",
      description: "Custom web application development services designed for speed, security & SEO optimization, ensuring maximum online visibility & performance.",
      icon: <Code className="h-12 w-12" />,
      image: "https://images.unsplash.com/photo-1547658719-da2b51169166?q=80&w=800&auto=format&fit=crop",
      link: "/services/web-app-development",
      features: ["Custom Web Applications", "E-commerce Solutions", "CMS Development", "API Integration"]
    },
    {
      title: "AI Based Solutions",
      description: "Leverage enterprise AI solutions to fully automate business processes, improve decision-making & enhance customers interactions with cutting-edge systems.",
      icon: <Bot className="h-12 w-12" />,
      image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?q=80&w=800&auto=format&fit=crop",
      link: "/services/ai-solutions",
      features: ["Machine Learning Models", "Natural Language Processing", "Computer Vision", "Predictive Analytics"]
    },
    {
      title: "Digital Transformation",
      description: "Comprehensive digital transformation services to modernize your business processes and drive innovation across your organization.",
      icon: <Zap className="h-12 w-12" />,
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=800&auto=format&fit=crop",
      link: "/services/digital-transformation",
      features: ["Process Automation", "Cloud Migration", "Legacy System Modernization", "Digital Strategy"]
    }
  ]
  return (
    <main className="pt-24 bg-gray-950">
      {/* Hero Section */}
      <section className="py-24 bg-gray-950 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
              <Sparkles className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-400 text-sm font-medium">Tailored Services For Every Vision</span>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Our <span className="text-yellow-400">Services</span>
            </h1>

            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We deliver comprehensive software solutions designed to accelerate your business growth
              and digital transformation.
            </p>
          </motion.div>
        </div>
      </section>
      {/* Main Services */}
      <section className="py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {mainServices.map((service, index) => (
              <motion.div
                key={index}
                className="group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative rounded-2xl overflow-hidden mb-8">
                  <Image
                    src={service.image}
                    alt={service.title}
                    width={600}
                    height={300}
                    className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
                  <div className="absolute bottom-6 left-6">
                    <div className="w-16 h-16 bg-yellow-500/20 rounded-xl flex items-center justify-center text-yellow-400 mb-4">
                      {service.icon}
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white group-hover:text-yellow-400 transition-colors duration-300">
                    {service.title}
                  </h3>

                  <p className="text-gray-300 leading-relaxed">
                    {service.description}
                  </p>

                  <div className="space-y-2">
                    {service.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center text-gray-400">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link
                    href={service.link}
                    className="inline-flex items-center text-yellow-400 hover:text-yellow-300 font-medium group/link"
                  >
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/link:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Let's discuss how our services can help transform your business and drive growth.
            </p>

            <Link
              href="/contact"
              className="inline-flex items-center justify-center px-10 py-5 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-bold text-lg rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-yellow-500/25"
            >
              Start Your Project
              <ArrowRight className="ml-3 h-6 w-6" />
            </Link>
          </motion.div>
        </div>
      </section>
    </main>
  )
}
