"use client"

import { useState } from "react"
import { Zap, Users, Setting<PERSON>, Clock, Shield, Globe, type LucideIcon } from "lucide-react"
import { motion } from "framer-motion"

interface FeatureCardProps {
  title: string
  description: string
  icon: string
  index?: number
}

export default function FeatureCard({ title, description, icon, index = 0 }: FeatureCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const getIcon = (): LucideIcon => {
    switch (icon) {
      case "Zap":
        return Zap
      case "Users":
        return Users
      case "Settings":
        return Settings
      case "Clock":
        return Clock
      case "Shield":
        return Shield
      case "Globe":
        return Globe
      default:
        return Zap
    }
  }

  const Icon = getIcon()

  return (
    <motion.div
      className="relative bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10 overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div
        className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 transition-opacity duration-300"
        style={{ opacity: isHovered ? 0.1 : 0 }}
      />

      <motion.div
        className="text-white mb-4 relative z-10"
        animate={{
          scale: isHovered ? 1.1 : 1,
          y: isHovered ? -5 : 0,
        }}
        transition={{ duration: 0.3 }}
      >
        <Icon className="h-8 w-8" />
      </motion.div>

      <h3 className="text-xl font-semibold mb-2 text-white relative z-10">{title}</h3>
      <p className="text-gray-400 relative z-10">{description}</p>

      <motion.div
        className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-yellow-500 to-purple-500"
        initial={{ scaleX: 0 }}
        animate={{ scaleX: isHovered ? 1 : 0 }}
        transition={{ duration: 0.3 }}
        style={{ transformOrigin: "left" }}
      />
    </motion.div>
  )
}
