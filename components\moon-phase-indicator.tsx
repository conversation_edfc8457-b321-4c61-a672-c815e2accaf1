"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"

interface MoonPhaseIndicatorProps {
  className?: string
  size?: "sm" | "md" | "lg"
}

export default function MoonPhaseIndicator({ className = "", size = "md" }: MoonPhaseIndicatorProps) {
  const [phase, setPhase] = useState(0)
  const [phaseName, setPhaseName] = useState("Full Moon")

  // Calculate current moon phase
  useEffect(() => {
    // Get real moon phase data (simplified calculation)
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    const day = now.getDate()

    // Simple algorithm to approximate moon phase (0-29.5)
    // This is a simplified version - real calculations are more complex
    const phase = ((year - 2000) % 19) * 11 + month + day
    const normalizedPhase = phase % 29.5

    // Convert to 0-1 range
    const phaseNormalized = normalizedPhase / 29.5
    setPhase(phaseNormalized)

    // Set phase name
    if (phaseNormalized < 0.07 || phaseNormalized > 0.93) {
      setPhaseName("New Moon")
    } else if (phaseNormalized < 0.18) {
      setPhaseName("Waxing Crescent")
    } else if (phaseNormalized < 0.32) {
      setPhaseName("First Quarter")
    } else if (phaseNormalized < 0.43) {
      setPhaseName("Waxing Gibbous")
    } else if (phaseNormalized < 0.57) {
      setPhaseName("Full Moon")
    } else if (phaseNormalized < 0.68) {
      setPhaseName("Waning Gibbous")
    } else if (phaseNormalized < 0.82) {
      setPhaseName("Last Quarter")
    } else {
      setPhaseName("Waning Crescent")
    }
  }, [])

  // Determine size dimensions
  const dimensions = {
    sm: { size: 24, textSize: "text-xs" },
    md: { size: 32, textSize: "text-sm" },
    lg: { size: 48, textSize: "text-base" },
  }

  const { size: moonSize, textSize } = dimensions[size]

  // Calculate shadow position based on phase
  const getShadowStyle = () => {
    // Phase 0 = new moon, 0.5 = full moon, 1 = new moon
    const shadowDirection = phase < 0.5 ? -1 : 1
    const phaseOffset = Math.abs(phase - 0.5) * 2 // 0 to 1

    return {
      transform: `translateX(${shadowDirection * phaseOffset * moonSize}px)`,
      opacity: 0.95,
    }
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="relative">
        <motion.div
          className="rounded-full bg-gradient-to-br from-gray-200 to-white"
          style={{
            width: moonSize,
            height: moonSize,
            boxShadow: `0 0 ${moonSize / 4}px rgba(200, 200, 255, 0.5)`,
          }}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        />

        {/* Moon shadow */}
        <motion.div
          className="absolute top-0 rounded-full bg-[#0a0a20]"
          style={{
            width: moonSize,
            height: moonSize,
            ...getShadowStyle(),
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.95 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        />
      </div>

      <motion.div
        initial={{ opacity: 0, x: -5 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <p className={`${textSize} font-medium text-gray-300`}>{phaseName}</p>
      </motion.div>
    </div>
  )
}
