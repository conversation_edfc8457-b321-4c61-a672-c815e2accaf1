"use client"

import type React from "react"

import { useState, useRef } from "react"
import { motion, useMotionValue, useSpring, useTransform } from "framer-motion"
import Link from "next/link"
import {
  Brain,
  Shield,
  Code,
  BarChart,
  Smartphone,
  Database,
  LineChart,
  Lock,
  Bot,
  Network,
  Cpu,
  Zap,
  ArrowRight,
  type LucideIcon,
} from "lucide-react"

interface AdvancedServiceCardProps {
  title: string
  description: string
  icon: string
  slug: string
  features: string[]
  index?: number
  color?: string
}

export default function AdvancedServiceCard({
  title,
  description,
  icon,
  slug,
  features,
  index = 0,
  color = "blue",
}: AdvancedServiceCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  // Mouse position for 3D effect
  const x = useMotionValue(0)
  const y = useMotionValue(0)

  // Spring animations for smoother movement
  const rotateX = useSpring(useTransform(y, [-100, 100], [10, -10]), { stiffness: 200, damping: 25 })
  const rotateY = useSpring(useTransform(x, [-100, 100], [-10, 10]), { stiffness: 200, damping: 25 })

  // Handle mouse move for 3D effect
  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = cardRef.current?.getBoundingClientRect()
    if (!rect) return

    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    x.set(e.clientX - centerX)
    y.set(e.clientY - centerY)
  }

  // Reset on mouse leave
  const handleMouseLeave = () => {
    setIsHovered(false)
    x.set(0)
    y.set(0)
  }

  // Get the right gradient based on color
  const getGradient = () => {
    const gradients = {
      blue: "from-blue-600 to-cyan-500",
      purple: "from-purple-600 to-indigo-500",
      rose: "from-yellow-500 to-amber-600",
      amber: "from-amber-500 to-orange-600",
      emerald: "from-emerald-500 to-teal-600",
      indigo: "from-indigo-600 to-blue-500",
    }

    return gradients[color as keyof typeof gradients] || gradients.blue
  }

  // Update color classes to black and white
  const colorClasses = {
    blue: "bg-gradient-to-br from-white to-gray-300",
    purple: "bg-gradient-to-br from-white to-gray-300",
    rose: "bg-gradient-to-br from-white to-gray-300",
    amber: "bg-gradient-to-br from-white to-gray-300",
    emerald: "bg-gradient-to-br from-white to-gray-300",
    indigo: "bg-gradient-to-br from-white to-gray-300",
  }

  const glassBg = colorClasses[color as keyof typeof colorClasses] || colorClasses.blue

  // Icon mapping
  const getIcon = (): LucideIcon => {
    switch (icon) {
      case "Brain":
        return Brain
      case "Shield":
        return Shield
      case "Code":
        return Code
      case "BarChart":
        return BarChart
      case "Smartphone":
        return Smartphone
      case "Database":
        return Database
      case "LineChart":
        return LineChart
      case "Lock":
        return Lock
      case "Bot":
        return Bot
      case "Network":
        return Network
      case "Cpu":
        return Cpu
      case "Zap":
        return Zap
      default:
        return Brain
    }
  }

  const Icon = getIcon()

  return (
    <motion.div
      ref={cardRef}
      className="relative w-full h-full min-h-[420px] rounded-2xl overflow-hidden"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      style={{
        rotateX,
        rotateY,
        transformStyle: "preserve-3d",
      }}
    >
      {/* Gradient animated bg */}
      <div
        className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${getGradient()} opacity-10 transition-opacity duration-300 z-0`}
        style={{ opacity: isHovered ? 0.2 : 0.1 }}
      />

      {/* Card content */}
      <div
        className="relative z-10 h-full flex flex-col backdrop-blur-sm bg-white/90 dark:bg-black/70 p-8 rounded-2xl border border-gray-200 dark:border-gray-800 shadow-lg transition-all duration-300"
        style={{
          transform: "translateZ(20px)",
          boxShadow: isHovered ? "0 20px 40px rgba(0,0,0,0.1)" : "0 10px 30px rgba(0,0,0,0.05)",
        }}
      >
        {/* Icon */}
        <div style={{ transform: "translateZ(40px)" }}>
          <motion.div
            className={`w-16 h-16 rounded-2xl mb-6 flex items-center justify-center ${
              colorClasses[color as keyof typeof colorClasses]
            } text-white`}
            animate={{
              scale: isHovered ? 1.1 : 1,
              y: isHovered ? -5 : 0,
            }}
            transition={{ type: "spring", stiffness: 300, damping: 15 }}
          >
            <Icon className="h-8 w-8" />
          </motion.div>
        </div>

        {/* Title & Description */}
        <div style={{ transform: "translateZ(30px)" }}>
          <h3 className="text-xl font-bold mb-3 transition-colors duration-300 dark:text-white">{title}</h3>

          <p className="text-gray-600 dark:text-gray-300 mb-6 flex-grow">{description}</p>
        </div>

        {/* Features */}
        <div className="my-4 space-y-2" style={{ transform: "translateZ(30px)" }}>
          {features.map((feature, idx) => (
            <div key={idx} className="flex items-start">
              <div
                className={`w-2 h-2 mt-2 rounded-full ${colorClasses[color as keyof typeof colorClasses].replace(
                  "bg-gradient-to-br",
                  "bg",
                )} mr-2 flex-shrink-0`}
              />
              <span className="text-sm text-gray-500 dark:text-gray-400">{feature}</span>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="mt-auto pt-4" style={{ transform: "translateZ(40px)" }}>
          <Link
            href={`/services/${slug}`}
            className={`group inline-flex items-center text-sm font-medium transition-all duration-300 ${
              isHovered ? "text-black bg-white pl-4 pr-3 py-2 rounded-lg" : "text-gray-300"
            }`}
            style={{
              background: isHovered ? `white` : "transparent",
            }}
          >
            <span>Learn more</span>
            <motion.div animate={{ x: isHovered ? 5 : 0 }} transition={{ duration: 0.3 }}>
              <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300" />
            </motion.div>
          </Link>
        </div>

        {/* Corner accents */}
        <div className="absolute top-0 right-0 w-20 h-20 overflow-hidden">
          <div
            className={`absolute top-0 right-0 w-[100px] h-[100px] ${colorClasses[
              color as keyof typeof colorClasses
            ].replace(
              "bg-gradient-to-br",
              "bg",
            )} opacity-20 transform rotate-45 translate-x-1/2 -translate-y-1/2 transition-opacity duration-300`}
            style={{ opacity: isHovered ? 0.3 : 0.1 }}
          />
        </div>

        <div className="absolute bottom-0 left-0 w-16 h-16 overflow-hidden">
          <div
            className={`absolute bottom-0 left-0 w-[80px] h-[80px] ${colorClasses[
              color as keyof typeof colorClasses
            ].replace(
              "bg-gradient-to-br",
              "bg",
            )} opacity-10 transform rotate-45 -translate-x-1/2 translate-y-1/2 transition-opacity duration-300`}
            style={{ opacity: isHovered ? 0.2 : 0.05 }}
          />
        </div>
      </div>
    </motion.div>
  )
}
