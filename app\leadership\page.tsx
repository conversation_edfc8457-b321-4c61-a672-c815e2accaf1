"use client"

import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { ArrowRight, Linkedin, Twitter, Mail, Brain, Shield, Cloud, Search, Zap, Settings, RefreshCw } from "lucide-react"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"

export default function LeadershipPage() {
  // Leadership team data
  const executiveTeam = [
    {
      name: "<PERSON><PERSON>",
      position: "Chief Executive Officer (CEO), Founder",
      image:
        "/usman.jpg",
      bio: "As CEO and Founder, <PERSON><PERSON> leads our company vision and strategic direction. His background in enterprise security architecture and business leadership has positioned Lunar Studio as a trusted partner for organizations seeking cutting-edge technology solutions.",
      linkedin: "https://www.linkedin.com/in/muhammadusman18/",
      email: "<EMAIL>",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      position: "Chief Information Security Officer (CISO), Founder",
      image:
        "blank.png",
      bio: "<PERSON><PERSON><PERSON> oversees our cybersecurity practice and ensures the highest security standards across all our solutions. His innovative approach to security architecture has redefined how our clients protect their most valuable digital assets.",
      linkedin: "https://www.linkedin.com/in/huzaifah-tahir-25623a223/",
      email: "<EMAIL>",
    },
    {
      name: "<PERSON><PERSON> <PERSON>",
      position: "Chief Technology Officer (CTO), Co-Founder",
      image:"badar.png",
      bio: "Badar leads our technical strategy and innovation initiatives as CTO. With extensive experience in AI and machine learning, he ensures Lunar Studio stays at the cutting edge of technology while delivering robust, scalable solutions for our clients.",
      linkedin: "https://www.linkedin.com/in/badar-abbas/",
      email: "<EMAIL>",
    },
    {
      name: "Mariyam Zaman",
      position: "Chief Growth Officer (CGO), Co-Founder",
      image:
        "blank.png",
      bio: "Mariyam drives our business expansion and market development strategies. With her expertise in AI Engineering and Python-based Automation, she has been instrumental in developing Lunar's core technologies and creating scalable growth frameworks.",
      linkedin: "https://www.linkedin.com/in/mariyam-zaman/",
      email: "<EMAIL>",
    },
  ]

  // Remove the leadershipTeam array and section

  return (
    <main className="pt-24 relative">
      <AnimatedBackground particleCount={50} className="opacity-30" />

      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Our Team</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Leadership Team"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              Meet the visionaries and experts who drive our innovation and success.
            </p>
          </div>
        </div>
      </section>

      {/* Executive Leadership */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Executive Leadership</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our executive team brings decades of experience in technology, business strategy, and industry expertise
              to guide our company's vision and growth.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {executiveTeam.map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-red-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-80">
                  <Image src={member.image || "/placeholder.svg"} alt={member.name} fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-70"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <h3 className="text-xl font-bold text-white">{member.name}</h3>
                    <p className="text-red-400">{member.position}</p>
                  </div>
                </div>
                <div className="p-6">
                  <p className="text-gray-300 mb-6">{member.bio}</p>
                  <div className="flex space-x-4">
                    <a href={member.linkedin} className="text-gray-400 hover:text-white transition-colors">
                      <Linkedin className="h-5 w-5" />
                    </a>
                    <a href={member.twitter} className="text-gray-400 hover:text-white transition-colors">
                      <Twitter className="h-5 w-5" />
                    </a>
                    <a href={`mailto:${member.email}`} className="text-gray-400 hover:text-white transition-colors">
                      <Mail className="h-5 w-5" />
                    </a>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Centers of Excellence */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Excellence Redefined</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Centers of Excellence</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our specialized divisions drive innovation and excellence across key technological domains.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "AI Research Lab",
                description: "Pioneering breakthrough AI technologies and algorithms that shape the future of intelligent systems.",
                icon: <Brain className="h-8 w-8 text-red-400" />,
              },
              {
                title: "Cybersecurity Command Center",
                description: "Advanced threat detection and prevention systems protecting enterprise-grade digital assets.",
                icon: <Shield className="h-8 w-8 text-red-400" />,
              },
              {
                title: "Cloud Architecture Hub",
                description: "Designing scalable, high-performance cloud solutions for mission-critical applications.",
                icon: <Cloud className="h-8 w-8 text-red-400" />,
              },
            ].map((center, index) => (
              <motion.div
                key={center.title}
                className="bg-gray-900 p-8 rounded-lg border border-gray-800 hover:border-red-500 transition-colors"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="bg-red-900/30 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                  {center.icon}
                </div>
                <h3 className="text-xl font-bold mb-4 text-white">{center.title}</h3>
                <p className="text-gray-300">{center.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Global Excellence */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Global Impact</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Excellence Across Borders</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our global presence enables us to deliver exceptional solutions while maintaining the highest standards of quality and innovation.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                metric: "15+",
                label: "Countries Served",
              },
              {
                metric: "99.99%",
                label: "System Reliability",
              },
              {
                metric: "24/7",
                label: "Expert Support",
              },
              {
                metric: "100+",
                label: "Enterprise Clients",
              },
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="bg-gray-900 p-8 rounded-lg border border-gray-800 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-3xl font-bold text-red-400 mb-2">{stat.metric}</div>
                <div className="text-gray-300">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Innovation Framework */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Innovation Process</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Our Innovation Framework</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              A systematic approach to turning visionary ideas into groundbreaking solutions.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                title: "Research & Discovery",
                description: "Deep analysis of market trends and emerging technologies to identify opportunities for innovation.",
                icon: <Search className="h-8 w-8 text-red-400" />,
              },
              {
                title: "Rapid Prototyping",
                description: "Swift transformation of concepts into functional prototypes using cutting-edge development methodologies.",
                icon: <Zap className="h-8 w-8 text-red-400" />,
              },
              {
                title: "Enterprise Integration",
                description: "Seamless implementation of solutions within existing enterprise ecosystems.",
                icon: <Settings className="h-8 w-8 text-red-400" />,
              },
              {
                title: "Continuous Evolution",
                description: "Ongoing refinement and enhancement of solutions to maintain technological leadership.",
                icon: <RefreshCw className="h-8 w-8 text-red-400" />,
              },
            ].map((process, index) => (
              <motion.div
                key={process.title}
                className="bg-gray-900 p-8 rounded-lg border border-gray-800 hover:border-red-500 transition-colors"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="bg-red-900/30 w-16 h-16 rounded-lg flex items-center justify-center mb-6">
                  {process.icon}
                </div>
                <h3 className="text-xl font-bold mb-4 text-white">{process.title}</h3>
                <p className="text-gray-300">{process.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Values */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Our Values</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Guiding Principles</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our leadership team is guided by a set of core values that define our culture and approach to business.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: "Innovation",
                description:
                  "We constantly push the boundaries of what's possible, embracing new technologies and approaches to solve complex challenges.",
              },
              {
                title: "Excellence",
                description:
                  "We are committed to delivering the highest quality solutions and services, exceeding client expectations at every opportunity.",
              },
              {
                title: "Integrity",
                description:
                  "We operate with transparency, honesty, and ethical standards in all our interactions with clients, partners, and team members.",
              },
              {
                title: "Client Success",
                description:
                  "We measure our success by the success of our clients, focusing on delivering outcomes that drive real business value.",
              },
            ].map((value, index) => (
              <motion.div
                key={value.title}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-xl font-bold mb-3 text-white">{value.title}</h3>
                <p className="text-gray-300">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Join Our Team */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-red-900/30 to-red-800/30 rounded-lg p-8 md:p-12 border border-red-800/50">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Join Our Team</h2>
                <p className="text-gray-300 text-lg">
                  We're always looking for talented individuals to join our team and help us push the boundaries of
                  what's possible.
                </p>
              </div>
              <Link
                href="/careers"
                className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg whitespace-nowrap"
              >
                View Open Positions
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}


