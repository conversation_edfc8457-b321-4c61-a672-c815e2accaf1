"use client"

import { useEffect, useRef, useState } from "react"
import lottie from "lottie-web"

interface LottieAnimationProps {
  animationData: any
  className?: string
  loop?: boolean
  autoplay?: boolean
  speed?: number
  direction?: 1 | -1
  hover?: boolean
  onClick?: () => void
}

export default function LottieAnimation({
  animationData,
  className = "",
  loop = true,
  autoplay = true,
  speed = 1,
  direction = 1,
  hover = false,
  onClick,
}: LottieAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<any>(null)
  const [isHovered, setIsHovered] = useState(false)

  useEffect(() => {
    if (!containerRef.current) return

    // Initialize lottie animation
    animationRef.current = lottie.loadAnimation({
      container: containerRef.current,
      renderer: "svg",
      loop,
      autoplay: !hover && autoplay,
      animationData,
    })

    // Set animation speed
    animationRef.current.setSpeed(speed)

    // Set animation direction
    animationRef.current.setDirection(direction)

    // Cleanup
    return () => {
      if (animationRef.current) {
        animationRef.current.destroy()
      }
    }
  }, [animationData, loop, autoplay, speed, direction, hover])

  // Handle hover state
  useEffect(() => {
    if (!animationRef.current || !hover) return

    if (isHovered) {
      animationRef.current.play()
    } else {
      animationRef.current.stop()
    }
  }, [isHovered, hover])

  // Handle mouse events
  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  return (
    <div
      ref={containerRef}
      className={className}
      onMouseEnter={hover ? handleMouseEnter : undefined}
      onMouseLeave={hover ? handleMouseLeave : undefined}
      onClick={onClick}
      style={{ cursor: onClick ? "pointer" : "default" }}
    />
  )
}
