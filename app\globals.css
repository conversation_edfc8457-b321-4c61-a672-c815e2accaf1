@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
  -webkit-tap-highlight-color: transparent;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
  .animation-delay-500 {
    animation-delay: 0.5s;
  }
  .animation-delay-1000 {
    animation-delay: 1s;
  }

  .animate-slow-pulse {
    animation: slowPulse 6s infinite;
  }

  @keyframes slowPulse {
    0%,
    100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.5;
      transform: scale(1.05);
    }
  }

  .pb-safe {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }

  .pt-safe {
    padding-top: env(safe-area-inset-top, 0);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Page transitions */
.page-transition-enter {
  opacity: 0;
}
.page-transition-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}
.page-transition-exit {
  opacity: 1;
}
.page-transition-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

/* Mobile app-like styles */
@media (max-width: 767px) {
  /* Disable pull-to-refresh on iOS */
  html,
  body {
    overscroll-behavior-y: contain;
  }

  /* Add momentum scrolling */
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    overflow-y: scroll;
  }

  /* Improve tap target sizes */
  button,
  a {
    min-height: 44px;
    min-width: 44px;
  }

  /* Add active state for touch */
  .touch-active:active {
    opacity: 0.7;
  }

  /* Hide scrollbars on mobile */
  ::-webkit-scrollbar {
    display: none;
  }

  /* Status bar area */
  .status-bar-area {
    padding-top: env(safe-area-inset-top, 20px);
  }
}

/* Fix for white spaces */
html,
body {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  background-color: #000;
}

#__next {
  width: 100%;
  background-color: #000;
}

section {
  width: 100%;
}
