"use client"

import Image from "next/image"
import Link from "next/link"
import { ArrowRight, Star, Users, Lightbulb, Shield, Brain, Crosshair } from "lucide-react"
import { motion } from "framer-motion"
import MoonPhaseIndicator from "@/components/moon-phase-indicator"
import LunarGlowCard from "@/components/lunar-glow-card"

export default function AboutPage() {
  // Team members data
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "Chief Executive Officer (CEO), Founder",
      bio: "As CEO and Founder, <PERSON><PERSON> leads our company vision and strategic direction. Her innovative approach to leadership and deep expertise in AI Engineering and Python-based Automation has positioned Lunar A.i Studio as a trusted partner for organizations seeking cutting-edge technology solutions.",
      image: "/maryum.webp",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Chief Technology Officer (CTO), Founder",
      bio: "<PERSON><PERSON> leads our technical strategy and innovation initiatives as CTO and Founder. With extensive experience in AI and machine learning, he ensures Lunar A.i Studio stays at the cutting edge of technology while delivering robust, scalable solutions for our clients.",
      image: "/badar.png",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Chief Strategy Officer (CSO), Founder",
      bio: "<PERSON><PERSON> drives our strategic initiatives and long-term vision. His background in enterprise security architecture and business strategy ensures Lunar A.i Studio stays ahead of market trends while delivering exceptional value to our clients.",
      image: "/usman.jpg",
    },
  ]

  // Company values
  const companyValues = [
    {
      title: "Innovation",
      description:
        "We constantly push the boundaries of what's possible in AI and cybersecurity to stay ahead of emerging threats.",
      icon: <Lightbulb className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Security",
      description: "We implement uncompromising protection measures that safeguard your most valuable digital assets.",
      icon: <Shield className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Intelligence",
      description:
        "We harness the power of advanced AI to create systems that learn, adapt, and deliver exceptional results.",
      icon: <Brain className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Precision",
      description:
        "We deliver meticulously engineered solutions with attention to every technical detail and security consideration.",
      icon: <Crosshair className="h-6 w-6 text-yellow-500" />,
    },
  ]

  return (
    <main className="pt-24">
      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop"
            alt="About header background"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <MoonPhaseIndicator className="mx-auto mb-6" size="lg" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About Lunar A.i Studio</h1>
            <p className="text-gray-300 text-lg">
              We are a specialized firm dedicated to advancing the frontiers of Agentic AI and Cybersecurity for
              discerning enterprises.
            </p>
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?q=80&w=1920&auto=format&fit=crop"
            alt="Team background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Users className="h-4 w-4 mr-2" />
                Our Team
              </span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Meet Our Experts</h2>
            <p className="text-gray-300">
              Our team of specialists brings together decades of experience in luxury software development.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {teamMembers.map((member, index) => (
              <LunarGlowCard key={index} glowColor="silver" intensity="medium">
                <div className="bg-gray-900/70 backdrop-blur-sm rounded-lg overflow-hidden h-full">
                  <div className="relative h-64">
                    <Image src={member.image || "/placeholder.svg"} alt={member.name} fill className="object-cover" />
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-1 text-white">{member.name}</h3>
                    <p className="text-yellow-400 mb-4">{member.role}</p>
                    <p className="text-gray-300 text-sm mb-4">{member.bio}</p>
                    
                    {/* 24/7 Availability Badge */}
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-green-400 text-xs font-medium">Available 24/7</span>
                    </div>
                    
                    {/* Schedule Meeting Button */}
                    <Link
                      href="/contact"
                      className="inline-flex items-center justify-center w-full bg-gradient-to-r from-yellow-600 to-yellow-700 text-white px-4 py-2 rounded-lg font-medium hover:from-yellow-700 hover:to-yellow-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-yellow-900/20"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Schedule Meeting
                    </Link>
                  </div>
                </div>
              </LunarGlowCard>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/leadership" className="inline-flex items-center text-white font-medium hover:underline">
              View Full Team
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-5">
          <Image
            src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1920&auto=format&fit=crop"
            alt="Our story background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
                <span className="text-yellow-300 text-sm font-medium flex items-center">
                  <Star className="h-4 w-4 mr-2" />
                  Our Story
                </span>
              </div>
              <h2 className="text-3xl font-bold mb-6 text-white">A Legacy of Technical Excellence</h2>
              <p className="text-gray-300 mb-4">
                Founded in 2010, Lunar A.i Studio began with a singular vision: to create advanced AI and security solutions
                that provide unparalleled protection and intelligence for organizations with the most demanding
                requirements.
              </p>
              <p className="text-gray-300 mb-4">
                What started as a small team of AI researchers and security experts has evolved into a specialized firm
                with a global reputation for technical excellence. Our journey has been defined by an unwavering
                commitment to innovation and a deep understanding of emerging threats and opportunities in the digital
                landscape.
              </p>
              <p className="text-gray-300">
                Today, we are proud to partner with some of the world's most security-conscious organizations, helping
                them navigate complex challenges with sophisticated AI systems and robust security frameworks.
              </p>
            </div>
            <LunarGlowCard glowColor="silver" intensity="medium">
              <div className="relative h-80 md:h-96 rounded-lg overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?q=80&w=800&auto=format&fit=crop"
                  alt="Lunar A.i Studio Team"
                  fill
                  className="object-cover"
                />
              </div>
            </LunarGlowCard>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?q=80&w=1920&auto=format&fit=crop"
            alt="Values background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Our Values
              </span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">The Principles That Guide Us</h2>
            <p className="text-gray-300">
              Our core values inform every decision we make and every line of code we write.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {companyValues.map((value, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800 hover:border-yellow-500 transition-colors"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-600/30 to-yellow-800/30 rounded-lg flex items-center justify-center mb-6">
                  {value.icon}
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">{value.title}</h3>
                <p className="text-gray-300">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Enhance Your Security Posture?</h2>
            <p className="text-gray-300 text-lg mb-8">
              Let's discuss how Lunar A.i Studio can help protect your organization with our advanced AI and cybersecurity
              solutions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-3 rounded-md font-medium inline-flex items-center justify-center transition-colors"
              >
                Contact Us
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
              <Link
                href="/portfolio"
                className="border border-gray-700 bg-gray-900/50 backdrop-blur-sm text-white px-8 py-3 rounded-md font-medium inline-flex items-center justify-center hover:bg-gray-800/50 transition-colors"
              >
                View Our Work
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

