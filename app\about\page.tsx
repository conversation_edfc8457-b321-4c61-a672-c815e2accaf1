"use client"

import Image from "next/image"
import Link from "next/link"
import { ArrowRight, Star, Users, Lightbulb, Shield, Brain, Crosshair, Award, Target, Heart, Zap } from "lucide-react"
import { motion } from "framer-motion"

export default function AboutPage() {
  // Stats data
  const stats = [
    { value: "500+", label: "Solutions Delivered" },
    { value: "10+", label: "Years of Excellence" },
    { value: "98%", label: "Customer Satisfaction" },
    { value: "15+", label: "Industries Served" },
  ]

  // Team members data
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "Chief Executive Officer (CEO), Founder",
      bio: "As CEO and Founder, <PERSON><PERSON> leads our company vision and strategic direction. Her innovative approach to leadership and deep expertise in AI Engineering and Python-based Automation has positioned Lunar Studio as a trusted partner for organizations seeking cutting-edge technology solutions.",
      image: "/maryum.webp",
    },
    {
      name: "<PERSON><PERSON> Abbas",
      role: "Chief Technology Officer (CTO), Founder",
      bio: "<PERSON><PERSON> leads our technical strategy and innovation initiatives as CT<PERSON> and Founder. With extensive experience in AI and machine learning, he ensures Lunar Studio stays at the cutting edge of technology while delivering robust, scalable solutions for our clients.",
      image: "/badar.png",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Chief Strategy Officer (CSO), Founder",
      bio: "<PERSON><PERSON> drives our strategic initiatives and long-term vision. His background in enterprise security architecture and business strategy ensures Lunar Studio stays ahead of market trends while delivering exceptional value to our clients.",
      image: "/usman.jpg",
    },
  ]

  // Company values
  const companyValues = [
    {
      title: "Innovation",
      description: "We continuously challenge conventional thinking, encouraging our team to explore creative solutions that push technological boundaries.",
      icon: <Lightbulb className="h-8 w-8 text-yellow-500" />,
    },
    {
      title: "Integrity",
      description: "Integrity is the cornerstone of everything we do. We maintain the highest standards of ethical conduct, ensuring transparency in our processes.",
      icon: <Shield className="h-8 w-8 text-yellow-500" />,
    },
    {
      title: "Collaboration",
      description: "We believe that the most powerful solutions emerge from collective intelligence and diverse perspectives working together.",
      icon: <Users className="h-8 w-8 text-yellow-500" />,
    },
    {
      title: "Excellence",
      description: "Excellence is our benchmark for every project, every interaction, and every solution we create for our clients.",
      icon: <Award className="h-8 w-8 text-yellow-500" />,
    },
    {
      title: "Customer-Centricity",
      description: "Our customers are at the heart of everything we do. We create tailored experiences that address unique challenges.",
      icon: <Heart className="h-8 w-8 text-yellow-500" />,
    },
    {
      title: "Adaptability",
      description: "In a world of constant technological evolution, we embrace change as an opportunity for growth and innovation.",
      icon: <Zap className="h-8 w-8 text-yellow-500" />,
    },
  ]

  return (
    <main className="pt-24 bg-gray-950">
      {/* Hero Section */}
      <section className="py-24 bg-gray-950 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20">
                <Star className="h-4 w-4 text-yellow-400 mr-2" />
                <span className="text-yellow-400 text-sm font-medium">About Lunar Studio</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold leading-tight text-white">
                Transforming Ideas Into
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-yellow-600 block">
                  Digital Reality
                </span>
              </h1>

              <p className="text-xl text-gray-300 leading-relaxed">
                We are a team of passionate innovators, dedicated to crafting exceptional software solutions
                that drive business growth and technological advancement.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-semibold rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 transform hover:scale-105"
                >
                  Let's Work Together
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <Link
                  href="/portfolio"
                  className="inline-flex items-center justify-center px-8 py-4 border border-gray-600 text-white font-semibold rounded-lg hover:border-yellow-500 hover:bg-yellow-500/10 transition-all duration-300"
                >
                  View Our Work
                </Link>
              </div>
            </motion.div>

            {/* Right Content - Image */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="relative rounded-2xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?q=80&w=800&auto=format&fit=crop"
                  alt="Lunar Studio Team"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-4xl md:text-5xl font-bold text-yellow-400 mb-2">{stat.value}</div>
                <div className="text-gray-400 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-24 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
              <Users className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-400 text-sm font-medium">Meet Our Team</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              The Minds Behind <span className="text-yellow-400">Innovation</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our diverse team of experts brings together decades of experience in software development,
              AI, and digital transformation.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                className="group bg-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-800 hover:border-yellow-500/50 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="relative w-24 h-24 mx-auto mb-6 rounded-full overflow-hidden">
                  <Image
                    src={member.image || "/placeholder.svg"}
                    alt={member.name}
                    fill
                    className="object-cover"
                  />
                </div>

                <div className="text-center">
                  <h3 className="text-xl font-bold text-white mb-2">{member.name}</h3>
                  <p className="text-yellow-400 font-medium mb-4">{member.role}</p>
                  <p className="text-gray-300 text-sm leading-relaxed mb-6">{member.bio}</p>

                  <Link
                    href="/contact"
                    className="inline-flex items-center justify-center px-6 py-3 bg-yellow-500/10 border border-yellow-500/30 text-yellow-400 rounded-lg font-medium hover:bg-yellow-500/20 transition-all duration-300"
                  >
                    Connect
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
                <Star className="h-4 w-4 text-yellow-400 mr-2" />
                <span className="text-yellow-400 text-sm font-medium">Our Journey</span>
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Building the Future, <span className="text-yellow-400">One Solution at a Time</span>
              </h2>

              <div className="space-y-6 text-gray-300 text-lg leading-relaxed">
                <p>
                  Founded with a vision to bridge the gap between cutting-edge technology and practical business solutions,
                  Lunar Studio has grown from a small team of passionate developers into a trusted partner for businesses worldwide.
                </p>
                <p>
                  Our journey began with a simple belief: that technology should empower businesses, not complicate them.
                  This philosophy drives everything we do, from our initial consultation to the final deployment of your solution.
                </p>
                <p>
                  Today, we're proud to have helped hundreds of businesses transform their operations, streamline their processes,
                  and achieve their digital goals through innovative software solutions.
                </p>
              </div>

              <div className="mt-8">
                <Link
                  href="/portfolio"
                  className="inline-flex items-center px-6 py-3 bg-yellow-500 text-black font-semibold rounded-lg hover:bg-yellow-400 transition-all duration-300"
                >
                  See Our Impact
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </motion.div>

            <motion.div
              className="relative"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="relative rounded-2xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=800&auto=format&fit=crop"
                  alt="Our Story"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-24 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
              <Target className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-400 text-sm font-medium">Our Core Values</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Values That <span className="text-yellow-400">Drive Us Forward</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              These principles guide every decision we make and every solution we create for our clients.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {companyValues.map((value, index) => (
              <motion.div
                key={index}
                className="group bg-gray-900/50 backdrop-blur-sm p-8 rounded-xl border border-gray-800 hover:border-yellow-500/50 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="w-16 h-16 bg-yellow-500/10 rounded-xl flex items-center justify-center mb-6 group-hover:bg-yellow-500/20 transition-colors duration-300">
                  {value.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-yellow-400 transition-colors duration-300">
                  {value.title}
                </h3>
                <p className="text-gray-300 leading-relaxed">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Ready to Start Your
              <br />
              <span className="text-yellow-400">Digital Journey?</span>
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Let's collaborate to bring your vision to life with innovative software solutions
              that drive real business results.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center justify-center px-10 py-5 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-bold text-lg rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-yellow-500/25"
              >
                Start Your Project
                <ArrowRight className="ml-3 h-6 w-6" />
              </Link>
              <Link
                href="/portfolio"
                className="inline-flex items-center justify-center px-10 py-5 border-2 border-gray-600 text-white font-bold text-lg rounded-lg hover:border-yellow-500 hover:bg-yellow-500/10 transition-all duration-300"
              >
                View Our Portfolio
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}

