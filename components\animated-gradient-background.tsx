"use client"

import { useEffect, useRef, useState } from "react"

interface AnimatedGradientBackgroundProps {
  className?: string
  colors?: string[]
  speed?: number
}

export default function AnimatedGradientBackground({
  className = "",
  colors = ["#4F46E5", "#0EA5E9", "#10B981", "#8B5CF6"],
  speed = 0.002,
}: AnimatedGradientBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isLowPerfDevice, setIsLowPerfDevice] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Check if device is likely low performance
    const checkPerformance = () => {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      const isOlderDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4
      setIsLowPerfDevice(isMobile || isOlderDevice)
    }

    checkPerformance()

    // Use Intersection Observer to only animate when visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting)
        })
      },
      { threshold: 0.1 },
    )

    if (canvasRef.current) {
      observer.observe(canvasRef.current)
    }

    // Delay animation start to prioritize content loading
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 800)

    return () => {
      observer.disconnect()
      clearTimeout(timer)
    }
  }, [])

  useEffect(() => {
    // Only start animation when element is visible and page has loaded
    if (!isVisible || !isLoaded) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d", { alpha: true, desynchronized: true })
    if (!ctx) return

    // Set canvas dimensions - use much lower resolution
    const resizeCanvas = () => {
      const scale = isLowPerfDevice ? 0.2 : 0.4
      canvas.width = window.innerWidth * scale
      canvas.height = window.innerHeight * scale
      canvas.style.width = `${window.innerWidth}px`
      canvas.style.height = `${window.innerHeight}px`
    }

    resizeCanvas()

    // Debounce resize events
    let resizeTimer: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimer)
      resizeTimer = setTimeout(resizeCanvas, 200)
    }

    window.addEventListener("resize", handleResize)

    // For extremely low performance devices, just draw a static gradient
    if (isLowPerfDevice) {
      // Create a static gradient background
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height)

      // Use only 2 colors for simplicity
      gradient.addColorStop(0, colors[0])
      gradient.addColorStop(1, colors[colors.length - 1])

      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      return () => {
        window.removeEventListener("resize", handleResize)
      }
    }

    // For higher performance devices, use a simplified but still animated version
    // Create gradient points - use only 2 points for better performance
    const gradientPoints = [
      {
        x: canvas.width * 0.25,
        y: canvas.height * 0.25,
        color: colors[0],
        radius: Math.max(canvas.width, canvas.height) * 0.5,
      },
      {
        x: canvas.width * 0.75,
        y: canvas.height * 0.75,
        color: colors[colors.length - 1],
        radius: Math.max(canvas.width, canvas.height) * 0.5,
      },
    ]

    // Animation loop with very low frequency updates
    let lastTime = 0
    const frameInterval = 500 // Very low fps (2fps)
    let animationFrame: number
    let angle = 0

    const animate = (timestamp: number) => {
      const deltaTime = timestamp - lastTime

      if (deltaTime > frameInterval) {
        lastTime = timestamp - (deltaTime % frameInterval)

        // Clear canvas with a base color
        ctx.fillStyle = "#000000"
        ctx.fillRect(0, 0, canvas.width, canvas.height)

        // Update gradient point positions with very slow circular motion
        angle += 0.05
        const radius = Math.min(canvas.width, canvas.height) * 0.25

        gradientPoints[0].x = canvas.width * 0.5 + Math.cos(angle) * radius
        gradientPoints[0].y = canvas.height * 0.5 + Math.sin(angle) * radius

        gradientPoints[1].x = canvas.width * 0.5 + Math.cos(angle + Math.PI) * radius
        gradientPoints[1].y = canvas.height * 0.5 + Math.sin(angle + Math.PI) * radius

        // Create radial gradients for each point
        gradientPoints.forEach((point) => {
          const gradient = ctx.createRadialGradient(point.x, point.y, 0, point.x, point.y, point.radius)

          gradient.addColorStop(0, point.color)
          gradient.addColorStop(1, "rgba(0, 0, 0, 0)")

          ctx.globalCompositeOperation = "lighter"
          ctx.fillStyle = gradient
          ctx.fillRect(0, 0, canvas.width, canvas.height)
        })

        ctx.globalCompositeOperation = "source-over"
      }

      animationFrame = requestAnimationFrame(animate)
    }

    animationFrame = requestAnimationFrame(animate)

    return () => {
      window.removeEventListener("resize", handleResize)
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [colors, speed, isLowPerfDevice, isVisible, isLoaded])

  return <canvas ref={canvasRef} className={`absolute inset-0 -z-10 ${className}`} style={{ opacity: 0.7 }} />
}
