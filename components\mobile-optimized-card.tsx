"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import type { LucideIcon } from "lucide-react"

interface MobileOptimizedCardProps {
  title: string
  description: string
  icon: LucideIcon
  index?: number
  onClick?: () => void
  className?: string
}

export default function MobileOptimizedCard({
  title,
  description,
  icon: Icon,
  index = 0,
  onClick,
  className = "",
}: MobileOptimizedCardProps) {
  const [isTapped, setIsTapped] = useState(false)

  const handleTap = () => {
    setIsTapped(true)
    setTimeout(() => setIsTapped(false), 300)
    if (onClick) onClick()
  }

  return (
    <motion.div
      className={`relative bg-gray-900/70 backdrop-blur-sm p-6 rounded-xl border border-gray-800 ${
        isTapped ? "border-yellow-500" : ""
      } ${className}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      viewport={{ once: true, margin: "-50px" }}
      onTap={handleTap}
      whileTap={{ scale: 0.98 }}
    >
      <div className="flex items-start">
        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 flex items-center justify-center mr-4 flex-shrink-0 shadow-lg">
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-bold mb-2 text-white">{title}</h3>
          <p className="text-gray-300 text-sm">{description}</p>
        </div>
      </div>

      {/* Ripple effect on tap */}
      {isTapped && (
        <motion.div
          className="absolute inset-0 bg-yellow-500/10 rounded-xl z-0"
          initial={{ opacity: 0.5, scale: 0.8 }}
          animate={{ opacity: 0, scale: 1.2 }}
          transition={{ duration: 0.4 }}
        />
      )}
    </motion.div>
  )
}
