"use client"

import type React from "react"

import { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"

interface InteractiveMapProps {
  className?: string
  location?: { lat: number; lng: number }
  zoom?: number
}

export default function InteractiveMap({
  className = "",
  location = { lat: 37.7749, lng: -122.4194 }, // Default: San Francisco
  zoom = 15,
}: InteractiveMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [mapPosition, setMapPosition] = useState({ x: 0, y: 0 })
  const [startDragPosition, setStartDragPosition] = useState({ x: 0, y: 0 })
  const [currentZoom, setCurrentZoom] = useState(zoom)

  // Draw the map
  useEffect(() => {
    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")
    if (!mapRef.current || !ctx) return

    const drawMap = () => {
      const mapContainer = mapRef.current
      if (!mapContainer) return

      // Set canvas dimensions
      canvas.width = mapContainer.clientWidth
      canvas.height = mapContainer.clientHeight
      mapContainer.innerHTML = ""
      mapContainer.appendChild(canvas)

      // Draw base map
      ctx.fillStyle = "#1a1a1a"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Draw grid lines
      ctx.strokeStyle = "#333333"
      ctx.lineWidth = 1

      // Horizontal grid lines
      const gridSize = 50 * (currentZoom / 10)
      const offsetX = mapPosition.x % gridSize
      const offsetY = mapPosition.y % gridSize

      for (let y = offsetY; y < canvas.height; y += gridSize) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(canvas.width, y)
        ctx.stroke()
      }

      // Vertical grid lines
      for (let x = offsetX; x < canvas.width; x += gridSize) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, canvas.height)
        ctx.stroke()
      }

      // Draw roads
      drawRoads(ctx, canvas.width, canvas.height, gridSize, offsetX, offsetY)

      // Draw location marker
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2

      // Draw marker shadow
      ctx.beginPath()
      ctx.arc(centerX, centerY + 15, 15, 0, Math.PI * 2)
      ctx.fillStyle = "rgba(0, 0, 0, 0.3)"
      ctx.fill()

      // Create marker element
      const marker = document.createElement("div")
      marker.className = "absolute z-10 transform -translate-x-1/2 -translate-y-full"
      marker.style.left = `${centerX}px`
      marker.style.top = `${centerY}px`

      // Add marker content
      marker.innerHTML = `
        <div class="flex flex-col items-center">
          <div class="animate-bounce">
            <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" class="text-black">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
            </div>
          </div>
          <div class="mt-2 bg-white text-black px-3 py-1 rounded-lg shadow-lg text-sm font-medium">
            Lunar Studio HQ
          </div>
        </div>
      `

      mapContainer.appendChild(marker)

      // Add buildings
      drawBuildings(ctx, canvas.width, canvas.height, gridSize, offsetX, offsetY)
    }

    const drawRoads = (
      ctx: CanvasRenderingContext2D,
      width: number,
      height: number,
      gridSize: number,
      offsetX: number,
      offsetY: number,
    ) => {
      // Main roads
      ctx.strokeStyle = "#555555"
      ctx.lineWidth = 8

      // Horizontal main roads
      for (let y = offsetY; y < height; y += gridSize * 3) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(width, y)
        ctx.stroke()
      }

      // Vertical main roads
      for (let x = offsetX; x < width; x += gridSize * 3) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, height)
        ctx.stroke()
      }

      // Secondary roads
      ctx.strokeStyle = "#444444"
      ctx.lineWidth = 4

      // Draw some curved roads
      ctx.beginPath()
      ctx.moveTo(width * 0.2 + offsetX, height * 0.3 + offsetY)
      ctx.quadraticCurveTo(width * 0.5 + offsetX, height * 0.1 + offsetY, width * 0.8 + offsetX, height * 0.4 + offsetY)
      ctx.stroke()

      ctx.beginPath()
      ctx.moveTo(width * 0.1 + offsetX, height * 0.7 + offsetY)
      ctx.quadraticCurveTo(width * 0.4 + offsetX, height * 0.9 + offsetY, width * 0.7 + offsetX, height * 0.6 + offsetY)
      ctx.stroke()
    }

    const drawBuildings = (
      ctx: CanvasRenderingContext2D,
      width: number,
      height: number,
      gridSize: number,
      offsetX: number,
      offsetY: number,
    ) => {
      // Generate some random buildings
      const buildingCount = 50
      ctx.fillStyle = "#333333"

      for (let i = 0; i < buildingCount; i++) {
        const x = Math.random() * width * 1.5 - width * 0.25 + offsetX
        const y = Math.random() * height * 1.5 - height * 0.25 + offsetY
        const buildingWidth = Math.random() * 40 + 20
        const buildingHeight = Math.random() * 40 + 20

        ctx.fillRect(x, y, buildingWidth, buildingHeight)
      }

      // Add some landmark buildings
      ctx.fillStyle = "#444444"
      ctx.fillRect(width / 2 - 30 + offsetX, height / 2 - 30 + offsetY, 60, 60)

      // Add some building details
      ctx.fillStyle = "#222222"
      for (let i = 0; i < 10; i++) {
        const x = Math.random() * width + offsetX
        const y = Math.random() * height + offsetY
        const size = Math.random() * 15 + 5

        ctx.beginPath()
        ctx.arc(x, y, size, 0, Math.PI * 2)
        ctx.fill()
      }
    }

    // Initial draw
    drawMap()

    // Handle window resize
    const handleResize = () => {
      drawMap()
    }

    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [mapPosition, currentZoom, location])

  // Handle mouse interactions
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setStartDragPosition({
      x: e.clientX - mapPosition.x,
      y: e.clientY - mapPosition.y,
    })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setMapPosition({
        x: e.clientX - startDragPosition.x,
        y: e.clientY - startDragPosition.y,
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleMouseLeave = () => {
    setIsDragging(false)
    setIsHovered(false)
  }

  const handleWheel = (e: React.WheelEvent) => {
    // Zoom in/out
    const newZoom = Math.max(5, Math.min(20, currentZoom - e.deltaY * 0.01))
    setCurrentZoom(newZoom)
  }

  return (
    <motion.div
      className={`relative overflow-hidden rounded-lg ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div
        ref={mapRef}
        className="w-full h-full relative cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onWheel={handleWheel}
      />

      {/* Map controls */}
      <div className="absolute top-4 right-4 bg-black/70 backdrop-blur-sm rounded-lg p-2 flex flex-col gap-2">
        <button
          className="w-8 h-8 bg-white text-black rounded-md flex items-center justify-center hover:bg-gray-200 transition-colors"
          onClick={() => setCurrentZoom(Math.min(20, currentZoom + 1))}
        >
          +
        </button>
        <button
          className="w-8 h-8 bg-white text-black rounded-md flex items-center justify-center hover:bg-gray-200 transition-colors"
          onClick={() => setCurrentZoom(Math.max(5, currentZoom - 1))}
        >
          -
        </button>
      </div>

      {/* Instructions overlay */}
      {isHovered && (
        <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white text-xs px-3 py-2 rounded-lg">
          Drag to move • Scroll to zoom
        </div>
      )}
    </motion.div>
  )
}
