"use client"

import type React from "react"

import { useRef } from "react"
import { motion, useScroll, useTransform, useSpring } from "framer-motion"

interface ScrollRevealSectionProps {
  children: React.ReactNode
  className?: string
  threshold?: number
  animation?: "fade" | "slide" | "zoom" | "rotate" | "flip"
  delay?: number
  duration?: number
}

export default function ScrollRevealSection({
  children,
  className = "",
  threshold = 0.2,
  animation = "fade",
  delay = 0,
  duration = 0.5,
}: ScrollRevealSectionProps) {
  const sectionRef = useRef<HTMLDivElement>(null)

  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", `start ${1 - threshold}`],
  })

  // Add spring physics for smoother animation
  const springConfig = { stiffness: 100, damping: 30, restDelta: 0.001 }
  const springScrollProgress = useSpring(scrollYProgress, springConfig)

  // Define different animation variants
  const opacity = useTransform(springScrollProgress, [0, 1], [0, 1])
  const x = useTransform(springScrollProgress, [0, 1], [100, 0])
  const scale = useTransform(springScrollProgress, [0, 1], [0.8, 1])
  const rotateY = useTransform(springScrollProgress, [0, 1], [45, 0])
  const rotateX = useTransform(springScrollProgress, [0, 1], [90, 0])

  const getAnimationProps = () => {
    switch (animation) {
      case "fade":
        return {
          opacity: opacity,
        }
      case "slide":
        return {
          opacity: opacity,
          x: x,
        }
      case "zoom":
        return {
          opacity: opacity,
          scale: scale,
        }
      case "rotate":
        return {
          opacity: opacity,
          rotateY: rotateY,
        }
      case "flip":
        return {
          opacity: opacity,
          rotateX: rotateX,
        }
      default:
        return {
          opacity: opacity,
        }
    }
  }

  return (
    <motion.div ref={sectionRef} className={className} style={getAnimationProps()} transition={{ duration, delay }}>
      {children}
    </motion.div>
  )
}
