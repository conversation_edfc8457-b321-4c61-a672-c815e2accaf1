import Image from "next/image"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

export default function BlogPage() {
  return (
    <main className="pt-24">
      {/* Header */}
      <section className="bg-black text-white py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Blog</h1>
            <p className="text-gray-300 text-lg">
              Insights, trends, and expert perspectives on AI, cybersecurity, and digital innovation.
            </p>
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-center">Featured Posts</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {/* Featured Post 1 */}
            <div className="bg-gray-50 rounded-lg overflow-hidden shadow-md">
              <div className="relative h-48">
                <Image
                  src="https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=2940&auto=format&fit=crop"
                  alt="The Future of RAG-Based AI"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <span className="bg-black text-white text-xs px-2 py-1 rounded">AI & Tech</span>
                  <span className="text-gray-500 text-sm ml-auto">June 15, 2023</span>
                </div>
                <h3 className="text-xl font-bold mb-2">The Future of RAG-Based AI in Enterprise Solutions</h3>
                <p className="text-gray-600 mb-4">
                  Explore how Retrieval-Augmented Generation is transforming how businesses leverage their proprietary
                  data with AI.
                </p>
                <Link
                  href="/blog/future-of-rag-ai"
                  className="text-black font-medium inline-flex items-center hover:underline"
                >
                  Read More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* Featured Post 2 */}
            <div className="bg-gray-50 rounded-lg overflow-hidden shadow-md">
              <div className="relative h-48">
                <Image
                  src="https://images.unsplash.com/photo-1563986768609-322da13575f3?q=80&w=2940&auto=format&fit=crop"
                  alt="Cybersecurity Trends"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <span className="bg-black text-white text-xs px-2 py-1 rounded">Cybersecurity</span>
                  <span className="text-gray-500 text-sm ml-auto">May 28, 2023</span>
                </div>
                <h3 className="text-xl font-bold mb-2">5 Cybersecurity Trends Every Business Should Watch in 2023</h3>
                <p className="text-gray-600 mb-4">
                  Stay ahead of emerging threats with our expert analysis of the latest cybersecurity trends and best
                  practices.
                </p>
                <Link
                  href="/blog/cybersecurity-trends-2023"
                  className="text-black font-medium inline-flex items-center hover:underline"
                >
                  Read More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* Featured Post 3 */}
            <div className="bg-gray-50 rounded-lg overflow-hidden shadow-md">
              <div className="relative h-48">
                <Image
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2115&auto=format&fit=crop"
                  alt="Digital Marketing Strategies"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <span className="bg-black text-white text-xs px-2 py-1 rounded">Digital Marketing</span>
                  <span className="text-gray-500 text-sm ml-auto">April 10, 2023</span>
                </div>
                <h3 className="text-xl font-bold mb-2">How AI is Revolutionizing Digital Marketing Strategies</h3>
                <p className="text-gray-600 mb-4">
                  Discover how AI-powered tools are helping marketers create more personalized and effective campaigns.
                </p>
                <Link
                  href="/blog/ai-digital-marketing"
                  className="text-black font-medium inline-flex items-center hover:underline"
                >
                  Read More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Categories */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-center">Browse by Category</h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <Link href="/blog/category/ai-tech" className="group">
              <div className="bg-white p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl group-hover:-translate-y-1">
                <h3 className="text-xl font-bold mb-2">AI & Tech Innovations</h3>
                <p className="text-gray-600 mb-4">
                  The latest breakthroughs in artificial intelligence, machine learning, and emerging technologies.
                </p>
                <span className="text-black font-medium inline-flex items-center group-hover:underline">
                  View Articles
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </span>
              </div>
            </Link>

            <Link href="/blog/category/cybersecurity" className="group">
              <div className="bg-white p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl group-hover:-translate-y-1">
                <h3 className="text-xl font-bold mb-2">Cybersecurity Trends</h3>
                <p className="text-gray-600 mb-4">
                  Expert insights on protecting your digital assets, threat intelligence, and security best practices.
                </p>
                <span className="text-black font-medium inline-flex items-center group-hover:underline">
                  View Articles
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </span>
              </div>
            </Link>

            <Link href="/blog/category/digital-marketing" className="group">
              <div className="bg-white p-6 rounded-lg shadow-md transition-all duration-300 hover:shadow-xl group-hover:-translate-y-1">
                <h3 className="text-xl font-bold mb-2">Digital Marketing Tips</h3>
                <p className="text-gray-600 mb-4">
                  Strategies, tactics, and tools to help you grow your online presence and engage with your audience.
                </p>
                <span className="text-black font-medium inline-flex items-center group-hover:underline">
                  View Articles
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </span>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Recent Posts */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-12 text-center">Recent Posts</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {/* Recent Post 1 */}
            <div className="flex flex-col md:flex-row gap-6">
              <div className="relative w-full md:w-1/3 h-48 md:h-auto rounded-lg overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1677442135136-760c813a743d?q=80&w=2832&auto=format&fit=crop"
                  alt="Agentic AI Applications"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="md:w-2/3">
                <div className="flex items-center mb-2">
                  <span className="bg-black text-white text-xs px-2 py-1 rounded">AI & Tech</span>
                  <span className="text-gray-500 text-sm ml-4">June 5, 2023</span>
                </div>
                <h3 className="text-xl font-bold mb-2">Practical Applications of Agentic AI in Business Operations</h3>
                <p className="text-gray-600 mb-4">
                  Learn how autonomous AI agents are streamlining workflows and enhancing decision-making processes.
                </p>
                <Link
                  href="/blog/agentic-ai-applications"
                  className="text-black font-medium inline-flex items-center hover:underline"
                >
                  Read More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* Recent Post 2 */}
            <div className="flex flex-col md:flex-row gap-6">
              <div className="relative w-full md:w-1/3 h-48 md:h-auto rounded-lg overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1614064641938-3bbee52942c7?q=80&w=2940&auto=format&fit=crop"
                  alt="Zero Trust Security"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="md:w-2/3">
                <div className="flex items-center mb-2">
                  <span className="bg-black text-white text-xs px-2 py-1 rounded">Cybersecurity</span>
                  <span className="text-gray-500 text-sm ml-4">May 22, 2023</span>
                </div>
                <h3 className="text-xl font-bold mb-2">Implementing Zero Trust Security in Your Organization</h3>
                <p className="text-gray-600 mb-4">
                  A comprehensive guide to adopting a zero trust security model to protect your digital assets.
                </p>
                <Link
                  href="/blog/zero-trust-security"
                  className="text-black font-medium inline-flex items-center hover:underline"
                >
                  Read More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* Recent Post 3 */}
            <div className="flex flex-col md:flex-row gap-6">
              <div className="relative w-full md:w-1/3 h-48 md:h-auto rounded-lg overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1547658719-da2b51169166?q=80&w=2864&auto=format&fit=crop"
                  alt="Web Development Trends"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="md:w-2/3">
                <div className="flex items-center mb-2">
                  <span className="bg-black text-white text-xs px-2 py-1 rounded">Web Development</span>
                  <span className="text-gray-500 text-sm ml-4">May 15, 2023</span>
                </div>
                <h3 className="text-xl font-bold mb-2">Top Web Development Trends to Watch in 2023</h3>
                <p className="text-gray-600 mb-4">
                  Stay ahead of the curve with these emerging web development technologies and methodologies.
                </p>
                <Link
                  href="/blog/web-development-trends"
                  className="text-black font-medium inline-flex items-center hover:underline"
                >
                  Read More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>

            {/* Recent Post 4 */}
            <div className="flex flex-col md:flex-row gap-6">
              <div className="relative w-full md:w-1/3 h-48 md:h-auto rounded-lg overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop"
                  alt="Data Science for Business"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="md:w-2/3">
                <div className="flex items-center mb-2">
                  <span className="bg-black text-white text-xs px-2 py-1 rounded">Data Science</span>
                  <span className="text-gray-500 text-sm ml-4">May 8, 2023</span>
                </div>
                <h3 className="text-xl font-bold mb-2">Leveraging Data Science for Business Growth</h3>
                <p className="text-gray-600 mb-4">
                  How to use data analytics to gain competitive insights and drive strategic decision-making.
                </p>
                <Link
                  href="/blog/data-science-business-growth"
                  className="text-black font-medium inline-flex items-center hover:underline"
                >
                  Read More
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>

          <div className="text-center mt-8">
            <Link
              href="/blog/all"
              className="bg-black text-white px-8 py-3 rounded-md font-medium inline-flex items-center hover:bg-gray-800 transition-colors"
            >
              View All Posts
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-20 bg-black text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Subscribe to Our Newsletter</h2>
            <p className="text-gray-300 text-lg mb-8">
              Stay updated with the latest insights, trends, and news in AI, cybersecurity, and digital innovation.
            </p>
            <form className="flex flex-col sm:flex-row gap-4 justify-center">
              <input
                type="email"
                placeholder="Enter your email"
                className="px-4 py-3 rounded-md focus:outline-none text-black w-full sm:w-auto sm:flex-1 max-w-md"
                required
              />
              <button
                type="submit"
                className="bg-white text-black px-8 py-3 rounded-md font-medium hover:bg-gray-200 transition-colors"
              >
                Subscribe
              </button>
            </form>
            <p className="text-gray-400 text-sm mt-4">We respect your privacy. Unsubscribe at any time.</p>
          </div>
        </div>
      </section>
    </main>
  )
}
