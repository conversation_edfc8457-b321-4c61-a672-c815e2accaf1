"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON>R<PERSON>, BookOpen, Calendar, Clock, Search, Filter } from "lucide-react"
import { motion } from "framer-motion"
import { useState } from "react"

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState("All Blogs")

  const categories = [
    "All Blogs",
    "Web Development",
    "Mobile App Development",
    "AI and Machine Learning",
    "Tech Tools and Frameworks",
    "Tech Trends and Innovations",
    "UX/UI and Design",
    "Career Growth in Tech"
  ]

  const featuredPosts = [
    {
      title: "The Future of AI in Software Development: Trends to Watch in 2025",
      excerpt: "Explore how artificial intelligence is revolutionizing the software development landscape and what developers need to know to stay ahead.",
      image: "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=800&auto=format&fit=crop",
      category: "AI and Machine Learning",
      readTime: "8 min read",
      date: "2025-01-15",
      slug: "future-of-ai-software-development-2025"
    },
    {
      title: "Building Scalable Web Applications with Next.js and TypeScript",
      excerpt: "A comprehensive guide to creating robust, type-safe web applications that can handle enterprise-level traffic and complexity.",
      image: "https://images.unsplash.com/photo-1547658719-da2b51169166?q=80&w=800&auto=format&fit=crop",
      category: "Web Development",
      readTime: "12 min read",
      date: "2025-01-10",
      slug: "scalable-web-apps-nextjs-typescript"
    },
    {
      title: "Mobile-First Design: Creating Exceptional User Experiences",
      excerpt: "Learn the principles and best practices for designing mobile-first applications that delight users across all devices.",
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?q=80&w=800&auto=format&fit=crop",
      category: "UX/UI and Design",
      readTime: "6 min read",
      date: "2025-01-05",
      slug: "mobile-first-design-user-experience"
    }
  ]

  return (
    <main className="pt-24 bg-gray-950">
      {/* Hero Section */}
      <section className="py-24 bg-gray-950 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
              <BookOpen className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-400 text-sm font-medium">Our Way of Learning</span>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Insights & <span className="text-yellow-400">Innovation</span>
            </h1>

            <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto">
              Step into our center of insights, where intricate technical subjects & dynamic industry news
              are simplified perfectly for every learner.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-12 bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? "bg-yellow-500 text-black"
                    : "bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredPosts.map((post, index) => (
              <motion.article
                key={index}
                className="group bg-gray-900/50 backdrop-blur-sm rounded-xl overflow-hidden border border-gray-800 hover:border-yellow-500/50 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={post.image}
                    alt={post.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-yellow-500 text-black text-xs px-3 py-1 rounded-full font-medium">
                      {post.category}
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex items-center text-gray-400 text-sm mb-4">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>{new Date(post.date).toLocaleDateString()}</span>
                    <Clock className="h-4 w-4 ml-4 mr-2" />
                    <span>{post.readTime}</span>
                  </div>

                  <h3 className="text-xl font-bold text-white mb-3 group-hover:text-yellow-400 transition-colors duration-300">
                    {post.title}
                  </h3>

                  <p className="text-gray-300 mb-6 leading-relaxed">
                    {post.excerpt}
                  </p>

                  <Link
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center text-yellow-400 hover:text-yellow-300 font-medium group/link"
                  >
                    Read more
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/link:translate-x-1" />
                  </Link>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
      </section>

      {/* Recent Posts */}
      <section className="py-24 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Latest <span className="text-yellow-400">Articles</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Stay updated with the latest trends, insights, and best practices in software development and technology.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              {
                title: "Building Microservices with Node.js and Docker",
                excerpt: "Learn how to architect and deploy scalable microservices using modern containerization techniques.",
                image: "https://images.unsplash.com/photo-1618477247222-acbdb0e159b3?q=80&w=800&auto=format&fit=crop",
                category: "Web Development",
                readTime: "10 min read",
                date: "2025-01-12"
              },
              {
                title: "The Rise of Low-Code Development Platforms",
                excerpt: "Exploring how low-code platforms are democratizing software development and accelerating digital transformation.",
                image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=800&auto=format&fit=crop",
                category: "Tech Trends",
                readTime: "7 min read",
                date: "2025-01-08"
              },
              {
                title: "Implementing Real-time Features with WebSockets",
                excerpt: "A comprehensive guide to adding real-time functionality to your web applications using WebSocket technology.",
                image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=800&auto=format&fit=crop",
                category: "Web Development",
                readTime: "12 min read",
                date: "2025-01-03"
              },
              {
                title: "Machine Learning for Web Developers",
                excerpt: "Getting started with machine learning concepts and tools that every web developer should know about.",
                image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?q=80&w=800&auto=format&fit=crop",
                category: "AI and Machine Learning",
                readTime: "15 min read",
                date: "2024-12-28"
              }
            ].map((post, index) => (
              <motion.article
                key={index}
                className="group flex flex-col md:flex-row gap-6 bg-gray-900/30 backdrop-blur-sm rounded-xl p-6 border border-gray-800 hover:border-yellow-500/50 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="relative w-full md:w-48 h-32 md:h-auto rounded-lg overflow-hidden flex-shrink-0">
                  <Image
                    src={post.image}
                    alt={post.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                </div>

                <div className="flex-1">
                  <div className="flex items-center gap-4 text-gray-400 text-sm mb-3">
                    <span className="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs font-medium">
                      {post.category}
                    </span>
                    <span className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {new Date(post.date).toLocaleDateString()}
                    </span>
                    <span className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {post.readTime}
                    </span>
                  </div>

                  <h3 className="text-xl font-bold text-white mb-3 group-hover:text-yellow-400 transition-colors duration-300">
                    {post.title}
                  </h3>

                  <p className="text-gray-300 mb-4 leading-relaxed">
                    {post.excerpt}
                  </p>

                  <Link
                    href="#"
                    className="inline-flex items-center text-yellow-400 hover:text-yellow-300 font-medium group/link"
                  >
                    Read more
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/link:translate-x-1" />
                  </Link>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
      </section>

          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Link
              href="/blog/all"
              className="inline-flex items-center px-8 py-4 bg-yellow-500 text-black font-semibold rounded-lg hover:bg-yellow-400 transition-all duration-300 transform hover:scale-105"
            >
              View More
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="max-w-4xl mx-auto text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Stay Updated with <span className="text-yellow-400">Tech Insights</span>
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Get the latest articles, tutorials, and industry insights delivered straight to your inbox.
              Join our community of developers and tech enthusiasts.
            </p>

            <form className="flex flex-col sm:flex-row gap-4 justify-center max-w-lg mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-6 py-4 rounded-lg bg-gray-900 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500 transition-colors"
                required
              />
              <button
                type="submit"
                className="px-8 py-4 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-semibold rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 transform hover:scale-105"
              >
                Subscribe
              </button>
            </form>

            <p className="text-gray-400 text-sm mt-6">
              Join 10,000+ developers • No spam, ever • Unsubscribe anytime
            </p>
          </motion.div>
        </div>
      </section>
    </main>
  )
}
