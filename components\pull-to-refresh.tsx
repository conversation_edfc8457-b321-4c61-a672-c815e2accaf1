"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { motion, useAnimation } from "framer-motion"
import { RefreshCw } from "lucide-react"

interface PullToRefreshProps {
  onRefresh: () => Promise<void>
  children: React.ReactNode
}

export default function PullToRefresh({ onRefresh, children }: PullToRefreshProps) {
  const [isPulling, setIsPulling] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const startY = useRef(0)
  const controls = useAnimation()
  const refreshThreshold = 80 // Distance in pixels to trigger refresh

  useEffect(() => {
    const handleTouchStart = (e: TouchEvent) => {
      // Only enable pull to refresh at the top of the page
      if (window.scrollY <= 0) {
        startY.current = e.touches[0].clientY
        setIsPulling(true)
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isPulling) return

      const currentY = e.touches[0].clientY
      const distance = currentY - startY.current

      // Only allow pulling down, not up
      if (distance > 0) {
        // Apply resistance to make it harder to pull
        const resistedDistance = Math.min(distance * 0.4, refreshThreshold * 1.5)
        setPullDistance(resistedDistance)
        controls.set({ y: resistedDistance })

        // Prevent default scrolling behavior when pulling
        if (window.scrollY <= 0) {
          e.preventDefault()
        }
      }
    }

    const handleTouchEnd = async () => {
      if (!isPulling) return

      setIsPulling(false)

      if (pullDistance >= refreshThreshold) {
        // Trigger refresh
        setIsRefreshing(true)
        controls.start({ y: refreshThreshold / 2 })

        try {
          await onRefresh()
        } catch (error) {
          console.error("Refresh failed:", error)
        }

        setIsRefreshing(false)
      }

      // Reset position with animation
      controls.start({ y: 0 })
      setPullDistance(0)
    }

    document.addEventListener("touchstart", handleTouchStart, { passive: true })
    document.addEventListener("touchmove", handleTouchMove, { passive: false })
    document.addEventListener("touchend", handleTouchEnd)

    return () => {
      document.removeEventListener("touchstart", handleTouchStart)
      document.removeEventListener("touchmove", handleTouchMove)
      document.removeEventListener("touchend", handleTouchEnd)
    }
  }, [isPulling, pullDistance, controls, onRefresh])

  return (
    <div className="relative overflow-hidden">
      {/* Pull to refresh indicator */}
      <motion.div
        className="absolute top-0 left-0 right-0 flex justify-center items-center pointer-events-none z-50"
        animate={controls}
      >
        <div className="bg-black/70 backdrop-blur-sm rounded-full p-2 shadow-lg">
          <motion.div
            animate={isRefreshing ? { rotate: 360 } : { rotate: (pullDistance / refreshThreshold) * 180 }}
            transition={isRefreshing ? { repeat: Number.POSITIVE_INFINITY, duration: 1, ease: "linear" } : {}}
          >
            <RefreshCw className="h-6 w-6 text-yellow-500" />
          </motion.div>
        </div>
      </motion.div>

      {/* Content */}
      <div>{children}</div>
    </div>
  )
}
