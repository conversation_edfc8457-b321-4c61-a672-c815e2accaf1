"use client"

import { useState, useEffect, useRef } from "react"
import { useInView } from "framer-motion"

interface CountUpProps {
  end: number
  start?: number
  duration?: number
  delay?: number
  decimals?: number
  prefix?: string
  suffix?: string
}

export default function CountUp({
  end,
  start = 0,
  duration = 2.5,
  delay = 0,
  decimals = 0,
  prefix = "",
  suffix = "",
}: CountUpProps) {
  const [count, setCount] = useState(start)
  const [isAnimating, setIsAnimating] = useState(false)
  const ref = useRef<HTMLSpanElement>(null)
  const isInView = useInView(ref, { once: true, amount: 0.5 })

  useEffect(() => {
    if (!isInView || isAnimating) return

    setIsAnimating(true)

    let startTime: number
    let animationFrame: number

    const startAnimation = (timestamp: number) => {
      startTime = timestamp
      animate(timestamp)
    }

    const animate = (timestamp: number) => {
      const runtime = timestamp - startTime
      const relativeProgress = runtime / (duration * 1000)

      if (relativeProgress < 1) {
        const currentCount = Math.min(start + (end - start) * easeOutQuad(relativeProgress), end)

        setCount(currentCount)
        animationFrame = requestAnimationFrame(animate)
      } else {
        setCount(end)
        cancelAnimationFrame(animationFrame)
      }
    }

    // Easing function
    const easeOutQuad = (t: number) => t * (2 - t)

    // Start animation after delay
    const timeout = setTimeout(() => {
      animationFrame = requestAnimationFrame(startAnimation)
    }, delay * 1000)

    return () => {
      clearTimeout(timeout)
      cancelAnimationFrame(animationFrame)
    }
  }, [isInView, isAnimating, start, end, duration, delay])

  return (
    <span ref={ref}>
      {prefix}
      {count.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
      {suffix}
    </span>
  )
}
