"use client"

import { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"
import { TrendingUp, <PERSON>, MousePointer, DollarSign } from "lucide-react"

export default function MarketingAnalyticsChart() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [activeMetric, setActiveMetric] = useState("conversions")
  const [isHovered, setIsHovered] = useState(false)

  const metrics = {
    conversions: {
      color: "#3b82f6",
      icon: TrendingUp,
      data: [15, 25, 32, 40, 35, 55, 62, 70, 65, 80, 90, 85],
      label: "Conversions",
      value: "+85%",
      description: "Year-over-year growth",
    },
    visitors: {
      color: "#10b981",
      icon: Users,
      data: [100, 150, 200, 180, 250, 300, 280, 350, 400, 380, 450, 500],
      label: "Visitors",
      value: "12.5K",
      description: "Monthly average",
    },
    clickRate: {
      color: "#f59e0b",
      icon: Mouse<PERSON>ointer,
      data: [2.5, 2.8, 3.2, 3.0, 3.5, 3.8, 4.0, 4.2, 4.5, 4.8, 5.0, 5.2],
      label: "Click Rate",
      value: "5.2%",
      description: "Current performance",
    },
    revenue: {
      color: "#8b5cf6",
      icon: DollarSign,
      data: [10000, 15000, 18000, 22000, 20000, 25000, 30000, 28000, 35000, 40000, 45000, 50000],
      label: "Revenue",
      value: "$50K",
      description: "Monthly revenue",
    },
  }

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // Draw chart
    const drawChart = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      const data = metrics[activeMetric as keyof typeof metrics].data
      const color = metrics[activeMetric as keyof typeof metrics].color

      const padding = 40
      const chartWidth = canvas.width - padding * 2
      const chartHeight = canvas.height - padding * 2

      // Find max value for scaling
      const maxValue = Math.max(...data)

      // Draw grid lines
      ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
      ctx.lineWidth = 1

      // Horizontal grid lines
      for (let i = 0; i <= 4; i++) {
        const y = padding + (chartHeight / 4) * i
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(padding + chartWidth, y)
        ctx.stroke()
      }

      // Vertical grid lines
      for (let i = 0; i <= data.length; i++) {
        const x = padding + (chartWidth / data.length) * i
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, padding + chartHeight)
        ctx.stroke()
      }

      // Draw x-axis labels (months)
      const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
      ctx.fillStyle = "rgba(255, 255, 255, 0.5)"
      ctx.font = "10px Arial"
      ctx.textAlign = "center"

      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i
        ctx.fillText(months[i], x, padding + chartHeight + 15)
      }

      // Draw line chart
      ctx.strokeStyle = color
      ctx.lineWidth = 3
      ctx.beginPath()

      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i
        const y = padding + chartHeight - (data[i] / maxValue) * chartHeight

        if (i === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }

      ctx.stroke()

      // Draw gradient area under the line
      const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight)
      gradient.addColorStop(0, `${color}40`) // 25% opacity
      gradient.addColorStop(1, `${color}00`) // 0% opacity

      ctx.fillStyle = gradient
      ctx.beginPath()

      // Start from bottom left
      ctx.moveTo(padding, padding + chartHeight)

      // Draw line to match the chart line
      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i
        const y = padding + chartHeight - (data[i] / maxValue) * chartHeight
        ctx.lineTo(x, y)
      }

      // Complete the shape
      ctx.lineTo(padding + chartWidth, padding + chartHeight)
      ctx.closePath()
      ctx.fill()

      // Draw data points
      ctx.fillStyle = color

      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i
        const y = padding + chartHeight - (data[i] / maxValue) * chartHeight

        ctx.beginPath()
        ctx.arc(x, y, 4, 0, Math.PI * 2)
        ctx.fill()

        ctx.beginPath()
        ctx.arc(x, y, 6, 0, Math.PI * 2)
        ctx.strokeStyle = "rgba(255, 255, 255, 0.3)"
        ctx.lineWidth = 2
        ctx.stroke()
      }
    }

    drawChart()

    // Handle window resize
    const handleResize = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
      drawChart()
    }

    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [
    activeMetric,
    metrics[activeMetric as keyof typeof metrics].color,
    metrics[activeMetric as keyof typeof metrics].data,
  ])

  const MetricIcon = metrics[activeMetric as keyof typeof metrics].icon

  return (
    <motion.div
      className="relative w-full bg-gradient-to-br from-gray-900 to-black rounded-xl overflow-hidden shadow-2xl"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <div
              className="w-10 h-10 rounded-full flex items-center justify-center mr-3"
              style={{ backgroundColor: `${metrics[activeMetric as keyof typeof metrics].color}20` }}
            >
              <MetricIcon className="h-5 w-5" style={{ color: metrics[activeMetric as keyof typeof metrics].color }} />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">Marketing Analytics</h3>
              <p className="text-gray-400 text-sm">Performance metrics visualization</p>
            </div>
          </div>

          <div className="text-right">
            <p className="text-2xl font-bold text-white">{metrics[activeMetric as keyof typeof metrics].value}</p>
            <p className="text-gray-400 text-xs">{metrics[activeMetric as keyof typeof metrics].description}</p>
          </div>
        </div>

        <div className="h-64">
          <canvas ref={canvasRef} className="w-full h-full"></canvas>
        </div>

        <div className="grid grid-cols-4 gap-2 mt-6">
          {Object.entries(metrics).map(([key, metric]) => {
            const Icon = metric.icon
            return (
              <button
                key={key}
                className={`p-3 rounded-lg transition-all duration-300 ${
                  activeMetric === key ? "bg-white/10 shadow-lg" : "bg-transparent hover:bg-white/5"
                }`}
                onClick={() => setActiveMetric(key)}
              >
                <div className="flex flex-col items-center">
                  <Icon
                    className="h-5 w-5 mb-1"
                    style={{ color: activeMetric === key ? metric.color : "rgba(255,255,255,0.5)" }}
                  />
                  <span className={`text-xs ${activeMetric === key ? "text-white" : "text-gray-400"}`}>
                    {metric.label}
                  </span>
                </div>
              </button>
            )
          })}
        </div>
      </div>

      <motion.div
        className="absolute inset-0 border-2 border-transparent z-30 rounded-xl pointer-events-none"
        animate={{
          borderColor: isHovered ? "rgba(59, 130, 246, 0.5)" : "rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  )
}
