"use client"

import { motion } from "framer-motion"

export default function TechStack() {
  const technologies = [
    { name: "React", icon: "/placeholder.svg?height=40&width=40" },
    { name: "Next.js", icon: "/placeholder.svg?height=40&width=40" },
    { name: "TypeScript", icon: "/placeholder.svg?height=40&width=40" },
    { name: "Node.js", icon: "/placeholder.svg?height=40&width=40" },
    { name: "Python", icon: "/placeholder.svg?height=40&width=40" },
    { name: "AWS", icon: "/placeholder.svg?height=40&width=40" },
    { name: "Docker", icon: "/placeholder.svg?height=40&width=40" },
    { name: "GraphQL", icon: "/placeholder.svg?height=40&width=40" },
    { name: "MongoDB", icon: "/placeholder.svg?height=40&width=40" },
    { name: "PostgreSQL", icon: "/placeholder.svg?height=40&width=40" },
  ]

  return (
    <section className="py-16 bg-gradient-to-b from-black to-gray-950">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h2 className="text-2xl md:text-3xl font-bold text-white">Our Technology Stack</h2>
          <p className="text-gray-400 mt-2">We use cutting-edge technologies to build exceptional software</p>
        </motion.div>

        <div className="flex flex-wrap justify-center gap-8 md:gap-12">
          {technologies.map((tech, index) => (
            <motion.div
              key={tech.name}
              className="flex flex-col items-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.1 }}
            >
              <div className="w-16 h-16 bg-white/10 rounded-xl flex items-center justify-center mb-2">
                <img src={tech.icon || "/placeholder.svg"} alt={tech.name} className="w-8 h-8" />
              </div>
              <span className="text-sm text-gray-300">{tech.name}</span>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
