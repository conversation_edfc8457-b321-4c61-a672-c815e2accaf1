"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { <PERSON>R<PERSON>, Check, Code, BarChart, Smartphone, Database, Zap, RefreshCw } from "lucide-react"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"

export default function DigitalTransformationPage() {
  return (
    <main className="pt-24 relative">
      <AnimatedBackground particleCount={50} className="opacity-30" />

      {/* Hero Section */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">Digital Transformation</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Reimagine Your Business for the Digital Age"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg mb-8">
              Transform your organization with innovative digital solutions that enhance efficiency, improve customer
              experiences, and drive sustainable growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/contact"
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center transition-colors group"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
              <Link
                href="#transformation-solutions"
                className="border border-gray-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center hover:bg-gray-800 transition-colors"
              >
                Explore Solutions
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Digital Transformation Framework */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Our Digital Transformation Framework</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We follow a comprehensive approach to digital transformation that addresses people, processes, and
              technology.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                title: "Assess",
                description:
                  "Evaluate your current digital maturity, identify opportunities, and define transformation goals.",
                icon: <BarChart className="h-8 w-8" />,
              },
              {
                title: "Strategize",
                description:
                  "Develop a tailored roadmap that aligns digital initiatives with your business objectives.",
                icon: <Zap className="h-8 w-8" />,
              },
              {
                title: "Implement",
                description: "Execute the transformation plan with agile methodologies and continuous feedback loops.",
                icon: <Code className="h-8 w-8" />,
              },
              {
                title: "Optimize",
                description: "Continuously measure, refine, and scale your digital capabilities for sustained success.",
                icon: <RefreshCw className="h-8 w-8" />,
              },
            ].map((step, index) => (
              <motion.div
                key={step.title}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-16 h-16 bg-yellow-900/30 rounded-lg flex items-center justify-center mb-6 text-yellow-400">
                  {step.icon}
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">{step.title}</h3>
                <p className="text-gray-300">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Transformation Solutions */}
      <section id="transformation-solutions" className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">Our Solutions</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Digital Transformation Solutions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We offer a comprehensive suite of digital transformation solutions tailored to your specific business
              needs.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Enterprise Application Modernization",
                description:
                  "Transform legacy systems into modern, scalable applications that enhance efficiency and reduce maintenance costs.",
                icon: <Code className="h-8 w-8" />,
                features: [
                  "Legacy system assessment",
                  "Cloud migration strategy",
                  "Microservices architecture",
                  "API development and integration",
                ],
              },
              {
                title: "Customer Experience Transformation",
                description:
                  "Create seamless, personalized customer experiences across all digital touchpoints to drive engagement and loyalty.",
                icon: <Smartphone className="h-8 w-8" />,
                features: [
                  "Customer journey mapping",
                  "Omnichannel experience design",
                  "Personalization engines",
                  "Customer analytics and insights",
                ],
              },
              {
                title: "Data-Driven Transformation",
                description:
                  "Leverage your data assets to gain actionable insights, improve decision-making, and create new business opportunities.",
                icon: <Database className="h-8 w-8" />,
                features: [
                  "Data strategy development",
                  "Data architecture modernization",
                  "Advanced analytics implementation",
                  "Data governance frameworks",
                ],
              },
              {
                title: "Digital Workplace Transformation",
                description:
                  "Create a modern workplace that enhances collaboration, productivity, and employee experience.",
                icon: <Zap className="h-8 w-8" />,
                features: [
                  "Collaboration platform implementation",
                  "Process automation",
                  "Knowledge management systems",
                  "Remote work enablement",
                ],
              },
              {
                title: "Business Process Automation",
                description:
                  "Automate manual processes to improve efficiency, reduce errors, and free up resources for higher-value activities.",
                icon: <RefreshCw className="h-8 w-8" />,
                features: [
                  "Process analysis and optimization",
                  "Workflow automation",
                  "Robotic process automation (RPA)",
                  "Integration with existing systems",
                ],
              },
              {
                title: "Digital Innovation Lab",
                description:
                  "Establish a dedicated innovation capability to explore, test, and scale new digital business models and technologies.",
                icon: <BarChart className="h-8 w-8" />,
                features: [
                  "Innovation strategy development",
                  "Rapid prototyping and MVP creation",
                  "Technology evaluation frameworks",
                  "Innovation portfolio management",
                ],
              },
            ].map((solution, index) => (
              <motion.div
                key={solution.title}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="p-6">
                  <div className="w-16 h-16 bg-yellow-900/30 rounded-lg flex items-center justify-center mb-6 text-yellow-400">
                    {solution.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-white">{solution.title}</h3>
                  <p className="text-gray-300 mb-6">{solution.description}</p>

                  <div className="bg-gray-800 p-4 rounded-lg mb-6">
                    <h4 className="text-white font-semibold mb-2">Key Features:</h4>
                    <ul className="space-y-2">
                      {solution.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <Check className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link
                    href={`/contact?solution=${encodeURIComponent(solution.title)}`}
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    Learn more
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">Success Stories</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Digital Transformation Case Studies</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover how our digital transformation solutions have helped organizations achieve their business
              objectives.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Digital Transformation for Manufacturing Giant",
                category: "Manufacturing",
                image: "/placeholder.svg?height=400&width=600",
                results: "30% increase in operational efficiency and 45% reduction in equipment downtime",
              },
              {
                title: "Omnichannel Platform for Retail Chain",
                category: "Retail",
                image: "/placeholder.svg?height=400&width=600",
                results: "120% increase in online sales and 35% improvement in customer retention",
              },
              {
                title: "Data-Driven Transformation for Financial Services",
                category: "Banking & Finance",
                image: "/placeholder.svg?height=400&width=600",
                results: "40% faster decision-making and 25% increase in cross-selling opportunities",
              },
            ].map((study, index) => (
              <motion.div
                key={study.title}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                  <div className="absolute top-0 left-0 bg-yellow-600 text-white text-xs px-3 py-1">
                    {study.category}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{study.title}</h3>
                  <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3 mb-4">
                    <div className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-400 font-medium">{study.results}</span>
                    </div>
                  </div>
                  <Link
                    href="/case-studies"
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    Read case study
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/case-studies"
              className="bg-transparent border border-yellow-500 text-yellow-400 hover:bg-yellow-900/20 px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              View All Case Studies
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Benefits of Digital Transformation</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Digital transformation delivers tangible benefits that drive business growth and competitive advantage.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Enhanced Customer Experience",
                description:
                  "Create seamless, personalized experiences across all customer touchpoints to increase satisfaction and loyalty.",
              },
              {
                title: "Operational Efficiency",
                description:
                  "Streamline processes, reduce manual effort, and optimize resource allocation to improve productivity and reduce costs.",
              },
              {
                title: "Data-Driven Decision Making",
                description:
                  "Leverage advanced analytics to gain actionable insights that inform strategic and operational decisions.",
              },
              {
                title: "Business Agility",
                description:
                  "Increase your organization's ability to adapt quickly to changing market conditions and customer needs.",
              },
              {
                title: "Innovation Acceleration",
                description:
                  "Create an environment that fosters innovation and enables rapid development and deployment of new ideas.",
              },
              {
                title: "Competitive Advantage",
                description:
                  "Differentiate your business through digital capabilities that deliver unique value to customers and stakeholders.",
              },
            ].map((benefit, index) => (
              <motion.div
                key={benefit.title}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-xl font-bold mb-3 text-white">{benefit.title}</h3>
                <p className="text-gray-300">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">FAQ</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Frequently Asked Questions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Get answers to common questions about digital transformation and our approach.
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            {[
              {
                question: "What is digital transformation?",
                answer:
                  "Digital transformation is the integration of digital technology into all areas of a business, fundamentally changing how you operate and deliver value to customers. It's also a cultural change that requires organizations to continually challenge the status quo, experiment, and get comfortable with failure.",
              },
              {
                question: "How long does a digital transformation initiative take?",
                answer:
                  "The timeline for digital transformation varies based on the scope, complexity, and organizational readiness. Some focused initiatives can show results in 3-6 months, while comprehensive enterprise-wide transformations typically span 1-3 years. We approach transformation as an iterative journey, delivering value incrementally rather than as a single big-bang project.",
              },
              {
                question: "How do you measure the success of digital transformation?",
                answer:
                  "We establish clear, measurable KPIs at the beginning of each transformation initiative, aligned with your business objectives. These might include metrics like operational efficiency, customer satisfaction, revenue growth, time-to-market, or employee productivity. We provide regular reporting on these metrics and continuously optimize the transformation to improve results.",
              },
              {
                question: "How do you handle change management during digital transformation?",
                answer:
                  "Change management is a critical component of our approach. We address the people side of transformation through stakeholder engagement, communication planning, training programs, and adoption measurement. Our change management methodology ensures that employees understand the why behind the transformation, have the skills to succeed in the new environment, and are motivated to embrace the changes.",
              },
              {
                question: "What technologies do you typically use in digital transformation projects?",
                answer:
                  "We're technology-agnostic and select the best tools for your specific needs. Common technologies in our digital transformation projects include cloud platforms (AWS, Azure, Google Cloud), modern application frameworks, data analytics tools, automation platforms, and collaboration solutions. Our focus is on selecting technologies that align with your business goals, integrate with your existing systems, and provide long-term value.",
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800 mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-xl font-bold mb-3 text-white">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Ready to Transform Your Business?</h2>
                <p className="text-gray-300 text-lg">
                  Contact us today to discuss how our digital transformation solutions can help you achieve your
                  business objectives.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg whitespace-nowrap"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
