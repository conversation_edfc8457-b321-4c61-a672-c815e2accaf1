"use client"

import { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, AlertTriangle } from "lucide-react"

export default function CybersecurityThreatMap() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [threatCount, setThreatCount] = useState(0)
  const [blockedCount, setBlockedCount] = useState(0)
  const [isHovered, setIsHovered] = useState(false)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // World map coordinates (simplified)
    const worldMap = [
      // North America
      { x: 0.2, y: 0.35 },
      { x: 0.25, y: 0.3 },
      { x: 0.3, y: 0.35 },
      { x: 0.25, y: 0.4 },
      // South America
      { x: 0.3, y: 0.6 },
      { x: 0.25, y: 0.65 },
      { x: 0.3, y: 0.7 },
      // Europe
      { x: 0.45, y: 0.3 },
      { x: 0.5, y: 0.25 },
      { x: 0.55, y: 0.3 },
      // Africa
      { x: 0.5, y: 0.5 },
      { x: 0.45, y: 0.55 },
      { x: 0.5, y: 0.6 },
      // Asia
      { x: 0.7, y: 0.35 },
      { x: 0.75, y: 0.3 },
      { x: 0.8, y: 0.35 },
      { x: 0.75, y: 0.4 },
      // Australia
      { x: 0.8, y: 0.65 },
      { x: 0.85, y: 0.7 },
    ]

    // Convert relative coordinates to absolute
    const nodes = worldMap.map((point) => ({
      x: point.x * canvas.width,
      y: point.y * canvas.height,
      radius: 3,
      connections: [],
      pulseRadius: 0,
      pulseOpacity: 0,
      isPulsing: false,
    }))

    // Create connections between nodes
    nodes.forEach((node) => {
      nodes.forEach((otherNode) => {
        if (node !== otherNode) {
          const dx = node.x - otherNode.x
          const dy = node.y - otherNode.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < canvas.width * 0.2) {
            node.connections.push(otherNode)
          }
        }
      })
    })

    // Attack class
    class Attack {
      startNode: (typeof nodes)[0]
      endNode: (typeof nodes)[0]
      progress: number
      speed: number
      color: string
      isBlocked: boolean
      blockPoint: number

      constructor() {
        // Random start and end nodes
        this.startNode = nodes[Math.floor(Math.random() * nodes.length)]

        // Ensure end node is different and has a connection
        do {
          this.endNode = nodes[Math.floor(Math.random() * nodes.length)]
        } while (this.endNode === this.startNode || !this.startNode.connections.includes(this.endNode))

        this.progress = 0
        this.speed = 0.005 + Math.random() * 0.01
        this.color = `hsl(${Math.random() * 60}, 100%, 50%)`
        this.isBlocked = Math.random() > 0.3 // 70% chance of being blocked
        this.blockPoint = this.isBlocked ? 0.4 + Math.random() * 0.3 : 1
      }

      update() {
        if (this.progress < this.blockPoint) {
          this.progress += this.speed

          if (this.isBlocked && this.progress >= this.blockPoint) {
            // Trigger pulse animation on block
            this.startNode.isPulsing = true
            this.startNode.pulseRadius = 0
            this.startNode.pulseOpacity = 1

            // Increment counters
            setBlockedCount((prev) => prev + 1)
          }

          if (!this.isBlocked && this.progress >= 1) {
            // Attack reached target
            this.endNode.isPulsing = true
            this.endNode.pulseRadius = 0
            this.endNode.pulseOpacity = 1

            // Increment threat counter
            setThreatCount((prev) => prev + 1)
          }
        }

        return this.progress < this.blockPoint
      }

      draw() {
        const startX = this.startNode.x
        const startY = this.startNode.y
        const endX = this.endNode.x
        const endY = this.endNode.y

        const currentX = startX + (endX - startX) * this.progress
        const currentY = startY + (endY - startY) * this.progress

        // Draw attack line
        ctx.beginPath()
        ctx.moveTo(startX, startY)
        ctx.lineTo(currentX, currentY)
        ctx.strokeStyle = this.color
        ctx.lineWidth = 2
        ctx.stroke()

        // Draw attack head
        ctx.beginPath()
        ctx.arc(currentX, currentY, 4, 0, Math.PI * 2)
        ctx.fillStyle = this.color
        ctx.fill()

        // Draw block symbol if blocked
        if (this.isBlocked && this.progress >= this.blockPoint) {
          const blockX = startX + (endX - startX) * this.blockPoint
          const blockY = startY + (endY - startY) * this.blockPoint

          ctx.beginPath()
          ctx.arc(blockX, blockY, 8, 0, Math.PI * 2)
          ctx.fillStyle = "rgba(0, 255, 0, 0.7)"
          ctx.fill()

          ctx.beginPath()
          ctx.moveTo(blockX - 5, blockY)
          ctx.lineTo(blockX + 5, blockY)
          ctx.strokeStyle = "rgba(0, 0, 0, 0.8)"
          ctx.lineWidth = 2
          ctx.stroke()
        }
      }
    }

    // Attacks array
    const attacks: Attack[] = []

    // Generate new attack
    const generateAttack = () => {
      if (attacks.length < 15) {
        attacks.push(new Attack())
      }
    }

    // Initial attacks
    for (let i = 0; i < 5; i++) {
      generateAttack()
    }

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw connections
      ctx.globalAlpha = 0.2
      nodes.forEach((node) => {
        node.connections.forEach((connectedNode) => {
          ctx.beginPath()
          ctx.moveTo(node.x, node.y)
          ctx.lineTo(connectedNode.x, connectedNode.y)
          ctx.strokeStyle = "#ffffff"
          ctx.lineWidth = 1
          ctx.stroke()
        })
      })

      // Draw nodes
      ctx.globalAlpha = 1
      nodes.forEach((node) => {
        // Draw node
        ctx.beginPath()
        ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2)
        ctx.fillStyle = "#ffffff"
        ctx.fill()

        // Draw pulse if active
        if (node.isPulsing) {
          ctx.beginPath()
          ctx.arc(node.x, node.y, node.pulseRadius, 0, Math.PI * 2)
          ctx.fillStyle = `rgba(255, 0, 0, ${node.pulseOpacity})`
          ctx.fill()

          // Update pulse
          node.pulseRadius += 1
          node.pulseOpacity -= 0.02

          if (node.pulseOpacity <= 0) {
            node.isPulsing = false
          }
        }
      })

      // Update and draw attacks
      for (let i = attacks.length - 1; i >= 0; i--) {
        const isActive = attacks[i].update()
        attacks[i].draw()

        if (!isActive) {
          attacks.splice(i, 1)
        }
      }

      // Generate new attack randomly
      if (Math.random() < 0.03) {
        generateAttack()
      }

      requestAnimationFrame(animate)
    }

    animate()

    // Handle window resize
    const handleResize = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
    }

    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  return (
    <motion.div
      className="relative w-full h-96 bg-gradient-to-br from-gray-900 to-black rounded-xl overflow-hidden shadow-2xl"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm z-10"></div>
      <canvas ref={canvasRef} className="absolute inset-0 z-0"></canvas>

      <div className="absolute top-0 left-0 right-0 p-6 z-20">
        <h3 className="text-xl font-bold text-white mb-2">Live Threat Detection</h3>
        <p className="text-gray-300 text-sm">
          Real-time visualization of global cyber threats and our protection system
        </p>
      </div>

      <div className="absolute bottom-0 left-0 right-0 p-6 flex justify-between items-center z-20">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center mr-3">
            <AlertTriangle className="h-5 w-5 text-red-500" />
          </div>
          <div>
            <p className="text-xs text-gray-400">Threats Detected</p>
            <p className="text-xl font-bold text-white">{threatCount}</p>
          </div>
        </div>

        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center mr-3">
            <Shield className="h-5 w-5 text-green-500" />
          </div>
          <div>
            <p className="text-xs text-gray-400">Threats Blocked</p>
            <p className="text-xl font-bold text-white">{blockedCount}</p>
          </div>
        </div>
      </div>

      <motion.div
        className="absolute inset-0 border-2 border-transparent z-30 rounded-xl pointer-events-none"
        animate={{
          borderColor: isHovered ? "rgba(59, 130, 246, 0.5)" : "rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  )
}
