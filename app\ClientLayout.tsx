"use client"

import type React from "react"
import { ThemeProvider } from "@/components/theme-provider"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import WhatsAppChatButton from "@/components/whatsapp-chat-button"
import MobileNavigation from "@/components/mobile-navigation"
import { useIsMobile } from "@/hooks/use-mobile"
import AppShell from "@/components/app-shell"

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const isMobile = useIsMobile()

  return (
    <ThemeProvider attribute="class" defaultTheme="dark" enableSystem disableTransitionOnChange>
      <div className="flex flex-col min-h-screen bg-black text-white">
        {!isMobile && <Navbar />}
        <AppShell>
          <main className="flex-1 w-full">{children}</main>
        </AppShell>
        <Footer />
        <WhatsAppChatButton />
        {isMobile && <MobileNavigation />}
      </div>
    </ThemeProvider>
  )
}
