"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { <PERSON>R<PERSON>, Check, Shield, Lock, AlertTriangle, Eye, FileText, Server } from "lucide-react"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"
import CybersecurityThreatMap from "@/components/cybersecurity-threat-map"

export default function CybersecurityPage() {
  return (
    <main className="pt-24 relative">
      <AnimatedBackground particleCount={50} className="opacity-30" />

      {/* Hero Section */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">Enterprise Cybersecurity</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Comprehensive Protection for Your Digital Assets"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg mb-8">
              Protect your organization with advanced cybersecurity solutions designed to detect, prevent, and respond
              to evolving threats in today's complex digital landscape.
            </p>
            <div className="mt-8 relative h-64 w-full md:h-80 md:w-3/4 mx-auto rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=800&width=1200"
                alt="Enterprise Cybersecurity"
                fill
                className="object-cover rounded-lg border border-yellow-800/30"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
              <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm px-3 py-1 rounded text-xs text-gray-300">
                Advanced threat protection and security monitoring
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/contact"
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center transition-colors group"
              >
                Schedule a Security Assessment
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
              <Link
                href="#security-solutions"
                className="border border-gray-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center hover:bg-gray-800 transition-colors"
              >
                Explore Security Solutions
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Threat Map */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Global Threat Landscape</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our real-time threat intelligence monitors and responds to emerging cybersecurity threats worldwide.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <CybersecurityThreatMap />
          </div>
        </div>
      </section>

      {/* Security Solutions */}
      <section id="security-solutions" className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">Our Solutions</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Comprehensive Security Solutions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We offer a full spectrum of cybersecurity services designed to protect your organization at every level.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {[
              {
                title: "Threat Detection",
                image: "/placeholder.svg?height=400&width=600",
                description: "Real-time monitoring and rapid response to security incidents",
              },
              {
                title: "Zero Trust Architecture",
                image: "/placeholder.svg?height=400&width=600",
                description: "Verification for every person and device attempting to access resources",
              },
              {
                title: "Vulnerability Management",
                image: "/placeholder.svg?height=400&width=600",
                description: "Proactive identification and remediation of security vulnerabilities",
              },
            ].map((item, index) => (
              <motion.div
                key={index}
                className="relative overflow-hidden rounded-lg group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image
                    src={item.image || "/placeholder.svg"}
                    alt={item.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <h3 className="text-lg font-bold text-white mb-1">{item.title}</h3>
                  <p className="text-gray-300 text-sm">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Threat Detection & Response",
                description:
                  "Real-time monitoring and rapid response to security incidents to minimize potential damage and data loss.",
                icon: <AlertTriangle className="h-8 w-8" />,
                features: [
                  "24/7 security monitoring",
                  "Automated threat detection",
                  "Incident response planning",
                  "Security information and event management (SIEM)",
                ],
              },
              {
                title: "Zero Trust Architecture",
                description:
                  "Implementation of security models that require verification for every person and device attempting to access resources.",
                icon: <Lock className="h-8 w-8" />,
                features: [
                  "Identity and access management",
                  "Micro-segmentation",
                  "Least privilege access",
                  "Continuous monitoring and validation",
                ],
              },
              {
                title: "Vulnerability Management",
                description:
                  "Proactive identification and remediation of security vulnerabilities across your IT infrastructure.",
                icon: <Eye className="h-8 w-8" />,
                features: [
                  "Comprehensive vulnerability scanning",
                  "Risk assessment and prioritization",
                  "Remediation guidance",
                  "Continuous monitoring",
                ],
              },
              {
                title: "Security Compliance",
                description:
                  "Ensure your organization meets industry standards and regulatory requirements for data protection and privacy.",
                icon: <FileText className="h-8 w-8" />,
                features: [
                  "GDPR, HIPAA, and PCI DSS compliance",
                  "Security policy development",
                  "Regular compliance audits",
                  "Staff security awareness training",
                ],
              },
              {
                title: "Cloud Security",
                description:
                  "Protect your cloud infrastructure and data with comprehensive security solutions designed for modern cloud environments.",
                icon: <Server className="h-8 w-8" />,
                features: [
                  "Cloud security posture management",
                  "Data protection and encryption",
                  "Identity and access management",
                  "Threat detection and response",
                ],
              },
              {
                title: "Security Assessment",
                description:
                  "Comprehensive evaluation of your security posture to identify vulnerabilities and recommend improvements.",
                icon: <Shield className="h-8 w-8" />,
                features: [
                  "Penetration testing",
                  "Security architecture review",
                  "Risk assessment",
                  "Security roadmap development",
                ],
              },
            ].map((solution, index) => (
              <motion.div
                key={solution.title}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="p-6">
                  <div className="w-16 h-16 bg-yellow-900/30 rounded-lg flex items-center justify-center mb-6 text-yellow-400">
                    {solution.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-white">{solution.title}</h3>
                  <p className="text-gray-300 mb-6">{solution.description}</p>

                  <div className="bg-gray-800 p-4 rounded-lg mb-6">
                    <h4 className="text-white font-semibold mb-2">Key Features:</h4>
                    <ul className="space-y-2">
                      {solution.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <Check className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link
                    href={`/contact?solution=${encodeURIComponent(solution.title)}`}
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    Learn more
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">Success Stories</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Cybersecurity Case Studies</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover how our cybersecurity solutions have helped organizations protect their digital assets and ensure
              business continuity.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Secure Cloud Migration for Healthcare Provider",
                category: "Healthcare",
                image: "/placeholder.svg?height=400&width=600",
                results: "99.99% uptime with zero security breaches while ensuring HIPAA compliance",
              },
              {
                title: "Cybersecurity Enhancement for Government Agency",
                category: "Government",
                image: "/placeholder.svg?height=400&width=600",
                results: "100% prevention of critical security threats and enhanced protection of sensitive data",
              },
              {
                title: "Financial Institution Security Transformation",
                category: "Banking & Finance",
                image: "/placeholder.svg?height=400&width=600",
                results: "90% reduction in security incidents and full regulatory compliance",
              },
            ].map((study, index) => (
              <motion.div
                key={study.title}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                  <div className="absolute top-0 left-0 bg-yellow-600 text-white text-xs px-3 py-1">
                    {study.category}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{study.title}</h3>
                  <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3 mb-4">
                    <div className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-400 font-medium">{study.results}</span>
                    </div>
                  </div>
                  <Link
                    href="/case-studies"
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    Read case study
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/case-studies"
              className="bg-transparent border border-yellow-500 text-yellow-400 hover:bg-yellow-900/20 px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              View All Case Studies
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Security Approach */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Our Security Approach</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We follow a comprehensive, risk-based approach to cybersecurity that addresses people, processes, and
              technology.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Image
                src="/placeholder.svg?height=800&width=1200"
                alt="Security Approach"
                width={800}
                height={600}
                className="rounded-lg border border-gray-800 shadow-lg shadow-yellow-900/20"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-bold mb-3 text-white">1. Assess</h3>
                  <p className="text-gray-300">
                    We begin with a comprehensive assessment of your current security posture, identifying
                    vulnerabilities, threats, and compliance requirements.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold mb-3 text-white">2. Design</h3>
                  <p className="text-gray-300">
                    Based on the assessment, we design a tailored security strategy and architecture that aligns with
                    your business objectives and risk tolerance.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold mb-3 text-white">3. Implement</h3>
                  <p className="text-gray-300">
                    We deploy security solutions and controls, integrating them with your existing systems and processes
                    to minimize disruption.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold mb-3 text-white">4. Manage</h3>
                  <p className="text-gray-300">
                    Our team provides ongoing management, monitoring, and response to ensure your security posture
                    remains strong against evolving threats.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-bold mb-3 text-white">5. Optimize</h3>
                  <p className="text-gray-300">
                    We continuously evaluate and improve your security program, adapting to new threats, technologies,
                    and business requirements.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">FAQ</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Frequently Asked Questions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Get answers to common questions about our cybersecurity solutions and approach.
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            {[
              {
                question: "How do I know if my organization needs cybersecurity services?",
                answer:
                  "Every organization that uses digital systems, stores sensitive data, or connects to the internet needs cybersecurity protection. The level and type of security required depends on factors like your industry, regulatory requirements, data sensitivity, and threat landscape. We recommend a security assessment to evaluate your specific needs.",
              },
              {
                question: "What industries do you provide cybersecurity services for?",
                answer:
                  "We provide cybersecurity services across all industries, with specialized expertise in highly regulated sectors such as healthcare, financial services, government, and critical infrastructure. Our solutions are tailored to address the unique security challenges and compliance requirements of each industry.",
              },
              {
                question: "How do you stay current with evolving cyber threats?",
                answer:
                  "Our security team continuously monitors global threat intelligence feeds, participates in industry forums, and conducts ongoing research to stay ahead of emerging threats. We regularly update our security tools, methodologies, and knowledge base to ensure we're prepared to defend against the latest attack vectors.",
              },
              {
                question: "What is your incident response process?",
                answer:
                  "Our incident response process follows industry best practices: 1) Identification and containment of the threat, 2) Eradication of the threat, 3) Recovery of affected systems, 4) Post-incident analysis, and 5) Improvements to prevent similar incidents. We provide 24/7 response capabilities with defined SLAs based on incident severity.",
              },
              {
                question: "How do you help with regulatory compliance?",
                answer:
                  "We help organizations achieve and maintain compliance with various regulations (GDPR, HIPAA, PCI DSS, etc.) through comprehensive security assessments, implementation of required controls, documentation of security practices, regular compliance audits, and remediation support. Our approach ensures both technical compliance and the ability to demonstrate compliance to auditors.",
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800 mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-xl font-bold mb-3 text-white">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Ready to Secure Your Organization?</h2>
                <p className="text-gray-300 text-lg">
                  Contact us today to schedule a security assessment and discover how our cybersecurity solutions can
                  protect your business.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg whitespace-nowrap"
              >
                Schedule a Security Assessment
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
