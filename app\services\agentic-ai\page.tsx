"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { <PERSON>R<PERSON>, Check, ExternalLink, Zap } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"

export default function AgenticAIPage() {
  return (
    <main className="pt-24 bg-black">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <AnimatedBackground particleCount={50} className="opacity-30" />
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full"
              >
                <span className="text-yellow-400 text-sm font-medium">Advanced AI Solutions</span>
              </motion.div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">
                <AnimatedText
                  text="Agentic AI Systems"
                  className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400"
                />
              </h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-xl text-gray-300 mb-8"
              >
                Autonomous AI agents that can perform complex tasks, make decisions, and interact with other systems
                with minimal human intervention.
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="flex flex-wrap gap-4"
              >
                <Button
                  asChild
                  className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-3 rounded-md font-medium"
                >
                  <Link href="/contact">
                    Schedule a Consultation
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  className="border-gray-600 text-white hover:bg-gray-800 px-8 py-3 rounded-md font-medium"
                >
                  <Link href="/case-studies">View Case Studies</Link>
                </Button>
              </motion.div>
            </div>
            <div className="lg:w-1/2">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="relative rounded-lg overflow-hidden shadow-2xl"
              >
                <Image
                  src="https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=2940&auto=format&fit=crop"
                  alt="Agentic AI Systems"
                  width={600}
                  height={400}
                  className="w-full h-auto"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                <div className="absolute bottom-0 left-0 p-6">
                  <span className="bg-yellow-600 text-white text-xs px-3 py-1 rounded-full">
                    Next-Gen AI Technology
                  </span>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mb-4 inline-block bg-yellow-900/30 px-4 py-1 rounded-full"
            >
              <span className="text-yellow-400 text-sm font-medium">Key Capabilities</span>
            </motion.div>
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl font-bold mb-4 text-white"
            >
              Features of Our Agentic AI Systems
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-xl text-gray-400 max-w-3xl mx-auto"
            >
              Discover the powerful capabilities that make our agentic AI systems transformative for businesses.
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="bg-gray-900 p-8 rounded-lg border border-gray-800"
            >
              <div className="bg-yellow-900/30 p-3 rounded-full w-14 h-14 flex items-center justify-center mb-6">
                <Zap className="h-7 w-7 text-yellow-400" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">Autonomous Decision-Making</h3>
              <p className="text-gray-400 mb-6">
                AI agents capable of making complex decisions based on real-time data and predefined objectives without
                human intervention.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Real-time data processing</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Objective-driven decision frameworks</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Configurable risk tolerance</span>
                </li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-gray-900 p-8 rounded-lg border border-gray-800"
            >
              <div className="bg-yellow-900/30 p-3 rounded-full w-14 h-14 flex items-center justify-center mb-6">
                <ExternalLink className="h-7 w-7 text-yellow-400" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">Multi-Agent Coordination</h3>
              <p className="text-gray-400 mb-6">
                Orchestrate multiple AI agents working together to solve complex problems through coordinated actions
                and information sharing.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Agent-to-agent communication</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Task delegation and prioritization</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Conflict resolution mechanisms</span>
                </li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-gray-900 p-8 rounded-lg border border-gray-800"
            >
              <div className="bg-yellow-900/30 p-3 rounded-full w-14 h-14 flex items-center justify-center mb-6">
                <ArrowRight className="h-7 w-7 text-yellow-400" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">Goal-Oriented Behavior</h3>
              <p className="text-gray-400 mb-6">
                AI systems that persistently work toward achieving defined objectives, adapting strategies as conditions
                change.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Hierarchical goal structures</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Dynamic strategy adjustment</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Progress monitoring and reporting</span>
                </li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-gray-900 p-8 rounded-lg border border-gray-800"
            >
              <div className="bg-yellow-900/30 p-3 rounded-full w-14 h-14 flex items-center justify-center mb-6">
                <Zap className="h-7 w-7 text-yellow-400" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">Self-Improvement</h3>
              <p className="text-gray-400 mb-6">
                AI agents that learn from experience and continuously improve their performance over time without manual
                updates.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Reinforcement learning capabilities</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Performance analytics and optimization</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Automated model refinement</span>
                </li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-gray-900 p-8 rounded-lg border border-gray-800"
            >
              <div className="bg-yellow-900/30 p-3 rounded-full w-14 h-14 flex items-center justify-center mb-6">
                <ExternalLink className="h-7 w-7 text-yellow-400" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">System Integration</h3>
              <p className="text-gray-400 mb-6">
                Seamlessly connect with existing business systems and data sources to provide comprehensive automation
                solutions.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">API-based connectivity</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Database and legacy system compatibility</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Secure data exchange protocols</span>
                </li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-gray-900 p-8 rounded-lg border border-gray-800"
            >
              <div className="bg-yellow-900/30 p-3 rounded-full w-14 h-14 flex items-center justify-center mb-6">
                <ArrowRight className="h-7 w-7 text-yellow-400" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">Human Oversight</h3>
              <p className="text-gray-400 mb-6">
                Maintain appropriate human supervision with configurable controls, transparency, and intervention
                mechanisms.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Customizable approval workflows</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Decision explanation capabilities</span>
                </li>
                <li className="flex items-start">
                  <Check className="h-5 w-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-300">Emergency override systems</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Implement Agentic AI in Your Business?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how our autonomous AI agents can transform your operations and drive innovation.
                </p>
              </div>
              <Button
                asChild
                size="lg"
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-6 rounded-md font-medium text-lg whitespace-nowrap"
              >
                <Link href="/contact">
                  Schedule a Consultation
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
