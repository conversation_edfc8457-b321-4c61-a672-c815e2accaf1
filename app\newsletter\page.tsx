"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { ArrowRight, Mail, Check, Globe, Zap, BookOpen, Bell } from "lucide-react"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"

export default function NewsletterPage() {
  const [email, setEmail] = useState("")
  const [name, setName] = useState("")
  const [interests, setInterests] = useState<string[]>([])
  const [submitted, setSubmitted] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleInterestToggle = (interest: string) => {
    if (interests.includes(interest)) {
      setInterests(interests.filter((i) => i !== interest))
    } else {
      setInterests([...interests, interest])
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    // Simulate API call
    setTimeout(() => {
      setLoading(false)
      setSubmitted(true)
    }, 1500)
  }

  const interestOptions = [
    { id: "ai", label: "Artificial Intelligence", icon: <Zap className="h-4 w-4" /> },
    { id: "cybersecurity", label: "Cybersecurity", icon: <Shield className="h-4 w-4" /> },
    { id: "digital-transformation", label: "Digital Transformation", icon: <Code className="h-4 w-4" /> },
    { id: "cloud", label: "Cloud Computing", icon: <Database className="h-4 w-4" /> },
    { id: "tech-trends", label: "Tech Trends", icon: <Globe className="h-4 w-4" /> },
    { id: "case-studies", label: "Case Studies", icon: <BookOpen className="h-4 w-4" /> },
  ]

  return (
    <main className="pt-24 relative bg-gray-950">
      <AnimatedBackground particleCount={30} className="opacity-20" />

      {/* Header */}
      <section className="bg-gray-900 text-white py-20 relative overflow-hidden border-b border-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="inline-block bg-blue-900/30 px-4 py-1 rounded-full mb-4">
              <span className="text-blue-400 text-sm font-medium">Stay Informed</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText text=".Lunar Enterprise Insights" className="text-white" delay={0.2} />
            </h1>
            <p className="text-gray-300 text-lg">
              Subscribe to our newsletter for expert analysis, industry trends, and strategic insights on enterprise
              technology.
            </p>
          </div>
        </div>
      </section>

      {/* Newsletter Benefits */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 h-full">
                <div className="w-12 h-12 bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                  <Zap className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">Industry Insights</h3>
                <p className="text-gray-300">
                  Gain access to expert analysis and insights on emerging technologies and industry trends.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 h-full">
                <div className="w-12 h-12 bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                  <BookOpen className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">Case Studies</h3>
                <p className="text-gray-300">
                  Learn from real-world implementation stories and success strategies from leading organizations.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 h-full">
                <div className="w-12 h-12 bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                  <Bell className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">Early Updates</h3>
                <p className="text-gray-300">
                  Be the first to know about new technologies, services, and events in the enterprise technology space.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Subscription Form */}
      <section className="py-20 bg-gray-900 border-y border-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            <div className="bg-gray-800 p-8 rounded-lg border border-gray-700">
              {!submitted ? (
                <>
                  <h2 className="text-3xl font-bold mb-6 text-white text-center">Subscribe to Our Newsletter</h2>
                  <p className="text-gray-300 mb-8 text-center">
                    Join industry leaders who rely on our insights to stay ahead in the rapidly evolving technology
                    landscape.
                  </p>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                        Business Email
                      </label>
                      <input
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-white"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-3">
                        Areas of Interest (Optional)
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {interestOptions.map((option) => (
                          <button
                            key={option.id}
                            type="button"
                            onClick={() => handleInterestToggle(option.id)}
                            className={`flex items-center p-3 rounded-lg border ${
                              interests.includes(option.id)
                                ? "bg-blue-900/30 border-blue-500 text-white"
                                : "bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
                            } transition-colors`}
                          >
                            <div className="mr-2">{option.icon}</div>
                            <span className="text-sm">{option.label}</span>
                          </button>
                        ))}
                      </div>
                    </div>

                    <div className="flex items-start">
                      <input
                        id="privacy"
                        type="checkbox"
                        className="h-4 w-4 mt-1 border-gray-600 rounded bg-gray-700 text-blue-500 focus:ring-blue-500"
                        required
                      />
                      <label htmlFor="privacy" className="ml-2 block text-sm text-gray-300">
                        I agree to receive the .Lunar newsletter and understand I can unsubscribe at any time. View our{" "}
                        <a href="/privacy-policy" className="text-blue-400 hover:underline">
                          Privacy Policy
                        </a>
                        .
                      </label>
                    </div>

                    <div>
                      <button
                        type="submit"
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium inline-flex items-center justify-center transition-colors"
                        disabled={loading}
                      >
                        {loading ? (
                          <span className="flex items-center">
                            <svg
                              className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            Subscribing...
                          </span>
                        ) : (
                          <>
                            <span>Subscribe to Newsletter</span>
                            <Mail className="ml-2 h-5 w-5" />
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                </>
              ) : (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Check className="h-8 w-8 text-white" />
                  </div>
                  <h2 className="text-3xl font-bold mb-4 text-white">Thank You for Subscribing!</h2>
                  <p className="text-gray-300 mb-6">
                    You've successfully subscribed to the .Lunar Enterprise Insights newsletter. We've sent a
                    confirmation email to <span className="text-white">{email}</span>.
                  </p>
                  <p className="text-gray-400 text-sm mb-8">
                    Please check your inbox and confirm your subscription to start receiving our newsletter.
                  </p>
                  <button
                    onClick={() => {
                      setSubmitted(false)
                      setEmail("")
                      setName("")
                      setInterests([])
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
                  >
                    Subscribe Another Email
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Preview */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-blue-900/30 px-4 py-1 rounded-full mb-4">
              <span className="text-blue-400 text-sm font-medium">Preview</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Sample Newsletter Content</h2>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Here's a sample of what you can expect in our monthly enterprise insights newsletter.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
              <div className="border-b border-gray-700 pb-6 mb-6">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h3 className="text-2xl font-bold text-white">.Lunar Enterprise Insights</h3>
                    <p className="text-gray-400 text-sm">March 2025 Edition</p>
                  </div>
                  <div className="text-blue-400 font-bold text-lg">
                    <span className="text-blue-400">.</span>Lunar
                  </div>
                </div>
              </div>

              <div className="space-y-8">
                <div>
                  <h4 className="text-xl font-bold mb-3 text-white">The Future of Enterprise AI: Beyond Automation</h4>
                  <p className="text-gray-300 mb-3">
                    As AI continues to evolve, enterprises are moving beyond simple automation to implement intelligent
                    systems that can make decisions, predict outcomes, and drive strategic initiatives. In this article,
                    we explore how leading organizations are leveraging advanced AI to transform their operations.
                  </p>
                  <a href="#" className="text-blue-400 inline-flex items-center hover:underline">
                    Read More <ArrowRight className="ml-1 h-4 w-4" />
                  </a>
                </div>

                <div className="border-t border-gray-700 pt-8">
                  <h4 className="text-xl font-bold mb-3 text-white">Case Study: Digital Transformation in Banking</h4>
                  <p className="text-gray-300 mb-3">
                    Learn how a global financial institution implemented our cloud-native platform to modernize their
                    core banking systems, resulting in 60% faster transaction processing and enhanced security
                    compliance.
                  </p>
                  <a href="#" className="text-blue-400 inline-flex items-center hover:underline">
                    Read More <ArrowRight className="ml-1 h-4 w-4" />
                  </a>
                </div>

                <div className="border-t border-gray-700 pt-8">
                  <h4 className="text-xl font-bold mb-3 text-white">
                    Industry Trend: Zero Trust Security Architecture
                  </h4>
                  <p className="text-gray-300 mb-3">
                    With the rise of remote work and cloud adoption, traditional security perimeters are no longer
                    sufficient. Discover how zero trust architecture is becoming the new standard for enterprise
                    security.
                  </p>
                  <a href="#" className="text-blue-400 inline-flex items-center hover:underline">
                    Read More <ArrowRight className="ml-1 h-4 w-4" />
                  </a>
                </div>
              </div>

              <div className="border-t border-gray-700 mt-8 pt-8 text-center">
                <p className="text-gray-400 text-sm mb-4">
                  You're receiving this email because you subscribed to the .Lunar Enterprise Insights newsletter.
                </p>
                <div className="flex justify-center space-x-4">
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    <span className="sr-only">LinkedIn</span>
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                    </svg>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white transition-colors">
                    <span className="sr-only">Twitter</span>
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-900 border-t border-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-blue-900/30 px-4 py-1 rounded-full mb-4">
              <span className="text-blue-400 text-sm font-medium">FAQ</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Frequently Asked Questions</h2>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 mb-6">
              <h3 className="text-xl font-bold mb-3 text-white">How often will I receive the newsletter?</h3>
              <p className="text-gray-300">
                The .Lunar Enterprise Insights newsletter is sent once a month, typically in the first week.
                Occasionally, we may send special editions for major announcements or industry-changing events.
              </p>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 mb-6">
              <h3 className="text-xl font-bold mb-3 text-white">Is the newsletter free?</h3>
              <p className="text-gray-300">
                Yes, the .Lunar Enterprise Insights newsletter is completely free. We believe in sharing knowledge and
                insights with our community without any cost barriers.
              </p>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 mb-6">
              <h3 className="text-xl font-bold mb-3 text-white">Can I customize the content I receive?</h3>
              <p className="text-gray-300">
                When you subscribe, you can select your areas of interest. While everyone receives our core content,
                we'll prioritize stories and insights that match your selected interests.
              </p>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
              <h3 className="text-xl font-bold mb-3 text-white">How do I unsubscribe?</h3>
              <p className="text-gray-300">
                You can unsubscribe at any time by clicking the "Unsubscribe" link at the bottom of any newsletter
                email. Your privacy is important to us, and we make it easy to opt out whenever you wish.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-blue-900/30 to-blue-800/30 rounded-lg p-8 md:p-12 border border-blue-800/50 max-w-4xl mx-auto">
            <div className="text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Stay Ahead of Industry Trends</h2>
              <p className="text-gray-300 text-lg mb-8">
                Join thousands of enterprise leaders who rely on our insights to navigate the rapidly evolving
                technology landscape.
              </p>
              <button
                onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors"
              >
                Subscribe Now
                <ArrowRight className="ml-2 h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

function Shield({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
    </svg>
  )
}

function Code({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="16 18 22 12 16 6"></polyline>
      <polyline points="8 6 2 12 8 18"></polyline>
    </svg>
  )
}

function Database({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
      <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
      <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
    </svg>
  )
}
