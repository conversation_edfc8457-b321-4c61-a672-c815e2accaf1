import Link from "next/link"
import { Facebook, Twitter, Instagram, Linkedin, Github, MapPin, Mail, Phone, Globe, ArrowRight } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-black text-white border-t border-gray-800/50">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="inline-flex items-center space-x-2 group">
              <div className="relative">
                <span className="text-2xl font-bold text-white group-hover:text-yellow-500 transition-colors duration-300">
                  Lunar
                </span>
                <span className="absolute -top-1 -right-1 text-yellow-500 text-sm font-bold">.</span>
              </div>
            </Link>
            <p className="mt-6 text-gray-400 leading-relaxed">
              Enterprise-grade software solutions for businesses worldwide. We deliver innovative technology services
              that drive digital transformation and business growth.
            </p>
            
            {/* Social Links */}
            <div className="flex space-x-4 mt-8">
              <a 
                href="#" 
                className="w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-gray-800 transition-all duration-200"
                aria-label="Facebook"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-gray-800 transition-all duration-200"
                aria-label="Twitter"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a 
                href="https://www.instagram.com/lunarsoftwarehouse/" 
                className="w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-gray-800 transition-all duration-200"
                aria-label="Instagram"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-gray-800 transition-all duration-200"
                aria-label="LinkedIn"
              >
                <Linkedin className="h-5 w-5" />
              </a>
              <a 
                href="https://github.com/lunarstudio2025" 
                className="w-10 h-10 rounded-lg bg-gray-800/50 flex items-center justify-center text-gray-400 hover:text-yellow-500 hover:bg-gray-800 transition-all duration-200"
                aria-label="GitHub"
              >
                <Github className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Services</h3>
            <ul className="space-y-3">
              <li>
                <Link 
                  href="/services/ai-solutions" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  AI Solutions
                </Link>
              </li>
              <li>
                <Link 
                  href="/services/cybersecurity" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  Cybersecurity
                </Link>
              </li>
              <li>
                <Link
                  href="/services/digital-transformation"
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  Digital Transformation
                </Link>
              </li>
              <li>
                <Link 
                  href="/services/cloud-services" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  Cloud Services
                </Link>
              </li>
              <li>
                <Link 
                  href="/services" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  View All Services
                </Link>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Company</h3>
            <ul className="space-y-3">
              <li>
                <Link 
                  href="/about" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  About Us
                </Link>
              </li>
              <li>
                <Link 
                  href="/leadership" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  Leadership
                </Link>
              </li>
              <li>
                <Link 
                  href="/careers" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  Careers
                </Link>
              </li>
              <li>
                <Link 
                  href="/newsletter" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  Newsletter
                </Link>
              </li>
              <li>
                <Link 
                  href="/contact" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 flex items-center group"
                >
                  <ArrowRight className="h-3 w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-6">Contact Us</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-lg bg-gray-800/50 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <MapPin className="h-4 w-4 text-yellow-500" />
                </div>
                <div>
                  <p className="text-white font-medium text-sm">Global Headquarters</p>
                  <p className="text-gray-400 text-sm leading-relaxed mt-1">
                    Lunar A.I Studio<br />
                    400 W Rich St, Suite #200<br />
                    Columbus, OH 43215<br />
                    United States
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-lg bg-gray-800/50 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Phone className="h-4 w-4 text-yellow-500" />
                </div>
                <div>
                  <p className="text-white font-medium text-sm">Phone</p>
                  <a 
                    href="tel:+923292244004" 
                    className="text-gray-400 text-sm hover:text-yellow-500 transition-colors duration-200"
                  >
                    +923292244004
                  </a>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-lg bg-gray-800/50 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Mail className="h-4 w-4 text-yellow-500" />
                </div>
                <div>
                  <p className="text-white font-medium text-sm">Email</p>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-gray-400 text-sm hover:text-yellow-500 transition-colors duration-200"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Legal Links */}
        <div className="mt-16 pt-8 border-t border-gray-800/50">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-sm font-semibold text-white mb-4">Legal</h4>
              <div className="grid grid-cols-1 gap-2">
                <Link 
                  href="/privacy-policy" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 text-sm"
                >
                  Privacy Policy
                </Link>
                <Link 
                  href="/terms-of-service" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 text-sm"
                >
                  Terms of Service
                </Link>
                <Link 
                  href="/cookie-policy" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 text-sm"
                >
                  Cookie Policy
                </Link>
                <Link 
                  href="/accessibility" 
                  className="text-gray-400 hover:text-yellow-500 transition-colors duration-200 text-sm"
                >
                  Accessibility
                </Link>
          </div>
        </div>

            <div className="md:col-span-2 flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
              <p className="text-gray-400 text-sm">
                &copy; {new Date().getFullYear()} Lunar Studio. All rights reserved.
              </p>
              
              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4 text-gray-400" />
                <select className="bg-transparent text-gray-400 text-sm border-none focus:outline-none cursor-pointer hover:text-yellow-500 transition-colors duration-200">
              <option value="en">English</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="zh">中文</option>
              <option value="ja">日本語</option>
              <option value="ar">العربية</option>
            </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
