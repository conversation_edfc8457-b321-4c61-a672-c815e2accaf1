"use client"

import type React from "react"

import { useState, useRef, type ReactNode } from "react"
import { motion, useMotionValue, useSpring, useTransform } from "framer-motion"

interface ThreeDCardProps {
  children: ReactNode
  className?: string
  glowColor?: string
  depth?: number
  borderRadius?: string
}

export default function ThreeDCard({
  children,
  className = "",
  glowColor = "rgba(255, 255, 255, 0.15)",
  depth = 30,
  borderRadius = "1rem",
}: ThreeDCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  // Mouse position for 3D effect
  const x = useMotionValue(0)
  const y = useMotionValue(0)

  // Spring animations for smoother movement
  const rotateX = useSpring(useTransform(y, [-300, 300], [15, -15]), { stiffness: 200, damping: 25 })
  const rotateY = useSpring(useTransform(x, [-300, 300], [-15, 15]), { stiffness: 200, damping: 25 })

  // Handle mouse move for 3D effect
  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = cardRef.current?.getBoundingClientRect()
    if (!rect) return

    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    x.set(e.clientX - centerX)
    y.set(e.clientY - centerY)
  }

  // Reset on mouse leave
  const handleMouseLeave = () => {
    setIsHovered(false)
    x.set(0)
    y.set(0)
  }

  return (
    <motion.div
      ref={cardRef}
      className={`relative ${className}`}
      style={{
        perspective: "1200px",
        transformStyle: "preserve-3d",
        borderRadius,
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
    >
      <motion.div
        className="relative w-full h-full"
        style={{
          rotateX,
          rotateY,
          transformStyle: "preserve-3d",
          borderRadius,
        }}
      >
        {/* Glow effect */}
        <motion.div
          className="absolute inset-0 rounded-[inherit] opacity-0"
          style={{
            boxShadow: `0 0 30px ${glowColor}`,
            borderRadius,
          }}
          animate={{ opacity: isHovered ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />

        {/* Main content */}
        <motion.div
          className="relative w-full h-full"
          style={{
            transformStyle: "preserve-3d",
            transform: `translateZ(${depth / 2}px)`,
            borderRadius,
          }}
        >
          {children}
        </motion.div>

        {/* Back panel for depth */}
        <motion.div
          className="absolute inset-0 bg-black/20 backdrop-blur-sm"
          style={{
            transform: `translateZ(-${depth / 2}px)`,
            backfaceVisibility: "hidden",
            borderRadius,
          }}
        />

        {/* Edge panels for 3D effect */}
        {/* Top */}
        <motion.div
          className="absolute left-0 right-0 h-[1px] bg-white/10"
          style={{
            transform: `rotateX(90deg) translateZ(${depth / 2}px)`,
            backfaceVisibility: "hidden",
            width: "100%",
            transformOrigin: "top",
          }}
        />

        {/* Bottom */}
        <motion.div
          className="absolute left-0 right-0 bottom-0 h-[1px] bg-white/10"
          style={{
            transform: `rotateX(-90deg) translateZ(${depth / 2}px)`,
            backfaceVisibility: "hidden",
            width: "100%",
            transformOrigin: "bottom",
          }}
        />

        {/* Left */}
        <motion.div
          className="absolute top-0 bottom-0 left-0 w-[1px] bg-white/10"
          style={{
            transform: `rotateY(-90deg) translateZ(${depth / 2}px)`,
            backfaceVisibility: "hidden",
            height: "100%",
            transformOrigin: "left",
          }}
        />

        {/* Right */}
        <motion.div
          className="absolute top-0 bottom-0 right-0 w-[1px] bg-white/10"
          style={{
            transform: `rotateY(90deg) translateZ(${depth / 2}px)`,
            backfaceVisibility: "hidden",
            height: "100%",
            transformOrigin: "right",
          }}
        />
      </motion.div>
    </motion.div>
  )
}
