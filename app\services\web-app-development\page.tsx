"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { ArrowRight, Code, Globe, Smartphone, Server, Sparkles, Check, Star, Zap, Layers, Shield } from "lucide-react"
import AnimatedText from "@/components/animated-text"
import ThreeDCard from "@/components/3d-card"

export default function WebAppDevelopmentPage() {
  // Case studies
  const caseStudies = [
    {
      title: "Luxury E-Commerce Platform",
      description:
        "A bespoke shopping experience for a high-end fashion retailer with personalized recommendations and seamless checkout.",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=600&auto=format&fit=crop",
      results: "127% increase in conversion rate",
    },
    {
      title: "Financial Services Dashboard",
      description:
        "An elegant, secure dashboard for wealth management with real-time portfolio analytics and reporting.",
      image: "https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=600&auto=format&fit=crop",
      results: "Reduced decision time by 68%",
    },
    {
      title: "Premium Travel Application",
      description:
        "A cross-platform mobile app for luxury travel experiences with offline functionality and concierge services.",
      image: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?q=80&w=600&auto=format&fit=crop",
      results: "4.9/5 App Store rating with 200K+ downloads",
    },
  ]

  // Technologies
  const technologies = [
    {
      category: "Frontend",
      items: ["React", "Next.js", "Vue.js", "Angular", "React Native", "Flutter", "TypeScript", "Tailwind CSS"],
    },
    {
      category: "Backend",
      items: ["Node.js", "Python", "Java", "Go", "Ruby on Rails", "ASP.NET Core", "GraphQL", "REST APIs"],
    },
    {
      category: "Database",
      items: ["PostgreSQL", "MongoDB", "MySQL", "Redis", "Elasticsearch", "Firebase", "Supabase", "DynamoDB"],
    },
    {
      category: "DevOps",
      items: ["AWS", "Azure", "Google Cloud", "Docker", "Kubernetes", "CI/CD", "Terraform", "Vercel"],
    },
  ]

  // Services
  const services = [
    {
      title: "Luxury Web Applications",
      description:
        "Bespoke web applications with sophisticated interfaces, seamless performance, and enterprise-grade security.",
      icon: <Globe className="h-10 w-10 text-yellow-500" />,
      features: [
        "Custom frontend architecture",
        "Responsive design excellence",
        "Performance optimization",
        "Seamless third-party integrations",
      ],
    },
    {
      title: "Premium Mobile Experiences",
      description:
        "Native and cross-platform mobile applications that deliver exceptional user experiences across all devices.",
      icon: <Smartphone className="h-10 w-10 text-yellow-500" />,
      features: [
        "iOS and Android development",
        "Cross-platform solutions",
        "Offline functionality",
        "Biometric authentication",
      ],
    },
    {
      title: "Enterprise Web Platforms",
      description: "Scalable, secure web platforms designed for complex business requirements and high-volume traffic.",
      icon: <Server className="h-10 w-10 text-yellow-500" />,
      features: [
        "Microservices architecture",
        "Global content delivery",
        "Enterprise authentication",
        "High-availability infrastructure",
      ],
    },
  ]

  // Development process
  const developmentProcess = [
    {
      number: "01",
      title: "Discovery & Strategy",
      description:
        "We begin with a comprehensive exploration of your vision, objectives, and requirements to create a tailored development strategy.",
    },
    {
      number: "02",
      title: "Design & Architecture",
      description:
        "Our experts craft elegant interfaces and robust technical architectures that align with your brand's luxury positioning.",
    },
    {
      number: "03",
      title: "Development & Testing",
      description:
        "Our skilled engineers bring your vision to life with meticulous attention to code quality, performance, and security.",
    },
    {
      number: "04",
      title: "Deployment & Support",
      description:
        "We ensure a seamless launch and provide ongoing support to maintain the excellence of your digital product.",
    },
  ]

  return (
    <main className="pt-24 relative">
      {/* Hero Section */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=1920&auto=format&fit=crop"
            alt="Web & App Development"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Premium Development
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Web & App Development"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              Sophisticated web applications and mobile experiences crafted with meticulous attention to detail and
              performance.
            </p>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=1920&auto=format&fit=crop"
            alt="Services background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Services
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Bespoke Development Solutions
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We craft tailored digital experiences that reflect the unique identity and requirements of your luxury
              brand.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ThreeDCard className="p-6 h-full">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full">
                    <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 shadow-lg">
                      {service.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-white">{service.title}</h3>
                    <p className="text-gray-300 mb-6">{service.description}</p>

                    <h4 className="text-sm font-semibold text-white mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <span className="bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 rounded-full p-1 mr-3 mt-1 shadow-sm">
                            <svg className="h-2 w-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                              <circle cx="4" cy="4" r="3" />
                            </svg>
                          </span>
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </ThreeDCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Development Process */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?q=80&w=1920&auto=format&fit=crop"
            alt="Development Process"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Process
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Development Excellence
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our meticulous approach ensures that every project we undertake meets our exacting standards of quality
              and performance.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {developmentProcess.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ThreeDCard className="p-6 h-full">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full">
                    <div className="text-3xl font-bold text-yellow-500 mb-4">{step.number}</div>
                    <h3 className="text-xl font-bold mb-3 text-white">{step.title}</h3>
                    <p className="text-gray-300">{step.description}</p>
                  </div>
                </ThreeDCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=1920&auto=format&fit=crop"
            alt="Technologies"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Tech Stack
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Cutting-Edge Technologies
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We leverage the latest technologies and frameworks to build robust, scalable, and high-performance
              applications.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {technologies.map((tech, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ThreeDCard className="p-6 h-full">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full">
                    <h3 className="text-xl font-bold mb-4 text-white">{tech.category}</h3>
                    <div className="flex flex-wrap gap-2">
                      {tech.items.map((item, itemIndex) => (
                        <span key={itemIndex} className="bg-gray-800 text-gray-300 px-3 py-1 rounded-full text-sm">
                          {item}
                        </span>
                      ))}
                    </div>
                  </div>
                </ThreeDCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1920&auto=format&fit=crop"
            alt="Case Studies background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Featured Projects
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Our Success Stories
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Explore how our bespoke development solutions have transformed businesses across luxury industries.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {caseStudies.map((study, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/70 backdrop-blur-sm rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{study.title}</h3>
                  <p className="text-gray-300 mb-4">{study.description}</p>
                  <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3 mb-4">
                    <div className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-400 font-medium">{study.results}</span>
                    </div>
                  </div>
                  <Link
                    href="/portfolio"
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    View Case Study
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1920&auto=format&fit=crop"
            alt="Why Choose Us"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
                <span className="text-yellow-300 text-sm font-medium flex items-center">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Why Choose Us
                </span>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
                Unparalleled Development Excellence
              </h2>
              <p className="text-gray-300 mb-8">
                At Lunar Studio, we combine technical expertise with a deep understanding of luxury brands to create
                digital experiences that exceed expectations.
              </p>

              <div className="space-y-4">
                {[
                  {
                    title: "Technical Excellence",
                    description:
                      "Our engineers are masters of their craft, delivering code that is elegant, efficient, and maintainable.",
                    icon: <Code className="h-6 w-6 text-yellow-500" />,
                  },
                  {
                    title: "Performance Obsession",
                    description:
                      "We meticulously optimize every aspect of your application to ensure lightning-fast performance.",
                    icon: <Zap className="h-6 w-6 text-yellow-500" />,
                  },
                  {
                    title: "Scalable Architecture",
                    description:
                      "Our solutions are built on robust foundations that can grow seamlessly with your business.",
                    icon: <Layers className="h-6 w-6 text-yellow-500" />,
                  },
                  {
                    title: "Security First",
                    description: "We implement enterprise-grade security measures to protect your data and your users.",
                    icon: <Shield className="h-6 w-6 text-yellow-500" />,
                  },
                ].map((item, index) => (
                  <div key={index} className="flex items-start">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-600/30 to-yellow-800/30 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                      {item.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold mb-1 text-white">{item.title}</h3>
                      <p className="text-gray-300">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              <ThreeDCard className="p-8">
                <div className="bg-gray-900/70 backdrop-blur-sm p-8 rounded-lg">
                  <div className="relative h-[400px] rounded-lg overflow-hidden">
                    <Image
                      src="https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?q=80&w=800&auto=format&fit=crop"
                      alt="Development Excellence"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6">
                      <div className="bg-black/60 backdrop-blur-sm p-4 rounded-lg">
                        <h3 className="text-xl font-bold mb-2 text-white">Crafting Digital Excellence</h3>
                        <p className="text-gray-300">
                          Our development team combines technical mastery with an eye for detail to create digital
                          experiences that stand apart.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </ThreeDCard>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50 backdrop-blur-sm">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Elevate Your Digital Presence?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how Lunar Studio can craft a bespoke web or mobile solution tailored to your unique
                  requirements.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg shadow-lg shadow-yellow-600/20"
              >
                Schedule a Private Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
