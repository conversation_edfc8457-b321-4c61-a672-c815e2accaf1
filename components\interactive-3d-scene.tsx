"use client"

import { useRef, useState, useEffect } from "react"
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber"
import {
  OrbitControls,
  useGLTF,
  Environment,
  Float,
  Text3D,
  MeshTransmissionMaterial,
  MeshDistortMaterial,
  Sphere,
} from "@react-three/drei"
import { MathUtils } from "three"
import { motion } from "framer-motion-3d"
import { MotionConfig } from "framer-motion"

function Model({ position = [0, 0, 0], scale = 1, rotation = [0, 0, 0] }) {
  const { scene } = useGLTF("/assets/3d/duck.glb")
  const modelRef = useRef()

  useFrame((state) => {
    if (modelRef.current) {
      modelRef.current.rotation.y = state.clock.getElapsedTime() * 0.5
    }
  })

  return (
    <motion.primitive
      ref={modelRef}
      object={scene}
      position={position}
      scale={scale}
      rotation={rotation}
      whileHover={{ scale: scale * 1.2 }}
      transition={{ type: "spring", stiffness: 100, damping: 10 }}
    />
  )
}

function FloatingText({ text, position, color = "#ffffff", size = 0.5 }) {
  const textRef = useRef()
  const [hovered, setHovered] = useState(false)

  useFrame((state) => {
    if (textRef.current) {
      textRef.current.position.y += Math.sin(state.clock.getElapsedTime() * 2) * 0.003
    }
  })

  return (
    <motion.group
      position={position}
      ref={textRef}
      whileHover={{ scale: 1.2 }}
      animate={hovered ? { rotationY: Math.PI * 2 } : {}}
      transition={{ duration: 1.5, ease: "easeInOut" }}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      <Text3D font="/fonts/Geist_Bold.json" size={size} height={0.1} curveSegments={12}>
        {text}
        <MeshDistortMaterial color={color} speed={5} distort={hovered ? 0.3 : 0} radius={1} />
      </Text3D>
    </motion.group>
  )
}

function FloatingSpheres({ count = 10 }) {
  const spheres = useRef([])

  // Generate random positions
  useEffect(() => {
    spheres.current = Array.from({ length: count }, () => ({
      position: [MathUtils.randFloatSpread(10), MathUtils.randFloatSpread(10), MathUtils.randFloatSpread(10)],
      scale: MathUtils.randFloat(0.3, 1),
      speed: MathUtils.randFloat(0.5, 2),
      offset: Math.random() * Math.PI * 2,
    }))
  }, [count])

  return (
    <>
      {spheres.current.map((sphere, i) => (
        <Float key={i} speed={sphere.speed} rotationIntensity={1} floatIntensity={2} position={sphere.position}>
          <Sphere args={[sphere.scale, 16, 16]}>
            <MeshTransmissionMaterial
              backside
              samples={4}
              thickness={0.5}
              roughness={0}
              transmission={1}
              distortion={0.5}
              distortionScale={1}
              temporalDistortion={0.1}
              color={i % 2 === 0 ? "#ff88cc" : "#88ccff"}
            />
          </Sphere>
        </Float>
      ))}
    </>
  )
}

function Scene() {
  const { camera } = useThree()

  useEffect(() => {
    camera.position.set(0, 0, 8)
  }, [camera])

  return (
    <MotionConfig transition={{ duration: 0.5, type: "spring" }}>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} intensity={1} />

      <Model position={[0, -1, 0]} scale={1.5} />

      <FloatingText text="LUNAR" position={[-3, 2, 0]} color="#88ccff" />
      <FloatingText text="STUDIO" position={[1, 2, 0]} color="#ff88cc" />

      <FloatingSpheres count={15} />

      <Environment preset="night" />
      <OrbitControls enableZoom={false} enablePan={false} minPolarAngle={Math.PI / 3} maxPolarAngle={Math.PI / 1.5} />
    </MotionConfig>
  )
}

export default function Interactive3DScene({ className = "" }) {
  return (
    <div className={`w-full h-[500px] ${className}`}>
      <Canvas>
        <Scene />
      </Canvas>
    </div>
  )
}
