"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { motion, type PanInfo, useAnimation } from "framer-motion"
import { X } from "lucide-react"

interface MobileBottomSheetProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  title?: string
  height?: "small" | "medium" | "large" | "full"
}

export default function MobileBottomSheet({
  isOpen,
  onClose,
  children,
  title,
  height = "medium",
}: MobileBottomSheetProps) {
  const controls = useAnimation()
  const [isDragging, setIsDragging] = useState(false)
  const sheetRef = useRef<HTMLDivElement>(null)

  // Calculate height based on prop
  const getHeight = () => {
    switch (height) {
      case "small":
        return "30vh"
      case "medium":
        return "50vh"
      case "large":
        return "75vh"
      case "full":
        return "calc(100vh - 60px)" // Leave space for the drag handle
      default:
        return "50vh"
    }
  }

  useEffect(() => {
    if (isOpen) {
      controls.start({ y: 0 })
      // Prevent body scrolling when sheet is open
      document.body.style.overflow = "hidden"
    } else {
      controls.start({ y: "100%" })
      document.body.style.overflow = ""
    }

    return () => {
      document.body.style.overflow = ""
    }
  }, [isOpen, controls])

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false)

    const threshold = 100 // minimum distance to close the sheet

    if (info.offset.y > threshold || info.velocity.y > 500) {
      onClose()
    } else {
      controls.start({ y: 0 })
    }
  }

  if (!isOpen) return null

  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 md:hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        ref={sheetRef}
        className="absolute bottom-0 left-0 right-0 bg-gray-900 rounded-t-2xl overflow-hidden"
        style={{ height: getHeight(), maxHeight: "calc(100vh - 20px)" }}
        initial={{ y: "100%" }}
        animate={controls}
        transition={{ type: "spring", damping: 30, stiffness: 300 }}
        drag="y"
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={0.1}
        onDragStart={() => setIsDragging(true)}
        onDragEnd={handleDragEnd}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Drag handle */}
        <div className="w-full h-6 flex justify-center items-center cursor-grab active:cursor-grabbing">
          <div className="w-10 h-1 bg-gray-600 rounded-full"></div>
        </div>

        {/* Header */}
        {title && (
          <div className="px-4 py-3 border-b border-gray-800 flex justify-between items-center">
            <h3 className="font-semibold text-white">{title}</h3>
            <button onClick={onClose} className="p-1 rounded-full bg-gray-800">
              <X className="h-5 w-5 text-gray-400" />
            </button>
          </div>
        )}

        {/* Content */}
        <div className="overflow-y-auto p-4" style={{ height: title ? "calc(100% - 60px)" : "calc(100% - 24px)" }}>
          {children}
        </div>
      </motion.div>
    </motion.div>
  )
}
