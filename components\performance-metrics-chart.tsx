"use client"

import { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"
import { TrendingUp, Clock, Zap, Users } from "lucide-react"

interface PerformanceMetricsChartProps {
  serviceType?: "web" | "security" | "data" | "ai"
  className?: string
}

export default function PerformanceMetricsChart({ serviceType = "web", className = "" }: PerformanceMetricsChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  const [activeMetric, setActiveMetric] = useState(0)

  // Define metrics based on service type
  const getMetrics = () => {
    switch (serviceType) {
      case "web":
        return [
          {
            label: "Load Time",
            data: [3.2, 2.8, 2.1, 1.5, 1.2, 0.9, 0.8],
            color: "#3b82f6",
            icon: Clock,
            unit: "seconds",
            improvement: "73% faster",
          },
          {
            label: "Conversion Rate",
            data: [2.1, 2.4, 2.8, 3.2, 3.8, 4.5, 5.2],
            color: "#10b981",
            icon: TrendingUp,
            unit: "%",
            improvement: "148% increase",
          },
          {
            label: "User Engagement",
            data: [3.5, 4.2, 4.8, 5.3, 5.9, 6.4, 7.2],
            color: "#8b5cf6",
            icon: Users,
            unit: "min",
            improvement: "106% longer",
          },
        ]
      case "security":
        return [
          {
            label: "Threat Detection",
            data: [48, 65, 78, 89, 94, 97, 99],
            color: "#eab308",
            icon: TrendingUp,
            unit: "%",
            improvement: "106% more accurate",
          },
          {
            label: "Response Time",
            data: [120, 90, 60, 45, 30, 15, 5],
            color: "#10b981",
            icon: Clock,
            unit: "min",
            improvement: "96% faster",
          },
          {
            label: "Security Score",
            data: [65, 72, 78, 83, 88, 92, 96],
            color: "#3b82f6",
            icon: TrendingUp,
            unit: "/100",
            improvement: "48% stronger",
          },
        ]
      case "data":
        return [
          {
            label: "Data Processing",
            data: [120, 95, 75, 60, 45, 30, 20],
            color: "#8b5cf6",
            icon: Zap,
            unit: "min",
            improvement: "83% faster",
          },
          {
            label: "Prediction Accuracy",
            data: [78, 82, 85, 89, 92, 94, 97],
            color: "#10b981",
            icon: TrendingUp,
            unit: "%",
            improvement: "24% more accurate",
          },
          {
            label: "Insights Generated",
            data: [12, 18, 25, 32, 45, 58, 72],
            color: "#3b82f6",
            icon: TrendingUp,
            unit: "/month",
            improvement: "500% more insights",
          },
        ]
      case "ai":
        return [
          {
            label: "Automation Rate",
            data: [15, 28, 42, 55, 68, 78, 85],
            color: "#8b5cf6",
            icon: TrendingUp,
            unit: "%",
            improvement: "467% more automated",
          },
          {
            label: "Decision Accuracy",
            data: [75, 80, 84, 88, 91, 94, 97],
            color: "#10b981",
            icon: TrendingUp,
            unit: "%",
            improvement: "29% more accurate",
          },
          {
            label: "Processing Speed",
            data: [180, 120, 90, 60, 40, 25, 15],
            color: "#3b82f6",
            icon: Zap,
            unit: "sec",
            improvement: "92% faster",
          },
        ]
      default:
        return []
    }
  }

  const metrics = getMetrics()
  const currentMetric = metrics[activeMetric]
  const MetricIcon = currentMetric?.icon || TrendingUp

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    // Draw chart
    const drawChart = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      const data = currentMetric.data
      const color = currentMetric.color

      const padding = 40
      const chartWidth = canvas.width - padding * 2
      const chartHeight = canvas.height - padding * 2

      // Find max value for scaling
      const maxValue = Math.max(...data)
      const minValue = Math.min(...data)
      const range = maxValue - minValue

      // Draw grid lines
      ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
      ctx.lineWidth = 1

      // Horizontal grid lines
      for (let i = 0; i <= 4; i++) {
        const y = padding + (chartHeight / 4) * i
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(padding + chartWidth, y)
        ctx.stroke()
      }

      // Draw x-axis labels (time periods)
      const labels = ["Initial", "Month 1", "Month 2", "Month 3", "Month 4", "Month 5", "Current"]
      ctx.fillStyle = "rgba(255, 255, 255, 0.5)"
      ctx.font = "10px Arial"
      ctx.textAlign = "center"

      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i
        ctx.fillText(labels[i], x, padding + chartHeight + 15)
      }

      // Draw line chart
      ctx.strokeStyle = color
      ctx.lineWidth = 3
      ctx.beginPath()

      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i
        const normalizedValue = (data[i] - minValue) / range
        const y = padding + chartHeight - normalizedValue * chartHeight

        if (i === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      }

      ctx.stroke()

      // Draw gradient area under the line
      const gradient = ctx.createLinearGradient(0, padding, 0, padding + chartHeight)
      gradient.addColorStop(0, `${color}40`) // 25% opacity
      gradient.addColorStop(1, `${color}00`) // 0% opacity

      ctx.fillStyle = gradient
      ctx.beginPath()

      // Start from bottom left
      ctx.moveTo(padding, padding + chartHeight)

      // Draw line to match the chart line
      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i
        const normalizedValue = (data[i] - minValue) / range
        const y = padding + chartHeight - normalizedValue * chartHeight
        ctx.lineTo(x, y)
      }

      // Complete the shape
      ctx.lineTo(padding + chartWidth, padding + chartHeight)
      ctx.closePath()
      ctx.fill()

      // Draw data points
      ctx.fillStyle = color

      for (let i = 0; i < data.length; i++) {
        const x = padding + (chartWidth / (data.length - 1)) * i
        const normalizedValue = (data[i] - minValue) / range
        const y = padding + chartHeight - normalizedValue * chartHeight

        ctx.beginPath()
        ctx.arc(x, y, 4, 0, Math.PI * 2)
        ctx.fill()

        ctx.beginPath()
        ctx.arc(x, y, 6, 0, Math.PI * 2)
        ctx.strokeStyle = "rgba(255, 255, 255, 0.3)"
        ctx.lineWidth = 2
        ctx.stroke()
      }
    }

    drawChart()

    // Handle window resize
    const handleResize = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
      drawChart()
    }

    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [activeMetric, currentMetric])

  return (
    <motion.div
      className={`relative w-full bg-gradient-to-br from-gray-900 to-black rounded-xl overflow-hidden shadow-2xl ${className}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <div
              className="w-10 h-10 rounded-full flex items-center justify-center mr-3"
              style={{ backgroundColor: `${currentMetric.color}20` }}
            >
              <MetricIcon className="h-5 w-5" style={{ color: currentMetric.color }} />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">Performance Metrics</h3>
              <p className="text-gray-400 text-sm">Before and after implementation</p>
            </div>
          </div>

          <div className="text-right">
            <p className="text-2xl font-bold text-white">{currentMetric.improvement}</p>
            <p className="text-gray-400 text-xs">Overall improvement</p>
          </div>
        </div>

        <div className="h-64">
          <canvas ref={canvasRef} className="w-full h-full"></canvas>
        </div>

        <div className="grid grid-cols-3 gap-2 mt-6">
          {metrics.map((metric, index) => (
            <button
              key={index}
              className={`p-3 rounded-lg transition-all duration-300 ${
                activeMetric === index ? "bg-white/10 shadow-lg" : "bg-transparent hover:bg-white/5"
              }`}
              onClick={() => setActiveMetric(index)}
            >
              <div className="flex flex-col items-center">
                <metric.icon
                  className="h-5 w-5 mb-1"
                  style={{ color: activeMetric === index ? metric.color : "rgba(255,255,255,0.5)" }}
                />
                <span className={`text-xs ${activeMetric === index ? "text-white" : "text-gray-400"}`}>
                  {metric.label}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      <motion.div
        className="absolute inset-0 border-2 border-transparent z-30 rounded-xl pointer-events-none"
        animate={{
          borderColor: isHovered ? "rgba(59, 130, 246, 0.5)" : "rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  )
}
