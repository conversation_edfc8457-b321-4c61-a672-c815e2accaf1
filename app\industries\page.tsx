"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { ArrowRight, CheckCircle, Building, Target, Users, Zap } from "lucide-react"

export default function IndustriesPage() {
  // Industry data - simplified and professional
  const industries = [
    {
      id: "fintech",
      name: "FinTech & Banking",
      description: "Secure, scalable financial technology solutions that drive innovation and ensure compliance.",
      image: "https://images.unsplash.com/photo-**********-ec7c0e9f34b1?q=80&w=800&auto=format&fit=crop",
      icon: <Building className="h-8 w-8" />,
      keyFeatures: [
        "Advanced fraud detection systems",
        "Regulatory compliance automation",
        "Real-time payment processing",
        "Customer experience platforms"
      ]
    },
    {
      id: "healthcare",
      name: "Healthcare & MedTech",
      description: "Digital health platforms that improve patient outcomes and streamline healthcare operations.",
      image: "https://images.unsplash.com/photo-*************-2173dba999ef?q=80&w=800&auto=format&fit=crop",
      icon: <Users className="h-8 w-8" />,
      keyFeatures: [
        "HIPAA-compliant data management",
        "Telemedicine platforms",
        "Electronic health records",
        "AI-powered diagnostics"
      ]
    },
    {
      id: "ecommerce",
      name: "E-commerce & Retail",
      description: "Comprehensive e-commerce solutions that enhance customer experience and drive sales growth.",
      image: "https://images.unsplash.com/photo-1534452203293-494d7ddbf7e0?q=80&w=800&auto=format&fit=crop",
      icon: <Target className="h-8 w-8" />,
      keyFeatures: [
        "Omnichannel commerce platforms",
        "AI-driven personalization",
        "Inventory management systems",
        "Customer analytics dashboards"
      ]
    },
    {
      id: "logistics",
      name: "Logistics & Supply Chain",
      description: "Smart logistics solutions that optimize operations and improve supply chain visibility.",
      image: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?q=80&w=800&auto=format&fit=crop",
      icon: <Zap className="h-8 w-8" />,
      keyFeatures: [
        "Real-time tracking systems",
        "Route optimization algorithms",
        "Warehouse management solutions",
        "Predictive analytics"
      ]
    },
    {
      id: "education",
      name: "Education & EdTech",
      description: "Innovative educational technology that transforms learning experiences and outcomes.",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=800&auto=format&fit=crop",
      icon: <Users className="h-8 w-8" />,
      keyFeatures: [
        "Learning management systems",
        "Adaptive learning platforms",
        "Student progress tracking",
        "Virtual classroom solutions"
      ]
    },
    {
      id: "manufacturing",
      name: "Manufacturing & IoT",
      description: "Smart manufacturing solutions that increase efficiency and reduce operational costs.",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?q=80&w=800&auto=format&fit=crop",
      icon: <Building className="h-8 w-8" />,
      keyFeatures: [
        "IoT sensor integration",
        "Predictive maintenance",
        "Quality control automation",
        "Production optimization"
      ]
    }
  ]

  return (
    <main className="pt-24 bg-gray-950">
      {/* Hero Section */}
      <section className="py-24 bg-gray-950 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
              <Target className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-400 text-sm font-medium">Industry Expertise</span>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Industries We <span className="text-yellow-400">Serve</span>
            </h1>

            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We deliver specialized software solutions tailored to the unique challenges and opportunities
              of different industries, driving innovation and growth.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Industries Grid */}
      <section className="py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {industries.map((industry, index) => (
              <motion.div
                key={industry.id}
                className="group bg-gray-900/50 backdrop-blur-sm rounded-xl overflow-hidden border border-gray-800 hover:border-yellow-500/50 transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={industry.image}
                    alt={industry.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
                  <div className="absolute bottom-4 left-4">
                    <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center text-yellow-400">
                      {industry.icon}
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-3 group-hover:text-yellow-400 transition-colors duration-300">
                    {industry.name}
                  </h3>

                  <p className="text-gray-300 mb-6 leading-relaxed">
                    {industry.description}
                  </p>

                  <div className="space-y-2 mb-6">
                    {industry.keyFeatures.map((feature, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-400">
                        <CheckCircle className="h-4 w-4 text-yellow-400 mr-2 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link
                    href="/contact"
                    className="inline-flex items-center text-yellow-400 hover:text-yellow-300 font-medium group/link"
                  >
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/link:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Ready to Transform Your
              <br />
              <span className="text-yellow-400">Industry?</span>
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Let's discuss how our industry-specific solutions can help you overcome challenges
              and achieve your business objectives.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center justify-center px-10 py-5 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-bold text-lg rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-yellow-500/25"
              >
                Schedule Consultation
                <ArrowRight className="ml-3 h-6 w-6" />
              </Link>
              <Link
                href="/portfolio"
                className="inline-flex items-center justify-center px-10 py-5 border-2 border-gray-600 text-white font-bold text-lg rounded-lg hover:border-yellow-500 hover:bg-yellow-500/10 transition-all duration-300"
              >
                View Case Studies
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}
