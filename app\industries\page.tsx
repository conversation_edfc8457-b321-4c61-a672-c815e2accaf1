"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { ArrowRight, CheckCircle, Globe, Building, ChevronRight } from "lucide-react"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"

export default function IndustriesPage() {
  // Industry data
  const industries = [
    {
      id: "banking-finance",
      name: "Banking & Finance",
      description: "Secure, compliant, and innovative solutions for financial institutions.",
      image: "/placeholder.svg?height=600&width=800",
      icon: "/placeholder.svg?height=40&width=40",
      challenges: [
        "Increasing regulatory compliance requirements",
        "Rising cybersecurity threats and fraud attempts",
        "Legacy system modernization needs",
        "Competitive pressure from fintech disruptors",
      ],
      solutions: [
        "AI-powered fraud detection and prevention",
        "Secure cloud migration and digital transformation",
        "Regulatory compliance automation",
        "Customer experience enhancement platforms",
      ],
    },
    {
      id: "healthcare",
      name: "Healthcare",
      description: "Digital health platforms and data solutions for improved patient care.",
      image: "/placeholder.svg?height=600&width=800",
      icon: "/placeholder.svg?height=40&width=40",
      challenges: [
        "Patient data security and HIPAA compliance",
        "Interoperability between systems and providers",
        "Telehealth implementation and optimization",
        "Clinical workflow efficiency improvements",
      ],
      solutions: [
        "Secure healthcare data management systems",
        "Interoperable EHR and healthcare platforms",
        "Telehealth and remote patient monitoring",
        "AI-assisted diagnosis and treatment planning",
      ],
    },
    {
      id: "manufacturing",
      name: "Manufacturing",
      description: "Smart factory solutions and supply chain optimization.",
      image: "/placeholder.svg?height=600&width=800",
      icon: "/placeholder.svg?height=40&width=40",
      challenges: [
        "Supply chain disruptions and visibility",
        "Production efficiency and quality control",
        "Equipment maintenance and downtime reduction",
        "Industry 4.0 transformation challenges",
      ],
      solutions: [
        "IoT-enabled smart factory implementations",
        "Predictive maintenance and asset management",
        "Supply chain visibility and optimization",
        "Digital twin technology for process improvement",
      ],
    },
    {
      id: "retail",
      name: "Retail & E-commerce",
      description: "Omnichannel experiences and customer engagement platforms.",
      image: "/placeholder.svg?height=600&width=800",
      icon: "/placeholder.svg?height=40&width=40",
      challenges: [
        "Omnichannel integration and consistency",
        "Personalization at scale",
        "Inventory management and fulfillment",
        "Customer retention and loyalty",
      ],
      solutions: [
        "Unified commerce platforms and POS systems",
        "AI-driven personalization engines",
        "Inventory optimization and demand forecasting",
        "Customer data platforms and loyalty solutions",
      ],
    },
    {
      id: "telecommunications",
      name: "Telecommunications",
      description: "Network optimization and digital service delivery solutions.",
      image: "/placeholder.svg?height=600&width=800",
      icon: "/placeholder.svg?height=40&width=40",
      challenges: [
        "5G implementation and monetization",
        "Network security and reliability",
        "Digital service development and delivery",
        "Customer experience and churn reduction",
      ],
      solutions: [
        "5G network planning and optimization",
        "Telecom security and threat management",
        "Digital service platforms and BSS/OSS",
        "Customer experience management systems",
      ],
    },
    {
      id: "government",
      name: "Government",
      description: "Secure e-governance and citizen service platforms.",
      image: "/placeholder.svg?height=600&width=800",
      icon: "/placeholder.svg?height=40&width=40",
      challenges: [
        "Cybersecurity and data protection",
        "Legacy system modernization",
        "Citizen service delivery and engagement",
        "Interagency collaboration and data sharing",
      ],
      solutions: [
        "Secure government cloud implementations",
        "Citizen engagement and service portals",
        "Data sharing and interoperability frameworks",
        "Cybersecurity and compliance solutions",
      ],
    },
  ]

  // Updated industry images
  const industryImages = {
    "banking-finance": "https://images.unsplash.com/photo-**********-ec7c0e9f34b1?q=80&w=2787&auto=format&fit=crop",
    healthcare: "https://images.unsplash.com/photo-*************-2173dba999ef?q=80&w=2940&auto=format&fit=crop",
    manufacturing: "https://images.unsplash.com/photo-*************-4a7360e9a6b5?q=80&w=2940&auto=format&fit=crop",
    retail: "https://images.unsplash.com/photo-*************-494d7ddbf7e0?q=80&w=2944&auto=format&fit=crop",
    telecommunications: "https://images.unsplash.com/photo-**********-b99a580bb7a8?q=80&w=2940&auto=format&fit=crop",
    government: "https://images.unsplash.com/photo-*************-8fa7962a78c8?q=80&w=2940&auto=format&fit=crop",
  }

  return (
    <main className="pt-24 relative">
      <AnimatedBackground particleCount={50} className="opacity-30" />

      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="mb-6 inline-block bg-red-900/30 px-4 py-1 rounded-full">
              <span className="text-red-400 text-sm font-medium">Industry Expertise</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Solutions Tailored to Your Industry"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              We understand the unique challenges of different industries and deliver specialized solutions to address
              them.
            </p>
          </div>

          <div className="mt-12 relative h-64 md:h-96 w-full md:w-3/4 mx-auto rounded-lg overflow-hidden">
            <Image
              src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=2940&auto=format&fit=crop"
              alt="Industry Solutions"
              fill
              className="object-cover rounded-lg border border-red-800/30"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
            <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm px-3 py-1 rounded text-xs text-gray-300">
              Tailored solutions for diverse industry challenges
            </div>
          </div>
        </div>
      </section>

      {/* Global Presence */}
      <section className="py-12 bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-8 md:mb-0 md:mr-8">
              <h2 className="text-3xl font-bold mb-4 text-white">Global Industry Expertise</h2>
              <p className="text-gray-300">
                With operations across multiple continents and experience serving clients in over 30 countries, we bring
                global best practices and local insights to every industry we serve.
              </p>
            </div>
            <div className="w-full md:w-1/2 relative h-64 md:h-80">
              <Image
                src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=2944&auto=format&fit=crop"
                alt="Global Operations Map"
                fill
                className="object-cover rounded-lg"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <Globe className="h-16 w-16 text-red-500 opacity-20" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Industries Grid */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Industries We Serve</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our deep domain expertise across multiple industries enables us to deliver tailored solutions that address
              specific business challenges.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {industries.map((industry, index) => (
              <motion.div
                key={industry.id}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-red-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image
                    src={industryImages[industry.id as keyof typeof industryImages] || "/placeholder.svg"}
                    alt={industry.name}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="w-16 h-16 bg-red-900/30 rounded-full flex items-center justify-center">
                      <img src={industry.icon || "/placeholder.svg"} alt={industry.name} className="h-8 w-8" />
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{industry.name}</h3>
                  <p className="text-gray-300 mb-6">{industry.description}</p>
                  <Link
                    href={`#${industry.id}`}
                    className="text-red-400 inline-flex items-center font-medium hover:text-red-300 transition-colors"
                  >
                    Learn more
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Industry Sections */}
      {industries.map((industry, index) => (
        <section key={industry.id} id={industry.id} className={`py-24 ${index % 2 === 0 ? "bg-gray-950" : "bg-black"}`}>
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className={index % 2 === 0 ? "" : "order-2"}
              >
                <div className="inline-block bg-red-900/30 px-4 py-1 rounded-full mb-4">
                  <span className="text-red-400 text-sm font-medium">Industry Solutions</span>
                </div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">{industry.name}</h2>
                <p className="text-gray-300 mb-8">{industry.description}</p>

                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-4 text-white">Industry Challenges</h3>
                  <ul className="space-y-3">
                    {industry.challenges.map((challenge, idx) => (
                      <li key={idx} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-300">{challenge}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4 text-white">Our Solutions</h3>
                  <ul className="space-y-3">
                    {industry.solutions.map((solution, idx) => (
                      <li key={idx} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-300">{solution}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-8">
                  <Link
                    href="/contact"
                    className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
                  >
                    Discuss Your Requirements
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className={index % 2 === 0 ? "order-2" : ""}
              >
                <div className="relative rounded-lg overflow-hidden border border-gray-800">
                  <Image
                    src={industryImages[industry.id as keyof typeof industryImages] || "/placeholder.svg"}
                    alt={industry.name}
                    width={800}
                    height={600}
                    className="w-full h-auto"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-60"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <div className="flex items-center">
                      <Building className="h-6 w-6 text-red-500 mr-2" />
                      <h3 className="text-xl font-bold text-white">{industry.name} Solutions</h3>
                    </div>
                  </div>
                </div>

                <div className="mt-8 bg-gray-900 p-6 rounded-lg border border-gray-800">
                  <h3 className="text-xl font-semibold mb-4 text-white">Success Stories</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <ChevronRight className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                      <div>
                        <p className="text-white font-medium">Leading {industry.name} Provider</p>
                        <p className="text-gray-400 text-sm">
                          Implemented our solutions to achieve 40% efficiency improvement and 25% cost reduction.
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <ChevronRight className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                      <div>
                        <p className="text-white font-medium">Global {industry.name} Enterprise</p>
                        <p className="text-gray-400 text-sm">
                          Leveraged our platform to enhance security posture and ensure regulatory compliance.
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Link
                      href="/case-studies"
                      className="text-red-400 inline-flex items-center font-medium hover:text-red-300 transition-colors"
                    >
                      View all case studies
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>
      ))}

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-red-900/30 to-red-800/30 rounded-lg p-8 md:p-12 border border-red-800/50">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Ready to Transform Your Industry?</h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how our industry-specific solutions can help you overcome challenges and achieve your
                  business objectives.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg whitespace-nowrap"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
