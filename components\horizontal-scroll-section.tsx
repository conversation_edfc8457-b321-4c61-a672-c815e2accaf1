"use client"

import type React from "react"

import { useRef, useEffect, useState } from "react"
import { motion, useScroll, useTransform } from "framer-motion"

interface HorizontalScrollSectionProps {
  children: React.ReactNode
  title: string
  subtitle: string
}

export default function HorizontalScrollSection({ children, title, subtitle }: HorizontalScrollSectionProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollRef = useRef<HTMLDivElement>(null)
  const [containerWidth, setContainerWidth] = useState(0)

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"],
  })

  // Update container width on resize
  useEffect(() => {
    if (!scrollRef.current) return

    const updateWidth = () => {
      if (scrollRef.current) {
        const scrollWidth = scrollRef.current.scrollWidth
        const viewportWidth = window.innerWidth
        setContainerWidth(scrollWidth - viewportWidth + 100) // Add some padding
      }
    }

    updateWidth()
    window.addEventListener("resize", updateWidth)

    return () => window.removeEventListener("resize", updateWidth)
  }, [])

  // Transform scroll progress to horizontal movement
  const x = useTransform(scrollYProgress, [0, 1], [0, -containerWidth])

  return (
    <section ref={containerRef} className="py-24 bg-gradient-to-b from-black to-gray-900 overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 mb-12">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="inline-block">
            <motion.div
              className="bg-blue-900/30 text-blue-400 rounded-full px-4 py-1 text-sm font-medium mb-4"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              {subtitle}
            </motion.div>
          </div>
          <h2 className="text-3xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            {title}
          </h2>
        </motion.div>
      </div>

      <div className="relative h-[600px]">
        <motion.div
          ref={scrollRef}
          className="absolute left-[calc(50%-8rem)] md:left-[calc(50%-15rem)] lg:left-[calc(50%-25rem)]"
          style={{ x }}
        >
          {children}
        </motion.div>
      </div>
    </section>
  )
}
