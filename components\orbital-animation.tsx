"use client"

import type React from "react"

import { useRef, useEffect } from "react"

interface OrbitalAnimationProps {
  className?: string
  size?: number
  speed?: number
  color?: string
  children?: React.ReactNode
}

// Update color to use grayscale
export default function OrbitalAnimation({
  className = "",
  size = 300,
  speed = 20,
  color = "#ffffff",
  children,
}: OrbitalAnimationProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = size
    canvas.height = size

    // Orbital particle class
    class Particle {
      x: number
      y: number
      radius: number
      angle: number
      orbitRadius: number
      speed: number
      opacity: number

      constructor() {
        this.orbitRadius = Math.random() * (size / 2 - 10) + 10
        this.angle = Math.random() * Math.PI * 2
        this.x = Math.cos(this.angle) * this.orbitRadius + size / 2
        this.y = Math.sin(this.angle) * this.orbitRadius + size / 2
        this.radius = Math.random() * 2 + 0.5
        this.speed = (Math.random() * 0.002 + 0.001) * (speed / 10)
        this.opacity = Math.random() * 0.5 + 0.3
      }

      update() {
        // Update angle
        this.angle += this.speed

        // Calculate new position
        this.x = Math.cos(this.angle) * this.orbitRadius + size / 2
        this.y = Math.sin(this.angle) * this.orbitRadius + size / 2
      }

      draw() {
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
        ctx.fillStyle = `${color}${Math.floor(this.opacity * 255)
          .toString(16)
          .padStart(2, "0")}`
        ctx.fill()
      }
    }

    // Create particles
    const particles: Particle[] = []
    const particleCount = 50

    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle())
    }

    // Draw orbital paths
    const drawOrbitalPaths = () => {
      // Get unique orbit radii
      const orbits = [...new Set(particles.map((p) => Math.round(p.orbitRadius / 5) * 5))].sort()

      // Draw each orbit path
      orbits.forEach((orbit) => {
        ctx.beginPath()
        ctx.arc(size / 2, size / 2, orbit, 0, Math.PI * 2)
        ctx.strokeStyle = `${color}10`
        ctx.lineWidth = 0.5
        ctx.stroke()
      })
    }

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw orbital paths
      drawOrbitalPaths()

      // Update and draw particles
      particles.forEach((particle) => {
        particle.update()
        particle.draw()
      })

      requestAnimationFrame(animate)
    }

    animate()
  }, [size, speed, color])

  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      <canvas ref={canvasRef} className="absolute inset-0 z-0" />
      {children && <div className="absolute inset-0 flex items-center justify-center z-10">{children}</div>}
    </div>
  )
}
