"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
  ArrowRight,
  Cloud,
  CreditCard,
  Shield,
  Server,
  Code,
  Check,
  Database,
  Users,
  Globe,
  LineChart,
  Zap,
  Sparkles,
  Star,
} from "lucide-react"
import LunarGlowCard from "@/components/lunar-glow-card"
import ThreeDCard from "@/components/3d-card"
import AnimatedText from "@/components/animated-text"

export default function SaasProductDevelopmentPage() {
  // Luxury benefits of SaaS development with us
  const benefits = [
    {
      title: "Exceptional Scalability",
      description:
        "Our SaaS solutions are engineered to scale seamlessly with your business growth, handling increased demand without compromising performance.",
      icon: <LineChart className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Advanced Security",
      description:
        "We implement military-grade security measures to protect your data and ensure compliance with global security standards and regulations.",
      icon: <Shield className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Premium User Experience",
      description:
        "Each interface is meticulously crafted to deliver an intuitive, responsive, and luxurious user experience that reflects your brand's prestige.",
      icon: <Users className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Global Availability",
      description:
        "Our solutions are deployed on world-class infrastructure ensuring high availability and optimal performance for users around the globe.",
      icon: <Globe className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Seamless Integration",
      description:
        "Integrate effortlessly with your existing ecosystem and third-party services through our sophisticated API architecture.",
      icon: <Database className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Continuous Innovation",
      description:
        "Benefit from our commitment to innovation with regular updates and enhancements that keep your SaaS solution at the cutting edge.",
      icon: <Zap className="h-6 w-6 text-yellow-500" />,
    },
  ]

  // Features of our SaaS solutions
  const features = [
    {
      title: "Multi-Tenant Architecture",
      description:
        "Sophisticated multi-tenant design that maintains perfect isolation between customers while maximizing resource efficiency.",
      icon: <Users className="h-12 w-12 text-yellow-500" />,
    },
    {
      title: "Global CDN Integration",
      description:
        "Content delivery network integration ensuring lightning-fast performance for users across all geographies.",
      icon: <Globe className="h-12 w-12 text-yellow-500" />,
    },
    {
      title: "Advanced Analytics Dashboard",
      description:
        "Comprehensive analytics platform providing actionable insights into user behavior, system performance, and business metrics.",
      icon: <LineChart className="h-12 w-12 text-yellow-500" />,
    },
    {
      title: "Enterprise-Grade Security",
      description:
        "Multi-layered security approach including encryption, authentication, authorization, and regular security audits.",
      icon: <Shield className="h-12 w-12 text-yellow-500" />,
    },
    {
      title: "Subscription Management",
      description:
        "Sophisticated billing and subscription management systems handling complex pricing models and payment processing.",
      icon: <CreditCard className="h-12 w-12 text-yellow-500" />,
    },
    {
      title: "API-First Design",
      description:
        "Comprehensive API ecosystem enabling seamless integration with existing systems and third-party services.",
      icon: <Code className="h-12 w-12 text-yellow-500" />,
    },
  ]

  // Case studies
  const caseStudies = [
    {
      title: "Enterprise Analytics Platform",
      description:
        "Developed a sophisticated analytics platform for a Fortune 500 financial institution, processing over 10 million transactions daily with sub-second response times.",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=600&auto=format&fit=crop",
      result: "Increased operational efficiency by 42% and reduced decision-making time by 65%",
    },
    {
      title: "Luxury Retail Management System",
      description:
        "Created a comprehensive retail management SaaS solution for a prestigious international luxury brand with seamless omnichannel capabilities.",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=600&auto=format&fit=crop",
      result: "Boosted client retention by 28% and increased average transaction value by 15%",
    },
    {
      title: "Healthcare Provider Platform",
      description:
        "Engineered a HIPAA-compliant SaaS platform for healthcare providers managing patient data, appointments, and billing across multiple facilities.",
      image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=600&auto=format&fit=crop",
      result: "Reduced administrative overhead by 37% while ensuring 100% compliance with regulations",
    },
  ]

  // Development process steps
  const processSteps = [
    {
      number: "01",
      title: "Discovery & Strategy",
      description:
        "We begin with a comprehensive analysis of your business requirements, market positioning, and technical needs to create a strategic roadmap.",
    },
    {
      number: "02",
      title: "Architecture & Design",
      description:
        "Our experts craft a sophisticated multi-tenant architecture with emphasis on security, scalability, and performance.",
    },
    {
      number: "03",
      title: "Development & Testing",
      description:
        "Using enterprise-grade technologies, we develop your SaaS product with rigorous testing at every stage to ensure exceptional quality.",
    },
    {
      number: "04",
      title: "Deployment & Integration",
      description:
        "We deploy your solution on premium infrastructure with seamless integration into your existing ecosystem.",
    },
    {
      number: "05",
      title: "Monitoring & Optimization",
      description:
        "Post-launch, we continuously monitor performance and user feedback to optimize and enhance your SaaS product.",
    },
  ]

  // FAQ items
  const faqItems = [
    {
      question: "How long does it typically take to develop a SaaS product?",
      answer:
        "Development timelines vary based on complexity, but our enterprise SaaS solutions typically require 4-8 months from concept to launch. We emphasize quality and security throughout our accelerated development process.",
    },
    {
      question: "What technology stack do you use for SaaS development?",
      answer:
        "We select the optimal technology stack based on your specific requirements. Our expertise spans cloud-native technologies, microservices architectures, and enterprise frameworks that ensure security, scalability, and performance.",
    },
    {
      question: "How do you ensure the security of SaaS applications?",
      answer:
        "We implement multi-layered security measures including encryption at rest and in transit, advanced authentication, regular security audits, penetration testing, and compliance with industry standards like SOC 2, ISO 27001, and GDPR.",
    },
    {
      question: "Can you integrate my existing systems with the new SaaS platform?",
      answer:
        "Absolutely. We design our SaaS solutions with comprehensive API architectures that enable seamless integration with your existing systems, third-party services, and other business applications.",
    },
    {
      question: "What support do you provide after the SaaS platform is launched?",
      answer:
        "We offer premium support and maintenance packages tailored to your needs, including 24/7 monitoring, regular updates, performance optimization, security patches, and ongoing development of new features.",
    },
  ]

  return (
    <main className="pt-24 relative">
      {/* Hero Section */}
      <section className="bg-black text-white py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=1920&auto=format&fit=crop"
            alt="SaaS development hero"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-6 backdrop-blur-sm"
            >
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Enterprise-Grade Solutions
              </span>
            </motion.div>
            <motion.h1
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-4xl md:text-5xl font-bold mb-6"
            >
              <AnimatedText
                text="SaaS Product Development"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.1}
              />
            </motion.h1>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-gray-300 text-xl leading-relaxed mb-10"
            >
              Bespoke software-as-a-service solutions crafted with meticulous attention to scalability, security, and
              sophistication for discerning enterprises that demand excellence.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="flex flex-col sm:flex-row justify-center gap-4"
            >
              <Link
                href="/contact"
                className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center transition-colors group shadow-lg shadow-yellow-600/20"
              >
                Discuss Your SaaS Project
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
              <Link
                href="/portfolio"
                className="border border-gray-700 bg-gray-900/50 backdrop-blur-sm text-white px-8 py-3 rounded-md font-medium flex items-center justify-center hover:bg-gray-800/50 transition-colors"
              >
                View SaaS Case Studies
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Floating icons */}
        <div className="absolute left-1/4 top-1/3 text-yellow-500/20 animate-float">
          <Cloud className="w-24 h-24" />
        </div>
        <div className="absolute right-1/4 bottom-1/3 text-yellow-500/20 animate-float animation-delay-2000">
          <Server className="w-16 h-16" />
        </div>
        <div className="absolute right-1/3 top-1/4 text-yellow-500/20 animate-float animation-delay-1000">
          <Database className="w-20 h-20" />
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-20 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=1920&auto=format&fit=crop"
            alt="SaaS development overview"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="mb-6 inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full backdrop-blur-sm"
              >
                <span className="text-yellow-300 text-sm font-medium flex items-center">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Enterprise Excellence
                </span>
              </motion.div>
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                viewport={{ once: true }}
                className="text-3xl md:text-4xl font-bold mb-6 text-white"
              >
                Transform Your Business with Custom SaaS Solutions
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-gray-300 text-lg mb-8 leading-relaxed"
              >
                In the rapidly evolving digital landscape, sophisticated software-as-a-service solutions are essential
                for enterprises seeking to maintain competitive advantage while ensuring operational excellence.
              </motion.p>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                viewport={{ once: true }}
                className="text-gray-300 text-lg mb-8 leading-relaxed"
              >
                At Lunar Studio, we craft bespoke SaaS products that align perfectly with your business objectives,
                providing the scalability, security, and sophistication that discerning enterprises demand.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                viewport={{ once: true }}
                className="space-y-4"
              >
                {[
                  "Enterprise-grade security and compliance",
                  "Seamless scalability for growing businesses",
                  "Elegant, intuitive user experiences",
                  "Advanced analytics and reporting",
                  "Global availability and performance optimization",
                ].map((item, index) => (
                  <div key={index} className="flex items-start">
                    <Check className="h-6 w-6 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span className="text-gray-300">{item}</span>
                  </div>
                ))}
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <ThreeDCard className="p-6">
                <div className="bg-gray-900/70 backdrop-blur-sm p-8 rounded-lg border border-gray-800">
                  <div className="relative aspect-video overflow-hidden rounded-lg mb-8">
                    <Image
                      src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=800&auto=format&fit=crop"
                      alt="SaaS architecture"
                      width={800}
                      height={450}
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-60"></div>
                    <div className="absolute bottom-4 left-4 bg-gray-900/80 backdrop-blur-sm px-4 py-2 rounded-md">
                      <span className="text-white font-medium text-sm">Enterprise SaaS Architecture</span>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-white mb-4">Sophisticated Cloud Architecture</h3>
                  <p className="text-gray-300 mb-6">
                    Our SaaS solutions are built on robust, scalable cloud architectures that ensure optimal
                    performance, security, and availability for your business.
                  </p>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-800/50 p-4 rounded-lg">
                      <div className="flex items-center mb-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 flex items-center justify-center mr-3">
                          <Server className="h-5 w-5 text-white" />
                        </div>
                        <span className="text-white font-medium">Multi-Cloud Support</span>
                      </div>
                      <p className="text-gray-300 text-sm">
                        Deploy across AWS, Azure, or Google Cloud for optimal redundancy
                      </p>
                    </div>
                    <div className="bg-gray-800/50 p-4 rounded-lg">
                      <div className="flex items-center mb-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 flex items-center justify-center mr-3">
                          <Shield className="h-5 w-5 text-white" />
                        </div>
                        <span className="text-white font-medium">Enterprise Security</span>
                      </div>
                      <p className="text-gray-300 text-sm">End-to-end encryption with advanced threat protection</p>
                    </div>
                  </div>
                </div>
              </ThreeDCard>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1573164713988-8665fc963095?q=80&w=1920&auto=format&fit=crop"
            alt="SaaS features background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Advanced Capabilities
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Premium SaaS Features
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto text-lg">
              Our SaaS solutions incorporate sophisticated features designed to deliver exceptional value, performance,
              and security for enterprise operations.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <ThreeDCard className="p-6 h-full">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full border border-gray-800 hover:border-yellow-500 transition-colors duration-300">
                    <div className="w-20 h-20 bg-gradient-to-br from-yellow-900/30 to-yellow-600/30 rounded-2xl flex items-center justify-center mb-6">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-4 text-white">{feature.title}</h3>
                    <p className="text-gray-300">{feature.description}</p>
                  </div>
                </ThreeDCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=1920&auto=format&fit=crop"
            alt="Benefits background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Exclusive Advantages
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Why Choose Our SaaS Development
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto text-lg">
              Our approach to SaaS development combines technical excellence with business acumen to deliver solutions
              that exceed expectations.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <LunarGlowCard glowColor="yellow" intensity="low">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 rounded-full flex items-center justify-center mb-6 shadow-lg">
                      {benefit.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-white">{benefit.title}</h3>
                    <p className="text-gray-300">{benefit.description}</p>
                  </div>
                </LunarGlowCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Development Process */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?q=80&w=1920&auto=format&fit=crop"
            alt="Process background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Methodology
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Premium Development Process
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto text-lg">
              Our structured approach ensures excellence at every stage, from initial concept to final deployment and
              beyond.
            </p>
          </motion.div>

          <div className="space-y-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col md:flex-row items-start gap-6"
              >
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-yellow-600/20 to-yellow-800/20 border border-yellow-500/20 rounded-full flex items-center justify-center text-yellow-500 text-xl md:text-2xl font-bold">
                    {step.number}
                  </div>
                </div>
                <div className="flex-grow bg-gray-900/50 backdrop-blur-sm p-6 rounded-lg border border-gray-800">
                  <h3 className="text-xl font-bold mb-2 text-white">{step.title}</h3>
                  <p className="text-gray-300">{step.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=1920&auto=format&fit=crop"
            alt="Case studies background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Success Stories
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Premium SaaS Case Studies
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto text-lg">
              Discover how our SaaS solutions have transformed operations and driven growth for prestigious enterprises.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {caseStudies.map((study, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-900/70 backdrop-blur-sm rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
              >
                <div className="relative h-48">
                  <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/50 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 bg-yellow-600/90 text-white text-xs px-3 py-1 rounded-md">
                    SaaS Development
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{study.title}</h3>
                  <p className="text-gray-300 mb-4 text-sm">{study.description}</p>
                  <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3 mb-4">
                    <div className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-400 font-medium text-sm">{study.result}</span>
                    </div>
                  </div>
                  <Link
                    href="/portfolio"
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors text-sm"
                  >
                    View Full Case Study
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?q=80&w=1920&auto=format&fit=crop"
            alt="FAQ background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Common Questions
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Frequently Asked Questions
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto text-lg">
              Find answers to common questions about our SaaS development services and approach.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {faqItems.map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800"
                >
                  <h3 className="text-xl font-bold mb-3 text-white">{item.question}</h3>
                  <p className="text-gray-300">{item.answer}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50 backdrop-blur-sm max-w-5xl mx-auto">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Transform Your Business with SaaS?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how our bespoke SaaS development services can elevate your operations and drive
                  sustainable growth for your enterprise.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group shadow-lg shadow-yellow-600/20 whitespace-nowrap"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
