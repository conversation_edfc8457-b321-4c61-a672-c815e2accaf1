"use client"

import Link from "next/link"
import Image from "next/image"
import { useEffect, useState, useRef } from "react"
import {
  ArrowRight,
  ChevronDown,
  Check,
  Globe,
  ExternalLink,
  Shield,
  Bot,
  Zap,
  LineChart,
  Server,
  Sparkles,
  Lock,
  Search,
  Brain,
  Lightbulb,
  Crosshair,
  Activity,
  ShoppingBag,
  MessageSquare,
  Send,
  Code,
  BarChart,
  Cloud,
  Database,
  Network,
  Smartphone,
  Palette,
  ShoppingCart,
  Building,
  Settings,
  Workflow,
  FileText,
  TrendingUp,
  Phone,
  Users,
  Star,
  Award,
  Target,
  Layers,
  Cpu,
  Play,
} from "lucide-react"
import { motion, useScroll, useTransform } from "framer-motion"
import WebGLBackground from "@/components/webgl-background"
import FloatingElements from "@/components/floating-elements"
import TestimonialCarousel from "@/components/testimonial-carousel"
import DynamicProjectShowcase from "@/components/dynamic-project-showcase"
import { useIsMobile } from "@/hooks/use-mobile"
import MobileServiceCard from "@/components/mobile-service-card"
import MobileSwipeCarousel from "@/components/mobile-swipe-carousel"

export default function Home() {
  const [scrollY, setScrollY] = useState(0)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const heroRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll()
  const isMobile = useIsMobile()

  const opacity = useTransform(scrollYProgress, [0, 0.2], [1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.2], [1, 0.95])

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Hero background images - luxury-themed
  const heroImages = [
    "https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1920&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1622547748225-3fc4abd2cca0?q=80&w=1920&auto=format&fit=crop",
  ]

  // Cycle through hero background images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % heroImages.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [heroImages.length])

  // Services data - comprehensive software offerings
  const services = [
    {
      title: "Web Development",
      description:
        "Modern, responsive web applications built with cutting-edge technologies and optimized for performance and user experience.",
      icon: <Code className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8d2ViJTIwZGV2ZWxvcG1lbnR8ZW58MHx8MHx8fDA%3D",
      link: "/services/web-app-development",
    },
    {
      title: "Android Development",
      description:
        "Native Android applications with modern UI/UX, advanced features, and seamless integration with backend services.",
      icon: <Smartphone className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YW5kcm9pZCUyMGRldmVsb3BtZW50fGVufDB8fDB8fHww",
      link: "/services/android-development",
    },
    {
      title: "MVP Development",
      description:
        "Rapid development of minimum viable products to validate your business concept and accelerate time to market.",
      icon: <BarChart className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1572177812156-58036aae439c?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bXZwJTIwZGV2ZWxvcG1lbnR8ZW58MHx8MHx8fDA%3D",
      link: "/services/mvp-development",
    },
    {
      title: "UI/UX Design",
      description:
        "Intuitive and beautiful user interfaces designed with user-centered approach and modern design principles.",
      icon: <Palette className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dWl1eCUyMGRlc2lnbnxlbnwwfHwwfHx8MA%3D%3D",
      link: "/services/uiux-design",
    },
    {
      title: "E-Commerce Solutions",
      description:
        "Complete e-commerce platforms with secure payment processing, inventory management, and customer analytics.",
      icon: <ShoppingCart className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZS1jb21tZXJjZXxlbnwwfHwwfHx8MA%3D%3D",
      link: "/services/ecommerce",
    },
    {
      title: "Real Estate Platforms",
      description:
        "Comprehensive real estate management systems with property listings, virtual tours, and client management.",
      icon: <Building className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8cmVhbCUyMGVzdGF0ZXxlbnwwfHwwfHx8MA%3D%3D",
      link: "/services/real-estate",
    },
    {
      title: "Custom Development",
      description:
        "Bespoke software solutions tailored to your specific business requirements and unique operational needs.",
      icon: <Settings className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y3VzdG9tJTIwZGV2ZWxvcG1lbnR8ZW58MHx8MHx8fDA%3D",
      link: "/services/custom-development",
    },
    {
      title: "End-to-End Development",
      description:
        "Complete software development lifecycle from concept to deployment, including maintenance and support.",
      icon: <Workflow className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZW5kJTIwdG8lMjBlbmR8ZW58MHx8MHx8fDA%3D",
      link: "/services/end-to-end-development",
    },
    {
      title: "Business Registration",
      description:
        "Digital solutions for business registration, compliance management, and regulatory reporting automation.",
      icon: <FileText className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YnVzaW5lc3MlMjByZWdpc3RyYXRpb258ZW58MHx8MHx8fDA%3D",
      link: "/services/business-registration",
    },
    {
      title: "SEO & Digital Marketing",
      description:
        "Search engine optimization and digital marketing strategies to increase online visibility and drive growth.",
      icon: <TrendingUp className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c2VvfGVufDB8fDB8fHww",
      link: "/services/seo-marketing",
    },
    {
      title: "Chatbot Development",
      description:
        "Intelligent chatbots and conversational AI solutions to enhance customer service and automate interactions.",
      icon: <MessageSquare className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y2hhdGJvdHxlbnwwfHwwfHx8MA%3D%3D",
      link: "/services/enterprise-chatbots",
    },
    {
      title: "Agentic AI Systems",
      description:
        "Autonomous AI agents that operate with intelligence and purpose, transforming how your organization makes decisions.",
      icon: <Bot className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-*************-bcc4688e7485?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YWdlbnRpYyUyMGFpfGVufDB8fDB8fHww",
      link: "/services/agentic-ai",
    },
  ]

  // Industry solutions - luxury focus
  const industries = [
    {
      name: "Financial Services",
      icon: <LineChart className="h-8 w-8 text-yellow-500" />,
      description: "Advanced AI and security solutions for banks, investment firms, and financial institutions.",
      image: "https://images.unsplash.com/photo-*************-2fceff292cdc?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Healthcare",
      icon: <Activity className="h-8 w-8 text-yellow-500" />,
      description: "HIPAA-compliant AI systems and security for patient data protection and medical research.",
      image: "https://images.unsplash.com/photo-*************-2173dba999ef?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Government & Defense",
      icon: <Shield className="h-8 w-8 text-yellow-500" />,
      description: "Mission-critical AI and cybersecurity solutions for government agencies and defense contractors.",
      image: "https://images.unsplash.com/photo-*************-8fa7962a78c8?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Energy & Utilities",
      icon: <Zap className="h-8 w-8 text-yellow-500" />,
      description: "Critical infrastructure protection and intelligent operations for energy providers.",
      image: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Enterprise Technology",
      icon: <Server className="h-8 w-8 text-yellow-500" />,
      description: "Advanced AI integration and security solutions for technology companies and platforms.",
      image: "https://images.unsplash.com/photo-**********-b99a580bb7a8?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Luxury Retail",
      icon: <ShoppingBag className="h-8 w-8 text-yellow-500" />,
      description: "Sophisticated AI-driven customer experiences with robust security for high-end retail brands.",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=500&auto=format&fit=crop",
    },
  ]

  // Real prestigious client logos
  const clients = [
    {
      name: "Goldman Sachs",
      logo: "https://upload.wikimedia.org/wikipedia/commons/6/61/Goldman_Sachs.svg",
    },
    {
      name: "Microsoft",
      logo: "https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg",
    },
    {
      name: "IBM",
      logo: "https://upload.wikimedia.org/wikipedia/commons/5/51/IBM_logo.svg",
    },
    {
      name: "Deloitte",
      logo: "https://upload.wikimedia.org/wikipedia/commons/9/9d/Deloitte.svg",
    },
    {
      name: "Accenture",
      logo: "https://upload.wikimedia.org/wikipedia/commons/c/cd/Accenture.svg",
    },
    {
      name: "Oracle",
      logo: "https://upload.wikimedia.org/wikipedia/commons/5/50/Oracle_logo.svg",
    },
  ]

  // Case studies - luxury projects
  const caseStudies = [
    {
      title: "AI-Powered Fraud Detection System for Global Bank",
      category: "Financial Services",
      image: "https://images.unsplash.com/photo-*************-2fceff292cdc?q=80&w=600&auto=format&fit=crop",
      results: "Reduced fraud by 98% and saved $12M annually",
    },
    {
      title: "Zero Trust Security Implementation for Healthcare Network",
      category: "Healthcare",
      image: "https://images.unsplash.com/photo-*************-2173dba999ef?q=80&w=600&auto=format&fit=crop",
      results: "100% compliance with zero security breaches",
    },
    {
      title: "Agentic AI System for Government Intelligence Analysis",
      category: "Government",
      image: "https://images.unsplash.com/photo-*************-3a3843ee7f5d?q=80&w=600&auto=format&fit=crop",
      results: "85% faster intelligence processing with higher accuracy",
    },
  ]

  // Testimonials from luxury clients
  const testimonials = [
    {
      quote:
        "Lunar A.i Studio delivered an exceptional software solution that perfectly aligns with our brand's luxury positioning. Their attention to detail and commitment to excellence exceeded our expectations.",
      author: "Alexandra Reynolds",
      title: "CTO, Luxury Retail Group",
      image: "https://images.unsplash.com/photo-*************-b8d87734a5a2?q=80&w=200&auto=format&fit=crop",
    },
    {
      quote:
        "Working with Lunar A.i Studio has transformed our digital presence. Their team's expertise in creating sophisticated, high-performance solutions has given us a significant competitive advantage.",
      author: "Jonathan Blackwell",
      title: "CEO, Private Banking Services",
      image: "https://images.unsplash.com/photo-**********-0b93528c311a?q=80&w=200&auto=format&fit=crop",
    },
    {
      quote:
        "The bespoke software developed by Lunar A.i Studio has revolutionized how we manage our exclusive client relationships. Their understanding of luxury service standards is unparalleled.",
      author: "Victoria Harrington",
      title: "Director, Elite Hospitality Group",
      image: "https://images.unsplash.com/photo-*************-15a19d654956?q=80&w=200&auto=format&fit=crop",
    },
  ]

  // Company stats - impressive numbers
  const stats = [
    { value: 15, label: "Years of Excellence", suffix: "+" },
    { value: 250, label: "Exclusive Clients", suffix: "+" },
    { value: 500, label: "Bespoke Projects", suffix: "+" },
    { value: 100, label: "Elite Specialists", suffix: "%" },
  ]

  const heroFeatures = [
    {
      title: "Agentic AI Systems",
      icon: <Bot className="h-4 w-4 text-yellow-500" />,
    },
    {
      title: "Advanced Cybersecurity",
      icon: <Shield className="h-4 w-4 text-yellow-500" />,
    },
    {
      title: "Custom GPTs & Bots",
      icon: <MessageSquare className="h-4 w-4 text-yellow-500" />,
    },
    {
      title: "Enterprise Protection",
      icon: <Lock className="h-4 w-4 text-yellow-500" />,
    },
  ]

  // Core values - luxury software house
  const coreValues = [
    {
      title: "Intelligence",
      description:
        "We harness the power of advanced AI to create systems that learn, adapt, and deliver exceptional results.",
      icon: <Brain className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Security",
      description: "We implement uncompromising protection measures that safeguard your most valuable digital assets.",
      icon: <Shield className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Innovation",
      description:
        "We constantly push the boundaries of what's possible in AI and cybersecurity to stay ahead of emerging threats and opportunities.",
      icon: <Lightbulb className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Precision",
      description:
        "We deliver meticulously engineered solutions with attention to every technical detail and security consideration.",
      icon: <Crosshair className="h-6 w-6 text-yellow-500" />,
    },
  ]

  const brands = [
    {
      name: "Microsoft",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Microsoft_logo.svg/2048px-Microsoft_logo.svg.png",
    },
    {
      name: "IBM",
      logo: "https://upload.wikimedia.org/wikipedia/commons/5/51/IBM_logo.svg",
    },
    {
      name: "Amazon",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/2560px-Amazon_logo.svg.png",
    },
    {
      name: "Google",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Google_2015_logo.svg/2560px-Google_2015_logo.svg.png",
    },
    {
      name: "Salesforce",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Salesforce.com_logo.svg/2560px-Salesforce.com_logo.svg.png",
    },
    {
      name: "Oracle",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/5/50/Oracle_logo.svg/2560px-Oracle_logo.svg.png",
    },
  ]

  return (
    <main className="flex min-h-screen flex-col bg-gray-950">
      {/* Modern Professional Hero Section */}
      <section
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gray-950"
      >
        {/* Clean background with subtle gradients */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-950 via-gray-900 to-black"></div>

          {/* Subtle geometric patterns */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-72 h-72 bg-yellow-500/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 right-20 w-96 h-96 bg-yellow-600/5 rounded-full blur-3xl"></div>
          </div>
        </div>

        {/* Main content */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-screen py-20">

            {/* Left Content */}
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              {/* Badge */}
              <motion.div
                className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <Sparkles className="h-4 w-4 text-yellow-400 mr-2" />
                <span className="text-yellow-400 text-sm font-medium">Next-Gen Software Solutions</span>
              </motion.div>

              {/* Main Headline */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight">
                  <span className="text-white block mb-2">Custom Software</span>
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-yellow-600">
                    Development
                  </span>
                </h1>
              </motion.div>

              {/* Description */}
              <motion.p
                className="text-xl text-gray-300 leading-relaxed max-w-2xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
              >
                Scalable web and mobile app solutions tailored for businesses, ensuring performance,
                security, and seamless user experience with cutting-edge AI integration.
              </motion.p>

              {/* Key Features */}
              <motion.div
                className="flex flex-wrap gap-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                {[
                  { icon: <Bot className="h-5 w-5" />, text: "AI-Powered Solutions" },
                  { icon: <Shield className="h-5 w-5" />, text: "Enterprise Security" },
                  { icon: <Zap className="h-5 w-5" />, text: "High Performance" },
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2 text-gray-300">
                    <div className="text-yellow-400">{feature.icon}</div>
                    <span className="text-sm font-medium">{feature.text}</span>
                  </div>
                ))}
              </motion.div>

              {/* CTA Buttons */}
              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.9 }}
              >
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-semibold rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-yellow-500/25"
                >
                  Let's Build Together
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <Link
                  href="/services"
                  className="inline-flex items-center justify-center px-8 py-4 border border-gray-600 text-white font-semibold rounded-lg hover:border-yellow-500 hover:bg-yellow-500/10 transition-all duration-300"
                >
                  Discover Services
                  <ExternalLink className="ml-2 h-5 w-5" />
                </Link>
              </motion.div>

              {/* Trust Indicators */}
              <motion.div
                className="flex items-center space-x-8 pt-8 border-t border-gray-800"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1.1 }}
              >
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-gray-400 text-sm">24/7 Support</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-yellow-400" />
                  <span className="text-gray-400 text-sm">500+ Projects</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Globe className="h-4 w-4 text-yellow-400" />
                  <span className="text-gray-400 text-sm">Global Reach</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Content - Hero Visual */}
            <motion.div
              className="relative hidden lg:block"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="relative">
                {/* Main hero image */}
                <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl">
                  <Image
                    src="https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=800&auto=format&fit=crop"
                    alt="Software Development"
                    width={600}
                    height={400}
                    className="w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                </div>

                {/* Floating elements */}
                <motion.div
                  className="absolute -top-6 -right-6 bg-yellow-500 text-black p-4 rounded-xl shadow-lg"
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <Code className="h-8 w-8" />
                </motion.div>

                <motion.div
                  className="absolute -bottom-6 -left-6 bg-gray-800 text-yellow-400 p-4 rounded-xl shadow-lg border border-gray-700"
                  animate={{ y: [0, 10, 0] }}
                  transition={{ duration: 3, repeat: Infinity, delay: 1.5 }}
                >
                  <Shield className="h-8 w-8" />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>



      {/* Stats Section */}
      <section className="py-20 bg-gray-900 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Your Success is Our Story,
              <br />
              <span className="text-yellow-400">Proven in Numbers</span>
            </h2>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { value: "500+", label: "Solutions Delivered", icon: <Code className="h-8 w-8" /> },
              { value: "10+", label: "Years of Excellence", icon: <Award className="h-8 w-8" /> },
              { value: "98%", label: "Customer Satisfaction", icon: <Star className="h-8 w-8" /> },
              { value: "15+", label: "Industries Served", icon: <Building className="h-8 w-8" /> },
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-500/10 rounded-full text-yellow-400 mb-4 group-hover:bg-yellow-500/20 transition-colors duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl md:text-5xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-gray-400 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Core Values Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?q=80&w=1920&auto=format&fit=crop"
            alt="Core Values background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Core Values
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              The Pillars of Our Excellence
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our commitment to these fundamental principles guides every aspect of our work and ensures we deliver
              nothing short of excellence.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {coreValues.map((value, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800 hover:border-yellow-500 transition-all duration-500 group relative overflow-hidden"
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ 
                  duration: 0.6, 
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 100,
                  damping: 15
                }}
                viewport={{ once: true, margin: "-50px" }}
                whileHover={{ 
                  y: -10, 
                  scale: 1.02,
                  boxShadow: "0 20px 40px rgba(0, 0, 0, 0.3)"
                }}
              >
                {/* Animated background gradient */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-yellow-600/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  initial={{ scale: 0.8 }}
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.5 }}
                />
                
                {/* Icon container with enhanced animation */}
                <motion.div
                  className="w-12 h-12 bg-gradient-to-br from-yellow-600/30 to-yellow-800/30 rounded-lg flex items-center justify-center mb-6 relative z-10"
                  initial={{ rotate: -10, scale: 0.8 }}
                  whileInView={{ rotate: 0, scale: 1 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: index * 0.15 + 0.2,
                    type: "spring",
                    stiffness: 200
                  }}
                  whileHover={{ 
                    rotate: 5, 
                    scale: 1.1,
                    boxShadow: "0 10px 20px rgba(234, 179, 8, 0.3)"
                  }}
                >
                  <motion.div
                    initial={{ scale: 0.5, opacity: 0 }}
                    whileInView={{ scale: 1, opacity: 1 }}
                    transition={{ 
                      duration: 0.4, 
                      delay: index * 0.15 + 0.4
                    }}
                  >
                    {value.icon}
                  </motion.div>
                </motion.div>
                
                {/* Title with slide-in animation */}
                <motion.h3
                  className="text-xl font-bold mb-3 text-white relative z-10"
                  initial={{ x: -20, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: index * 0.15 + 0.3
                  }}
                  whileHover={{ x: 5 }}
                >
                  {value.title}
                </motion.h3>
                
                {/* Description with fade-in animation */}
                <motion.p
                  className="text-gray-300 relative z-10"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: index * 0.15 + 0.5
                  }}
                >
                  {value.description}
                </motion.p>
                
                {/* Decorative corner element */}
                <motion.div
                  className="absolute top-0 right-0 w-8 h-8 border-t-2 border-r-2 border-yellow-500/30 rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  initial={{ scale: 0 }}
                  whileHover={{ scale: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="py-16 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold text-white mb-2">Trusted by prestigious brands worldwide</h2>
            <p className="text-gray-400">Partnering with industry leaders to deliver exceptional results</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center justify-items-center">
            {brands.map((brand, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="h-12 relative grayscale hover:grayscale-0 transition-all duration-300"
              >
                <Image
                  src={brand.logo || "/placeholder.svg"}
                  alt={brand.name}
                  width={120}
                  height={48}
                  className="object-contain h-full w-auto"
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-24 bg-gray-950 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
              <Sparkles className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-400 text-sm font-medium">Tailored Services For Every Vision</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our <span className="text-yellow-400">Services</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We deliver comprehensive software solutions designed to accelerate your business growth
              and digital transformation.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.slice(0, 6).map((service, index) => (
              <motion.div
                key={index}
                className="group bg-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-800 hover:border-yellow-500/50 transition-all duration-300 hover:bg-gray-900/70"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center mb-6">
                  <div className="w-14 h-14 bg-yellow-500/10 rounded-lg flex items-center justify-center text-yellow-400 group-hover:bg-yellow-500/20 transition-colors duration-300">
                    {service.icon}
                  </div>
                </div>

                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-yellow-400 transition-colors duration-300">
                  {service.title}
                </h3>

                <p className="text-gray-300 mb-6 leading-relaxed">
                  {service.description}
                </p>

                <Link
                  href={service.link}
                  className="inline-flex items-center text-yellow-400 hover:text-yellow-300 font-medium group/link"
                >
                  Learn More
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/link:translate-x-1" />
                </Link>
              </motion.div>
            ))}
          </div>

          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
          >
            <Link
              href="/services"
              className="inline-flex items-center px-8 py-4 bg-yellow-500 text-black font-semibold rounded-lg hover:bg-yellow-400 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-yellow-500/25"
            >
              View All Services
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-24 bg-black relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
              <Sparkles className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-400 text-sm font-medium">Why Choose Lunar Studio</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Excellence in Every <span className="text-yellow-400">Detail</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We combine technical expertise with creative innovation to deliver solutions that drive real business results.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Brain className="h-8 w-8" />,
                title: "AI-Powered Innovation",
                description: "Leverage cutting-edge artificial intelligence to automate processes and enhance user experiences."
              },
              {
                icon: <Shield className="h-8 w-8" />,
                title: "Enterprise Security",
                description: "Bank-level security protocols to protect your data and ensure compliance with industry standards."
              },
              {
                icon: <Zap className="h-8 w-8" />,
                title: "Lightning Fast",
                description: "Optimized performance and rapid deployment to get your solutions to market faster."
              },
              {
                icon: <Users className="h-8 w-8" />,
                title: "Expert Team",
                description: "Seasoned professionals with deep expertise across multiple technologies and industries."
              },
              {
                icon: <Globe className="h-8 w-8" />,
                title: "Global Support",
                description: "24/7 worldwide support to ensure your systems run smoothly across all time zones."
              },
              {
                icon: <Target className="h-8 w-8" />,
                title: "Results Driven",
                description: "Focused on delivering measurable outcomes that directly impact your business growth."
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="group text-center"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-500/10 rounded-full text-yellow-400 mb-6 group-hover:bg-yellow-500/20 transition-colors duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-gray-950 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-yellow-500/10 border border-yellow-500/20 mb-6">
              <Sparkles className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-400 text-sm font-medium">Trusted by Innovation</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              What Our <span className="text-yellow-400">Clients Say</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Discover why leading companies trust us with their most critical digital initiatives.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-800"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center mb-6">
                  <Image
                    src={testimonial.image}
                    alt={testimonial.author}
                    width={60}
                    height={60}
                    className="rounded-full object-cover"
                  />
                  <div className="ml-4">
                    <h4 className="text-white font-semibold">{testimonial.author}</h4>
                    <p className="text-gray-400 text-sm">{testimonial.title}</p>
                  </div>
                </div>
                <p className="text-gray-300 leading-relaxed italic">"{testimonial.quote}"</p>
                <div className="flex text-yellow-400 mt-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-current" />
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-black relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Ready to Transform Your
              <br />
              <span className="text-yellow-400">Digital Future?</span>
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Let's discuss how our custom software solutions can accelerate your business growth
              and drive innovation in your industry.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center justify-center px-10 py-5 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-bold text-lg rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-yellow-500/25"
              >
                <Phone className="mr-3 h-6 w-6" />
                Start Your Project
                <ArrowRight className="ml-3 h-6 w-6" />
              </Link>
              <Link
                href="/portfolio"
                className="inline-flex items-center justify-center px-10 py-5 border-2 border-gray-600 text-white font-bold text-lg rounded-lg hover:border-yellow-500 hover:bg-yellow-500/10 transition-all duration-300"
              >
                View Our Work
                <ExternalLink className="ml-3 h-6 w-6" />
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50 backdrop-blur-sm">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Elevate Your Digital Presence?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how Lunar A.i Studio can craft a bespoke solution tailored to your unique requirements.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg shadow-lg shadow-yellow-600/20"
              >
                Schedule a Private Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer CTA */}
      <section className="py-16 bg-black border-t border-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-6">Experience the Lunar Difference</h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/services"
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 hover:border-yellow-500 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              Our Services
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
            <Link
              href="/portfolio"
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 hover:border-yellow-500 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              Portfolio
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
            <Link
              href="/about"
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 hover:border-yellow-500 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              About Us
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
            <Link
              href="/contact"
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 hover:border-yellow-500 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              Contact Us
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>
    </main>
  )
}
