"use client"

import Link from "next/link"
import Image from "next/image"
import { useEffect, useState, useRef } from "react"
import {
  ArrowRight,
  ChevronDown,
  Check,
  Globe,
  ExternalLink,
  Shield,
  Bot,
  Zap,
  LineChart,
  Server,
  Sparkles,
  Lock,
  Search,
  Brain,
  Lightbulb,
  Crosshair,
  Activity,
  ShoppingBag,
  MessageSquare,
  Send,
  Code,
  BarChart,
  Cloud,
  Database,
  Network,
  Smartphone,
  Palette,
  ShoppingCart,
  Building,
  Settings,
  Workflow,
  FileText,
  TrendingUp,
  Phone,
  Users,
} from "lucide-react"
import { motion, useScroll, useTransform } from "framer-motion"
import WebGLBackground from "@/components/webgl-background"
import FloatingElements from "@/components/floating-elements"
import TestimonialCarousel from "@/components/testimonial-carousel"
import DynamicProjectShowcase from "@/components/dynamic-project-showcase"
import { useIsMobile } from "@/hooks/use-mobile"
import MobileServiceCard from "@/components/mobile-service-card"
import MobileSwipeCarousel from "@/components/mobile-swipe-carousel"

export default function Home() {
  const [scrollY, setScrollY] = useState(0)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const heroRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll()
  const isMobile = useIsMobile()

  const opacity = useTransform(scrollYProgress, [0, 0.2], [1, 0])
  const scale = useTransform(scrollYProgress, [0, 0.2], [1, 0.95])

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Hero background images - luxury-themed
  const heroImages = [
    "https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1920&auto=format&fit=crop",
    "https://images.unsplash.com/photo-1622547748225-3fc4abd2cca0?q=80&w=1920&auto=format&fit=crop",
  ]

  // Cycle through hero background images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % heroImages.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [heroImages.length])

  // Services data - comprehensive software offerings
  const services = [
    {
      title: "Web Development",
      description:
        "Modern, responsive web applications built with cutting-edge technologies and optimized for performance and user experience.",
      icon: <Code className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8d2ViJTIwZGV2ZWxvcG1lbnR8ZW58MHx8MHx8fDA%3D",
      link: "/services/web-app-development",
    },
    {
      title: "Android Development",
      description:
        "Native Android applications with modern UI/UX, advanced features, and seamless integration with backend services.",
      icon: <Smartphone className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YW5kcm9pZCUyMGRldmVsb3BtZW50fGVufDB8fDB8fHww",
      link: "/services/android-development",
    },
    {
      title: "MVP Development",
      description:
        "Rapid development of minimum viable products to validate your business concept and accelerate time to market.",
      icon: <BarChart className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1572177812156-58036aae439c?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bXZwJTIwZGV2ZWxvcG1lbnR8ZW58MHx8MHx8fDA%3D",
      link: "/services/mvp-development",
    },
    {
      title: "UI/UX Design",
      description:
        "Intuitive and beautiful user interfaces designed with user-centered approach and modern design principles.",
      icon: <Palette className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dWl1eCUyMGRlc2lnbnxlbnwwfHwwfHx8MA%3D%3D",
      link: "/services/uiux-design",
    },
    {
      title: "E-Commerce Solutions",
      description:
        "Complete e-commerce platforms with secure payment processing, inventory management, and customer analytics.",
      icon: <ShoppingCart className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZS1jb21tZXJjZXxlbnwwfHwwfHx8MA%3D%3D",
      link: "/services/ecommerce",
    },
    {
      title: "Real Estate Platforms",
      description:
        "Comprehensive real estate management systems with property listings, virtual tours, and client management.",
      icon: <Building className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8cmVhbCUyMGVzdGF0ZXxlbnwwfHwwfHx8MA%3D%3D",
      link: "/services/real-estate",
    },
    {
      title: "Custom Development",
      description:
        "Bespoke software solutions tailored to your specific business requirements and unique operational needs.",
      icon: <Settings className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y3VzdG9tJTIwZGV2ZWxvcG1lbnR8ZW58MHx8MHx8fDA%3D",
      link: "/services/custom-development",
    },
    {
      title: "End-to-End Development",
      description:
        "Complete software development lifecycle from concept to deployment, including maintenance and support.",
      icon: <Workflow className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZW5kJTIwdG8lMjBlbmR8ZW58MHx8MHx8fDA%3D",
      link: "/services/end-to-end-development",
    },
    {
      title: "Business Registration",
      description:
        "Digital solutions for business registration, compliance management, and regulatory reporting automation.",
      icon: <FileText className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YnVzaW5lc3MlMjByZWdpc3RyYXRpb258ZW58MHx8MHx8fDA%3D",
      link: "/services/business-registration",
    },
    {
      title: "SEO & Digital Marketing",
      description:
        "Search engine optimization and digital marketing strategies to increase online visibility and drive growth.",
      icon: <TrendingUp className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c2VvfGVufDB8fDB8fHww",
      link: "/services/seo-marketing",
    },
    {
      title: "Chatbot Development",
      description:
        "Intelligent chatbots and conversational AI solutions to enhance customer service and automate interactions.",
      icon: <MessageSquare className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y2hhdGJvdHxlbnwwfHwwfHx8MA%3D%3D",
      link: "/services/enterprise-chatbots",
    },
    {
      title: "Agentic AI Systems",
      description:
        "Autonomous AI agents that operate with intelligence and purpose, transforming how your organization makes decisions.",
      icon: <Bot className="h-10 w-10 text-yellow-500" />,
      image: "https://images.unsplash.com/photo-*************-bcc4688e7485?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YWdlbnRpYyUyMGFpfGVufDB8fDB8fHww",
      link: "/services/agentic-ai",
    },
  ]

  // Industry solutions - luxury focus
  const industries = [
    {
      name: "Financial Services",
      icon: <LineChart className="h-8 w-8 text-yellow-500" />,
      description: "Advanced AI and security solutions for banks, investment firms, and financial institutions.",
      image: "https://images.unsplash.com/photo-*************-2fceff292cdc?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Healthcare",
      icon: <Activity className="h-8 w-8 text-yellow-500" />,
      description: "HIPAA-compliant AI systems and security for patient data protection and medical research.",
      image: "https://images.unsplash.com/photo-*************-2173dba999ef?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Government & Defense",
      icon: <Shield className="h-8 w-8 text-yellow-500" />,
      description: "Mission-critical AI and cybersecurity solutions for government agencies and defense contractors.",
      image: "https://images.unsplash.com/photo-*************-8fa7962a78c8?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Energy & Utilities",
      icon: <Zap className="h-8 w-8 text-yellow-500" />,
      description: "Critical infrastructure protection and intelligent operations for energy providers.",
      image: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Enterprise Technology",
      icon: <Server className="h-8 w-8 text-yellow-500" />,
      description: "Advanced AI integration and security solutions for technology companies and platforms.",
      image: "https://images.unsplash.com/photo-**********-b99a580bb7a8?q=80&w=500&auto=format&fit=crop",
    },
    {
      name: "Luxury Retail",
      icon: <ShoppingBag className="h-8 w-8 text-yellow-500" />,
      description: "Sophisticated AI-driven customer experiences with robust security for high-end retail brands.",
      image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=500&auto=format&fit=crop",
    },
  ]

  // Real prestigious client logos
  const clients = [
    {
      name: "Goldman Sachs",
      logo: "https://upload.wikimedia.org/wikipedia/commons/6/61/Goldman_Sachs.svg",
    },
    {
      name: "Microsoft",
      logo: "https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg",
    },
    {
      name: "IBM",
      logo: "https://upload.wikimedia.org/wikipedia/commons/5/51/IBM_logo.svg",
    },
    {
      name: "Deloitte",
      logo: "https://upload.wikimedia.org/wikipedia/commons/9/9d/Deloitte.svg",
    },
    {
      name: "Accenture",
      logo: "https://upload.wikimedia.org/wikipedia/commons/c/cd/Accenture.svg",
    },
    {
      name: "Oracle",
      logo: "https://upload.wikimedia.org/wikipedia/commons/5/50/Oracle_logo.svg",
    },
  ]

  // Case studies - luxury projects
  const caseStudies = [
    {
      title: "AI-Powered Fraud Detection System for Global Bank",
      category: "Financial Services",
      image: "https://images.unsplash.com/photo-*************-2fceff292cdc?q=80&w=600&auto=format&fit=crop",
      results: "Reduced fraud by 98% and saved $12M annually",
    },
    {
      title: "Zero Trust Security Implementation for Healthcare Network",
      category: "Healthcare",
      image: "https://images.unsplash.com/photo-*************-2173dba999ef?q=80&w=600&auto=format&fit=crop",
      results: "100% compliance with zero security breaches",
    },
    {
      title: "Agentic AI System for Government Intelligence Analysis",
      category: "Government",
      image: "https://images.unsplash.com/photo-*************-3a3843ee7f5d?q=80&w=600&auto=format&fit=crop",
      results: "85% faster intelligence processing with higher accuracy",
    },
  ]

  // Testimonials from luxury clients
  const testimonials = [
    {
      quote:
        "Lunar A.i Studio delivered an exceptional software solution that perfectly aligns with our brand's luxury positioning. Their attention to detail and commitment to excellence exceeded our expectations.",
      author: "Alexandra Reynolds",
      title: "CTO, Luxury Retail Group",
      image: "https://images.unsplash.com/photo-*************-b8d87734a5a2?q=80&w=200&auto=format&fit=crop",
    },
    {
      quote:
        "Working with Lunar A.i Studio has transformed our digital presence. Their team's expertise in creating sophisticated, high-performance solutions has given us a significant competitive advantage.",
      author: "Jonathan Blackwell",
      title: "CEO, Private Banking Services",
      image: "https://images.unsplash.com/photo-**********-0b93528c311a?q=80&w=200&auto=format&fit=crop",
    },
    {
      quote:
        "The bespoke software developed by Lunar A.i Studio has revolutionized how we manage our exclusive client relationships. Their understanding of luxury service standards is unparalleled.",
      author: "Victoria Harrington",
      title: "Director, Elite Hospitality Group",
      image: "https://images.unsplash.com/photo-*************-15a19d654956?q=80&w=200&auto=format&fit=crop",
    },
  ]

  // Company stats - impressive numbers
  const stats = [
    { value: 15, label: "Years of Excellence", suffix: "+" },
    { value: 250, label: "Exclusive Clients", suffix: "+" },
    { value: 500, label: "Bespoke Projects", suffix: "+" },
    { value: 100, label: "Elite Specialists", suffix: "%" },
  ]

  const heroFeatures = [
    {
      title: "Agentic AI Systems",
      icon: <Bot className="h-4 w-4 text-yellow-500" />,
    },
    {
      title: "Advanced Cybersecurity",
      icon: <Shield className="h-4 w-4 text-yellow-500" />,
    },
    {
      title: "Custom GPTs & Bots",
      icon: <MessageSquare className="h-4 w-4 text-yellow-500" />,
    },
    {
      title: "Enterprise Protection",
      icon: <Lock className="h-4 w-4 text-yellow-500" />,
    },
  ]

  // Core values - luxury software house
  const coreValues = [
    {
      title: "Intelligence",
      description:
        "We harness the power of advanced AI to create systems that learn, adapt, and deliver exceptional results.",
      icon: <Brain className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Security",
      description: "We implement uncompromising protection measures that safeguard your most valuable digital assets.",
      icon: <Shield className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Innovation",
      description:
        "We constantly push the boundaries of what's possible in AI and cybersecurity to stay ahead of emerging threats and opportunities.",
      icon: <Lightbulb className="h-6 w-6 text-yellow-500" />,
    },
    {
      title: "Precision",
      description:
        "We deliver meticulously engineered solutions with attention to every technical detail and security consideration.",
      icon: <Crosshair className="h-6 w-6 text-yellow-500" />,
    },
  ]

  const brands = [
    {
      name: "Microsoft",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Microsoft_logo.svg/2048px-Microsoft_logo.svg.png",
    },
    {
      name: "IBM",
      logo: "https://upload.wikimedia.org/wikipedia/commons/5/51/IBM_logo.svg",
    },
    {
      name: "Amazon",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/2560px-Amazon_logo.svg.png",
    },
    {
      name: "Google",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Google_2015_logo.svg/2560px-Google_2015_logo.svg.png",
    },
    {
      name: "Salesforce",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/f/f9/Salesforce.com_logo.svg/2560px-Salesforce.com_logo.svg.png",
    },
    {
      name: "Oracle",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/5/50/Oracle_logo.svg/2560px-Oracle_logo.svg.png",
    },
  ]

  return (
    <main className="flex min-h-screen flex-col bg-gray-950">
      {/* Luxury Enhanced Hero Section */}
      <section
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gray-950"
      >
        {/* Professional background with subtle depth */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-b from-black via-gray-950/95 to-gray-950"></div>
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-900/0 via-gray-900/0 to-black/80"></div>

          {/* Refined background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute top-1/4 left-1/4 w-[50rem] h-[50rem] rounded-full bg-gradient-to-br from-yellow-900/5 to-yellow-500/5 blur-3xl transform -translate-x-1/2 -translate-y-1/2 animate-slow-pulse"></div>
              <div className="absolute top-3/4 right-1/4 w-[45rem] h-[45rem] rounded-full bg-gradient-to-br from-amber-900/5 to-amber-500/5 blur-3xl transform translate-x-1/2 -translate-y-1/2 animate-slow-pulse animation-delay-2000"></div>
              <div className="absolute top-2/4 left-3/4 w-[40rem] h-[40rem] rounded-full bg-gradient-to-br from-yellow-700/5 to-yellow-300/5 blur-3xl transform -translate-x-1/2 -translate-y-1/2 animate-slow-pulse animation-delay-4000"></div>
            </div>
          </div>

          {/* Subtle grid overlay */}
          <div className="absolute inset-0 z-0 opacity-5 bg-[url('/grid-pattern.svg')] bg-fixed"></div>
        </div>

        {/* Animated particles with subtle depth */}
        <motion.div className="absolute inset-0 z-0" style={{ opacity, scale }}>
          <WebGLBackground />
        </motion.div>

        {/* Professional content container */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 pt-16 md:pt-24">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-center">
            {/* Main content column */}
            <motion.div
              className="lg:col-span-6 lg:col-start-1"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              {/* Refined badge */}
              <motion.div
                className="mb-8 inline-block relative overflow-hidden"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-600/30 px-6 py-2 rounded-full backdrop-blur-sm border border-yellow-500/20">
                  <span className="text-yellow-300 text-sm font-medium flex items-center">
                    <Sparkles className="h-4 w-4 mr-2" />
                    <span className="relative tracking-wide">
                      PREMIER SOLUTIONS
                      <motion.span
                        className="absolute bottom-0 left-0 h-[1px] bg-gradient-to-r from-transparent via-yellow-400/40 to-transparent w-full"
                        initial={{ scaleX: 0 }}
                        animate={{ scaleX: 1 }}
                        transition={{ duration: 1.5, delay: 1, ease: "easeInOut" }}
                      />
                    </span>
                  </span>
                </div>
              </motion.div>

              {/* Professional headline */}
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 tracking-tight leading-none">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="overflow-hidden"
                >
                  <motion.span
                    className="block text-transparent bg-clip-text bg-gradient-to-r from-white via-gray-100 to-gray-300 mb-2"
                    initial={{ y: "100%" }}
                    animate={{ y: 0 }}
                    transition={{ duration: 0.7, delay: 0.4, ease: "easeOut" }}
                  >
                    Enterprise-Grade
                  </motion.span>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="overflow-hidden"
                >
                  <motion.span
                    className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-500 via-yellow-400 to-amber-300"
                    initial={{ y: "100%" }}
                    animate={{ y: 0 }}
                    transition={{ duration: 0.7, delay: 0.6, ease: "easeOut" }}
                  >
                    Digital Solutions
                  </motion.span>
                </motion.div>
              </h1>

              {/* Professional description */}
              <motion.p
                className="text-gray-300 text-xl mb-8 font-light max-w-3xl leading-relaxed"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 1.2 }}
              >
                <span className="text-yellow-400 font-medium">Lunar A.i Studio</span> delivers cutting-edge software solutions 
                that transform businesses through innovative technology, artificial intelligence, and unparalleled expertise.
              </motion.p>

              {/* 24/7 Availability Badge */}
              <motion.div
                className="mb-8 flex items-center space-x-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.4 }}
              >
                <div className="flex items-center space-x-2 bg-green-900/30 border border-green-500/30 px-4 py-2 rounded-full backdrop-blur-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Available 24/7</span>
                </div>
                <div className="text-gray-400 text-sm">
                  <span className="font-medium">Global Support</span> • <span>Instant Response</span>
                </div>
              </motion.div>

              {/* Professional CTA buttons */}
              <motion.div
                className="flex flex-col sm:flex-row gap-6 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.6 }}
              >
                <Link
                  href="/contact"
                  className="relative overflow-hidden bg-gradient-to-r from-yellow-600 to-yellow-700 text-white px-8 py-4 rounded-lg font-medium flex items-center justify-center group shadow-lg shadow-yellow-900/10 hover:shadow-yellow-900/20 transition-all duration-300 hover:scale-105"
                >
                  <span className="relative z-10 flex items-center">
                    <Phone className="mr-2 h-5 w-5" />
                    Schedule Free Consultation
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </span>
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-yellow-700 to-yellow-800"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: 0 }}
                    transition={{ duration: 0.4 }}
                  />
                </Link>
                <Link
                  href="/services"
                  className="relative group border border-gray-700 bg-gray-900/50 backdrop-blur-sm text-white px-8 py-4 rounded-lg font-medium flex items-center justify-center hover:border-yellow-500 transition-all duration-300 overflow-hidden hover:scale-105"
                >
                  <span className="relative z-10 flex items-center">
                    Explore Our Services
                    <motion.div className="ml-2" whileHover={{ x: 4 }} transition={{ duration: 0.2 }}>
                      <ExternalLink className="h-5 w-5" />
                    </motion.div>
                  </span>
                  <motion.div className="absolute inset-0 bg-gray-800/0 group-hover:bg-gray-800/80 transition-colors duration-300" />
                </Link>
              </motion.div>

              {/* Social Media Links */}
              <motion.div
                className="flex items-center space-x-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.8 }}
              >
                <span className="text-gray-400 text-sm font-medium">Follow our journey:</span>
                <div className="flex space-x-4">
                  <a
                    href="https://linkedin.com/company/lunar-ai-studio"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-blue-500/25"
                    aria-label="LinkedIn"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </a>
                  <a
                    href="https://twitter.com/lunar_ai_studio"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-400 to-blue-500 flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-blue-400/25"
                    aria-label="Twitter"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                  </a>
                  <a
                    href="https://github.com/lunar-ai-studio"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 rounded-lg bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-gray-500/25"
                    aria-label="GitHub"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="w-10 h-10 rounded-lg bg-gradient-to-br from-red-600 to-red-700 flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-lg hover:shadow-red-500/25"
                    aria-label="Email"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </a>
                </div>
              </motion.div>

              {/* Trust Indicators */}
              <motion.div
                className="flex items-center space-x-6 pt-6 border-t border-gray-800/50"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 2.0 }}
              >
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-400 text-sm">Enterprise Security</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Globe className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-400 text-sm">Global Reach</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Check className="h-5 w-5 text-yellow-500" />
                  <span className="text-gray-400 text-sm">Certified Solutions</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Right content column - Dynamic Project Showcase */}
            <motion.div
              className="lg:col-span-5 hidden lg:flex justify-center items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.9 }}
            >
              <div className="relative w-full h-[600px]">
                {/* Premium glass morphism effect */}
                <div className="absolute -top-8 -left-8 w-60 h-60 bg-yellow-500/5 rounded-full filter blur-3xl animate-pulse"></div>
                <div className="absolute -bottom-16 -right-8 w-60 h-60 bg-blue-500/5 rounded-full filter blur-3xl animate-pulse animation-delay-2000"></div>

                {/* Dynamic Project Showcase */}
                <DynamicProjectShowcase />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Global Presence */}
      <section className="py-6 bg-gray-900 border-b border-gray-800 relative">
        <div className="absolute inset-0 opacity-5">
          <Image
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1920&auto=format&fit=crop"
            alt="World map"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex flex-wrap justify-center items-center gap-8">
            <div className="flex items-center">
              <Globe className="h-5 w-5 text-yellow-400 mr-2" />
              <span className="text-gray-300 text-sm font-medium">Global Presence</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-gray-400 text-sm">North America</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-gray-400 text-sm">Europe</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-gray-400 text-sm">Asia Pacific</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-gray-400 text-sm">Middle East</span>
            </div>
          </div>
        </div>
      </section>

      {/* Core Values Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?q=80&w=1920&auto=format&fit=crop"
            alt="Core Values background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Core Values
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              The Pillars of Our Excellence
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our commitment to these fundamental principles guides every aspect of our work and ensures we deliver
              nothing short of excellence.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {coreValues.map((value, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800 hover:border-yellow-500 transition-all duration-500 group relative overflow-hidden"
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ 
                  duration: 0.6, 
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 100,
                  damping: 15
                }}
                viewport={{ once: true, margin: "-50px" }}
                whileHover={{ 
                  y: -10, 
                  scale: 1.02,
                  boxShadow: "0 20px 40px rgba(0, 0, 0, 0.3)"
                }}
              >
                {/* Animated background gradient */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-yellow-600/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  initial={{ scale: 0.8 }}
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.5 }}
                />
                
                {/* Icon container with enhanced animation */}
                <motion.div
                  className="w-12 h-12 bg-gradient-to-br from-yellow-600/30 to-yellow-800/30 rounded-lg flex items-center justify-center mb-6 relative z-10"
                  initial={{ rotate: -10, scale: 0.8 }}
                  whileInView={{ rotate: 0, scale: 1 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: index * 0.15 + 0.2,
                    type: "spring",
                    stiffness: 200
                  }}
                  whileHover={{ 
                    rotate: 5, 
                    scale: 1.1,
                    boxShadow: "0 10px 20px rgba(234, 179, 8, 0.3)"
                  }}
                >
                  <motion.div
                    initial={{ scale: 0.5, opacity: 0 }}
                    whileInView={{ scale: 1, opacity: 1 }}
                    transition={{ 
                      duration: 0.4, 
                      delay: index * 0.15 + 0.4
                    }}
                  >
                    {value.icon}
                  </motion.div>
                </motion.div>
                
                {/* Title with slide-in animation */}
                <motion.h3
                  className="text-xl font-bold mb-3 text-white relative z-10"
                  initial={{ x: -20, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: index * 0.15 + 0.3
                  }}
                  whileHover={{ x: 5 }}
                >
                  {value.title}
                </motion.h3>
                
                {/* Description with fade-in animation */}
                <motion.p
                  className="text-gray-300 relative z-10"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: index * 0.15 + 0.5
                  }}
                >
                  {value.description}
                </motion.p>
                
                {/* Decorative corner element */}
                <motion.div
                  className="absolute top-0 right-0 w-8 h-8 border-t-2 border-r-2 border-yellow-500/30 rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                  initial={{ scale: 0 }}
                  whileHover={{ scale: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="py-16 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold text-white mb-2">Trusted by prestigious brands worldwide</h2>
            <p className="text-gray-400">Partnering with industry leaders to deliver exceptional results</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center justify-items-center">
            {brands.map((brand, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="h-12 relative grayscale hover:grayscale-0 transition-all duration-300"
              >
                <Image
                  src={brand.logo || "/placeholder.svg"}
                  alt={brand.name}
                  width={120}
                  height={48}
                  className="object-contain h-full w-auto"
                />
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section - Enhanced Grid Layout */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1550751827-4bd374c3f58b?q=80&w=1920&auto=format&fit=crop"
            alt="Services background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Exclusive Services
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Bespoke Digital Solutions
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We craft tailored technology solutions that reflect the unique identity and requirements of your luxury
              brand.
            </p>
          </motion.div>

          {isMobile ? (
            <div className="px-4">
              <MobileSwipeCarousel>
                {services.map((service, index) => (
                  <div key={index} className="px-2">
                    <MobileServiceCard
                      title={service.title}
                      description={service.description}
                      icon={service.icon.type}
                      image={service.image}
                      link={service.link}
                      index={index}
                    />
                  </div>
                ))}
              </MobileSwipeCarousel>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <motion.div
                  key={index}
                  className="relative group rounded-xl overflow-hidden h-[400px] border border-gray-800 hover:border-yellow-500 transition-all duration-500"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent z-10"></div>
                  <Image
                    src={service.image || "/placeholder.svg?height=400&width=300"}
                    alt={service.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 z-20 flex flex-col justify-end p-6">
                    <div className="mb-4 bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 w-16 h-16 rounded-full flex items-center justify-center transform transition-transform duration-500 group-hover:scale-110 shadow-lg">
                      {service.icon}
                    </div>
                    <h3 className="text-2xl font-bold mb-3 text-white">{service.title}</h3>
                    <p className="text-gray-300 mb-6">{service.description}</p>
                    <Link
                      href={service.link}
                      className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors w-fit"
                    >
                      Discover More
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </div>
                  <div className="absolute top-4 right-4 z-20 bg-yellow-600/80 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <ArrowRight className="h-5 w-5 text-white" />
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link
              href="/services"
              className="bg-gradient-to-r from-yellow-600/20 to-yellow-800/20 border border-yellow-500 text-yellow-400 hover:bg-yellow-900/20 px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors backdrop-blur-sm"
            >
              Explore All Services
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Industry Solutions */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?q=80&w=1920&auto=format&fit=crop"
            alt="Industry Solutions background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Industry Expertise
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Tailored for Luxury Industries
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We understand the unique challenges and opportunities within luxury sectors, delivering solutions that
              elevate your brand's digital presence.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {industries.map((industry, index) => (
              <motion.div
                key={index}
                className="relative group overflow-hidden rounded-xl"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent z-10"></div>
                <Image
                  src={industry.image || "/placeholder.svg?height=250&width=400"}
                  alt={industry.name}
                  width={400}
                  height={250}
                  className="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 z-20 flex flex-col justify-end p-6">
                  <div className="flex items-start mb-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-yellow-600/80 to-yellow-800/80 rounded-full flex items-center justify-center mr-4 flex-shrink-0 shadow-lg">
                      {industry.icon}
                    </div>
                    <h3 className="text-xl font-bold text-white">{industry.name}</h3>
                  </div>
                  <p className="text-gray-300">{industry.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1920&auto=format&fit=crop"
            alt="Testimonials background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Client Testimonials
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              What Our Clients Say
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover why prestigious brands choose Lunar A.i Studio for their most critical digital initiatives.
            </p>
          </motion.div>

          <TestimonialCarousel testimonials={testimonials} />
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1920&auto=format&fit=crop"
            alt="Case Studies background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-yellow-900/50 to-yellow-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-yellow-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Featured Projects
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Our Success Stories
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Explore how our bespoke solutions have transformed businesses across luxury industries.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {caseStudies.map((study, index) => (
              <motion.div
                key={index}
                className="bg-gray-900/70 backdrop-blur-sm rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                  <div className="absolute top-0 left-0 bg-gradient-to-r from-yellow-600 to-yellow-700 text-white text-xs px-3 py-1">
                    {study.category}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{study.title}</h3>
                  <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3 mb-4">
                    <div className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-400 font-medium">{study.results}</span>
                    </div>
                  </div>
                  <Link
                    href="/portfolio"
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    View Case Study
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/portfolio"
              className="bg-gradient-to-r from-yellow-600/20 to-yellow-800/20 border border-yellow-500 text-yellow-400 hover:bg-yellow-900/20 px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors backdrop-blur-sm"
            >
              View All Case Studies
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50 backdrop-blur-sm">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Elevate Your Digital Presence?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how Lunar A.i Studio can craft a bespoke solution tailored to your unique requirements.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg shadow-lg shadow-yellow-600/20"
              >
                Schedule a Private Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer CTA */}
      <section className="py-16 bg-black border-t border-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-6">Experience the Lunar Difference</h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/services"
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 hover:border-yellow-500 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              Our Services
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
            <Link
              href="/portfolio"
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 hover:border-yellow-500 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              Portfolio
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
            <Link
              href="/about"
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 hover:border-yellow-500 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              About Us
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
            <Link
              href="/contact"
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 hover:border-yellow-500 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              Contact Us
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>
    </main>
  )
}
