"use client"

import { useState } from "react"
import {
  Brain,
  Shield,
  Code,
  BarChart,
  Smartphone,
  Database,
  LineChart,
  Lock,
  Bot,
  Network,
  Cpu,
  Zap,
  type LucideIcon,
} from "lucide-react"
import { motion } from "framer-motion"

interface ServiceCardProps {
  title: string
  description: string
  icon: string
  index?: number
}

export default function ServiceCard({ title, description, icon, index = 0 }: ServiceCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const getIcon = (): LucideIcon => {
    switch (icon) {
      case "Brain":
        return Brain
      case "Shield":
        return Shield
      case "Code":
        return Code
      case "BarChart":
        return BarChart
      case "Smartphone":
        return Smartphone
      case "Database":
        return Database
      case "LineChart":
        return LineChart
      case "Lock":
        return Lock
      case "Bot":
        return Bot
      case "Network":
        return Network
      case "Cpu":
        return Cpu
      case "Zap":
        return Zap
      default:
        return Brain
    }
  }

  const Icon = getIcon()

  // Generate a unique gradient based on the icon or index
  const getGradient = () => {
    const gradients = [
      "from-purple-600 to-blue-500",
      "from-blue-600 to-cyan-500",
      "from-emerald-600 to-teal-500",
      "from-orange-600 to-amber-500",
      "from-pink-600 to-rose-500",
      "from-indigo-600 to-purple-500",
      "from-cyan-600 to-blue-500",
      "from-yellow-500 to-amber-600",
    ]
    return gradients[index % gradients.length]
  }

  return (
    <motion.div
      className="relative bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 h-full"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div
        className="absolute inset-0 bg-gradient-to-br opacity-0 transition-opacity duration-300 -z-10"
        style={{ opacity: isHovered ? 0.05 : 0 }}
      />

      <div className="p-6 flex flex-col h-full">
        <div
          className={`w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br ${getGradient()} text-white transform transition-all duration-300 ${isHovered ? "scale-110" : ""}`}
        >
          <Icon className="h-8 w-8" />
        </div>

        <h3 className="text-xl font-bold mb-3 transition-colors duration-300">{title}</h3>

        <p className="text-gray-600 mb-6 flex-grow">{description}</p>

        <motion.div
          className="mt-auto"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: isHovered ? 1 : 0, y: isHovered ? 0 : 10 }}
          transition={{ duration: 0.2 }}
        >
          <button className="text-sm font-medium inline-flex items-center text-black hover:underline">
            Learn more
            <svg
              className="ml-1 w-4 h-4 transition-transform duration-300 transform"
              style={{ transform: isHovered ? "translateX(4px)" : "translateX(0)" }}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </button>
        </motion.div>
      </div>

      <div
        className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r transition-transform duration-300 transform origin-left"
        style={{
          transform: isHovered ? "scaleX(1)" : "scaleX(0)",
          backgroundImage: `linear-gradient(to right, var(--tw-gradient-stops))`,
        }}
      />
    </motion.div>
  )
}
