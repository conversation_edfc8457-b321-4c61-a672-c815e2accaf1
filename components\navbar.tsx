"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Menu, X, Globe, ChevronDown, Phone } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [languageMenuOpen, setLanguageMenuOpen] = useState(false)
  const [servicesMenuOpen, setServicesMenuOpen] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navigation = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Services", href: "/services" },
    { name: "Industries", href: "/industries" },
    { name: "Portfolio", href: "/portfolio" },
    { name: "Blog", href: "/blog" },
    { name: "Contact", href: "/contact" },
  ]

  const languages = [
    { code: "en", name: "English" },
    { code: "es", name: "Español" },
    { code: "fr", name: "Français" },
    { code: "de", name: "Deutsch" },
    { code: "zh", name: "中文" },
    { code: "ja", name: "日本語" },
    { code: "ar", name: "العربية" },
  ]

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? "bg-black/95 backdrop-blur-md border-b border-gray-800/50 py-3" 
          : "bg-transparent py-5"
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="relative">
              <span className="text-2xl font-bold text-white group-hover:text-yellow-500 transition-colors duration-300">
                Lunar
              </span>
              <span className="absolute -top-1 -right-1 text-yellow-500 text-sm font-bold">.</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigation.map((link) => (
              <Link
                key={link.name}
                href={link.href}
                className={`text-gray-300 hover:text-yellow-500 transition-colors duration-200 font-medium ${
                  pathname === link.href ? "text-yellow-500" : ""
                }`}
              >
                {link.name}
              </Link>
            ))}
          </nav>

          {/* Contact Number */}
          <div className="hidden lg:flex items-center space-x-4">
            <a
              href="tel:+923194821372"
              className="flex items-center space-x-2 text-gray-300 hover:text-yellow-500 transition-colors duration-200 font-medium"
            >
              <Phone className="h-4 w-4" />
              <span>+92 ************</span>
            </a>
          </div>

          {/* Right Section */}
          <div className="hidden lg:flex items-center space-x-6">
          {/* CTA Button */}
            <Link
              href="/contact"
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:shadow-yellow-600/25"
            >
              Get Started
            </Link>

            {/* Language Selector */}
            <div className="relative">
              <button
                className="flex items-center space-x-1 text-sm font-medium text-gray-300 hover:text-white transition-colors duration-200"
                onClick={() => setLanguageMenuOpen(!languageMenuOpen)}
                onMouseEnter={() => setLanguageMenuOpen(true)}
                onMouseLeave={() => setLanguageMenuOpen(false)}
              >
                <Globe className="h-4 w-4" />
                <span>EN</span>
                <ChevronDown className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {languageMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: 10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: 10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-0 mt-3 w-48 rounded-lg shadow-xl bg-gray-900/95 backdrop-blur-md border border-gray-800/50 py-2 z-50"
                    onMouseEnter={() => setLanguageMenuOpen(true)}
                    onMouseLeave={() => setLanguageMenuOpen(false)}
                  >
                    {languages.map((lang) => (
                      <button
                        key={lang.code}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-800/50 hover:text-yellow-500 transition-all duration-200"
                        onClick={() => setLanguageMenuOpen(false)}
                      >
                        {lang.name}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Mobile Navigation Toggle */}
          <button 
            className="lg:hidden text-white hover:text-yellow-500 transition-colors duration-200 p-2" 
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="lg:hidden bg-black/95 backdrop-blur-md border-t border-gray-800/50"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="container mx-auto px-4 py-6">
              <nav className="flex flex-col space-y-4">
                {navigation.map((link) => (
                  <Link
                    key={link.name}
                    href={link.href}
                    className={`text-lg font-medium transition-all duration-200 hover:text-yellow-500 py-2 ${
                      pathname === link.href 
                        ? "text-yellow-500" 
                        : "text-gray-300 hover:text-white"
                    }`}
                    onClick={() => setIsOpen(false)}
                  >
                    {link.name}
                  </Link>
                ))}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}
