"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageSquare, X, Send, Bot, User, Sparkles, Paperclip, ChevronRight } from "lucide-react"

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
}

const getRandomResponse = (query: string): string => {
  // Sample responses - in a real app, this would connect to an AI service
  const responses = [
    "I'd be happy to help with that! Could you provide more details?",
    "That's an interesting question about our services. We specialize in cutting-edge solutions tailored to your business needs.",
    "Thanks for your interest! Our team would be glad to discuss how we can assist with your project.",
    "Great question! Our experts have extensive experience in this area and can provide customized solutions.",
    "I understand what you're looking for. Would you like me to connect you with one of our specialists to discuss further?",
    "We have several options that might meet your needs. Could you tell me more about your specific requirements?",
    "I can definitely help with information about our AI solutions. Would you like me to explain how they work?",
    "Our cybersecurity services are designed to protect your business from evolving threats. Would you like more details?",
    "I'd recommend scheduling a consultation with our team to explore this further. Would you like me to arrange that?",
    "We have case studies similar to what you're describing. Would you like me to share some relevant examples?",
  ]

  // If the query contains specific keywords, provide more targeted responses
  if (query.toLowerCase().includes("ai") || query.toLowerCase().includes("artificial intelligence")) {
    return "Our AI solutions include RAG-based systems, agentic AI, and generative models tailored for business applications. Would you like to learn more about a specific AI technology?"
  }

  if (query.toLowerCase().includes("security") || query.toLowerCase().includes("cyber")) {
    return "Our cybersecurity services provide real-time threat detection, zero trust architecture implementation, and comprehensive security audits. How can we help protect your digital assets?"
  }

  if (query.toLowerCase().includes("marketing") || query.toLowerCase().includes("analytics")) {
    return "Our data-driven marketing solutions combine advanced analytics with strategic campaign management to maximize your ROI. Would you like to see some case studies?"
  }

  // Default to random response if no keywords match
  return responses[Math.floor(Math.random() * responses.length)]
}

const quickPrompts = [
  "Tell me about your AI solutions",
  "How can you help with cybersecurity?",
  "What marketing services do you offer?",
  "I'd like to schedule a consultation",
  "Can you share case studies?",
]

export default function AIChatbot() {
  const [isOpen, setIsOpen] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "assistant",
      content: "Hi there! I'm Luna, the AI assistant for Lunar Studio. How can I help you today?",
      timestamp: new Date(),
    },
  ])
  const [input, setInput] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // Auto-scroll to bottom of chat
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && !isExpanded) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 300)
    }
  }, [isOpen, isExpanded])

  const handleSubmit = (e: React.FormEvent, submittedMsg?: string) => {
    e.preventDefault()

    const messageText = submittedMsg || input
    if (!messageText.trim()) return

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: messageText,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setIsTyping(true)

    // Simulate AI response after a short delay
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: getRandomResponse(messageText),
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, botResponse])
      setIsTyping(false)
    }, 1500)
  }

  // Handle textarea autosize
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value)
    e.target.style.height = "auto"
    e.target.style.height = `${Math.min(e.target.scrollHeight, 150)}px`
  }

  // Handle quick prompt selection
  const handleQuickPrompt = (prompt: string) => {
    setInput(prompt)
    handleSubmit({ preventDefault: () => {} } as React.FormEvent, prompt)
    setIsExpanded(false)
  }

  return (
    <>
      {/* Chat toggle button */}
      <motion.button
        className="fixed bottom-6 right-6 z-50 p-4 rounded-full shadow-lg bg-gradient-to-r from-yellow-600 to-yellow-500 text-white"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(!isOpen)}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {isOpen ? <X className="h-6 w-6" /> : <MessageSquare className="h-6 w-6" />}
      </motion.button>

      {/* Chat window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed bottom-20 right-6 z-50 w-[360px] rounded-2xl shadow-2xl flex flex-col bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 overflow-hidden"
            initial={{ opacity: 0, y: 20, height: 0 }}
            animate={{
              opacity: 1,
              y: 0,
              height: isExpanded ? "85vh" : "500px",
              width: isExpanded ? "85vw" : "360px",
              right: isExpanded ? "50%" : "1.5rem",
              bottom: isExpanded ? "50%" : "5rem",
              x: isExpanded ? "50%" : 0,
              y: isExpanded ? "50%" : 0,
            }}
            exit={{ opacity: 0, y: 20, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Chat header */}
            <div className="bg-gradient-to-r from-gray-800 to-black p-4 flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center mr-3">
                  <Bot className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-medium text-white">Luna AI Assistant</h3>
                  <p className="text-xs text-blue-100">Lunar Studio</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-white hover:bg-white/10 rounded-full p-1"
                >
                  {isExpanded ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="4 14 10 14 10 20"></polyline>
                      <polyline points="20 10 14 10 14 4"></polyline>
                      <line x1="14" y1="10" x2="21" y2="3"></line>
                      <line x1="3" y1="21" x2="10" y2="14"></line>
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="15 3 21 3 21 9"></polyline>
                      <polyline points="9 21 3 21 3 15"></polyline>
                      <line x1="21" y1="3" x2="14" y2="10"></line>
                      <line x1="3" y1="21" x2="10" y2="14"></line>
                    </svg>
                  )}
                </button>
                <button onClick={() => setIsOpen(false)} className="text-white hover:bg-white/10 rounded-full p-1">
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Chat messages */}
            <div className="flex-grow overflow-y-auto p-4 bg-gray-50 dark:bg-gray-900">
              {isExpanded && (
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 flex items-center">
                    <Sparkles className="h-4 w-4 mr-2" />
                    How can I help you?
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    I can answer questions about our services, schedule consultations, or connect you with the right
                    team members.
                  </p>
                </div>
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`mb-4 flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                >
                  {message.role === "assistant" && (
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-white to-gray-300 flex items-center justify-center mr-2 flex-shrink-0">
                      <Bot className="h-4 w-4 text-black" />
                    </div>
                  )}

                  <div
                    className={`max-w-[80%] p-3 rounded-2xl ${
                      message.role === "user"
                        ? "bg-white text-black rounded-tr-none"
                        : "bg-gray-800 text-gray-200 shadow-sm rounded-tl-none"
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                    <p className="text-xs opacity-70 mt-1 text-right">
                      {new Date(message.timestamp).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                    </p>
                  </div>

                  {message.role === "user" && (
                    <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center ml-2 flex-shrink-0">
                      <User className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                    </div>
                  )}
                </div>
              ))}

              {isTyping && (
                <div className="mb-4 flex justify-start">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-cyan-500 flex items-center justify-center mr-2 flex-shrink-0">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                  <div className="p-3 rounded-2xl bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 shadow-sm rounded-tl-none">
                    <div className="flex space-x-1">
                      <div
                        className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                        style={{ animationDelay: "0ms" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                        style={{ animationDelay: "150ms" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                        style={{ animationDelay: "300ms" }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Quick prompts */}
            {messages.length < 3 && !isTyping && (
              <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
                <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Suggested questions:</p>
                <div className="flex flex-wrap gap-2">
                  {quickPrompts.slice(0, isExpanded ? 5 : 3).map((prompt, idx) => (
                    <button
                      key={idx}
                      className="text-xs px-3 py-1.5 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors flex items-center"
                      onClick={() => handleQuickPrompt(prompt)}
                    >
                      {prompt}
                      <ChevronRight className="h-3 w-3 ml-1" />
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Input area */}
            <form
              onSubmit={handleSubmit}
              className="p-3 border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 flex items-end"
            >
              <button
                type="button"
                className="p-2 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-full transition-colors"
              >
                <Paperclip className="h-5 w-5" />
              </button>

              <div className="flex-grow relative">
                <textarea
                  ref={inputRef}
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Type your message..."
                  className="w-full border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white resize-none overflow-hidden"
                  style={{ minHeight: "44px", maxHeight: "150px" }}
                  rows={1}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault()
                      handleSubmit(e)
                    }
                  }}
                />
              </div>

              <button
                type="submit"
                className="ml-2 p-2 rounded-full bg-white text-black disabled:opacity-50 transition-colors"
                disabled={!input.trim() || isTyping}
              >
                <Send className="h-5 w-5" />
              </button>
            </form>

            {/* Powered by notice */}
            <div className="px-4 py-2 text-center text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800">
              Powered by Lunar Studio AI
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
