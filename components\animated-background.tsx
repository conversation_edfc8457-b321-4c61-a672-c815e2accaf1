"use client"

import { useEffect, useRef, useState } from "react"

interface AnimatedBackgroundProps {
  className?: string
  particleCount?: number
  particleColor?: string
  speed?: number
}

export default function AnimatedBackground({
  className = "",
  particleCount = 100,
  particleColor = "#ffffff",
  speed = 1,
}: AnimatedBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isLowPerfDevice, setIsLowPerfDevice] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Check if device is likely low performance
    const checkPerformance = () => {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      const isOlderDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4
      setIsLowPerfDevice(isMobile || isOlderDevice)
    }

    checkPerformance()

    // Use Intersection Observer to only animate when visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting)
        })
      },
      { threshold: 0.1 },
    )

    if (canvasRef.current) {
      observer.observe(canvasRef.current)
    }

    // Delay animation start to prioritize content loading
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 700)

    return () => {
      observer.disconnect()
      clearTimeout(timer)
    }
  }, [])

  useEffect(() => {
    // Only start animation when element is visible and page has loaded
    if (!isVisible || !isLoaded) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d", { alpha: true, desynchronized: true })
    if (!ctx) return

    // Set canvas dimensions - use much lower resolution
    const resizeCanvas = () => {
      const scale = isLowPerfDevice ? 0.2 : 0.4
      canvas.width = window.innerWidth * scale
      canvas.height = window.innerHeight * scale
      canvas.style.width = `${window.innerWidth}px`
      canvas.style.height = `${window.innerHeight}px`
    }

    resizeCanvas()

    // Debounce resize events
    let resizeTimer: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimer)
      resizeTimer = setTimeout(resizeCanvas, 200)
    }

    window.addEventListener("resize", handleResize)

    // For extremely low performance devices, just draw static dots
    if (isLowPerfDevice) {
      // Draw static particles
      const staticParticleCount = Math.min(30, Math.floor(particleCount / 3))

      for (let i = 0; i < staticParticleCount; i++) {
        const x = Math.random() * canvas.width
        const y = Math.random() * canvas.height
        const size = Math.random() * 2 + 0.5
        const opacity = Math.random() * 0.5 + 0.2

        ctx.fillStyle = `${particleColor}${Math.floor(opacity * 255)
          .toString(16)
          .padStart(2, "0")}`
        ctx.beginPath()
        ctx.arc(x, y, size, 0, Math.PI * 2)
        ctx.fill()
      }

      return () => {
        window.removeEventListener("resize", handleResize)
      }
    }

    // For higher performance devices, use a simplified but still animated version
    // Create static particles
    const particles: { x: number; y: number; size: number; opacity: number }[] = []
    const adjustedParticleCount = Math.min(50, Math.floor(particleCount / 2))

    for (let i = 0; i < adjustedParticleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        size: Math.random() * 2 + 0.5,
        opacity: Math.random() * 0.5 + 0.2,
      })
    }

    // Animation loop with very low frequency updates
    let lastTime = 0
    const frameInterval = 500 // Very low fps (2fps)
    let animationFrame: number

    function animate(timestamp: number) {
      const deltaTime = timestamp - lastTime

      if (deltaTime > frameInterval) {
        lastTime = timestamp - (deltaTime % frameInterval)

        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // Draw particles
        for (let i = 0; i < particles.length; i++) {
          const p = particles[i]

          // Very subtle movement
          p.x += (Math.random() - 0.5) * speed * 0.2
          p.y += (Math.random() - 0.5) * speed * 0.2

          // Wrap around edges
          if (p.x < 0) p.x = canvas.width
          if (p.x > canvas.width) p.x = 0
          if (p.y < 0) p.y = canvas.height
          if (p.y > canvas.height) p.y = 0

          ctx.fillStyle = `${particleColor}${Math.floor(p.opacity * 255)
            .toString(16)
            .padStart(2, "0")}`
          ctx.beginPath()
          ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2)
          ctx.fill()
        }
      }

      animationFrame = requestAnimationFrame(animate)
    }

    animationFrame = requestAnimationFrame(animate)

    return () => {
      window.removeEventListener("resize", handleResize)
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [particleCount, particleColor, speed, isLowPerfDevice, isVisible, isLoaded])

  return <canvas ref={canvasRef} className={`absolute inset-0 -z-10 ${className}`} />
}
