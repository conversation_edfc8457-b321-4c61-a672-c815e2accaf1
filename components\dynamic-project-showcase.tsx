"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { 
  ShoppingCart, 
  BarChart, 
  Smartphone, 
  Building, 
  Activity,
  TrendingUp,
  Users,
  Code,
  Brain
} from "lucide-react"

interface Project {
  title: string
  status: string
  revenue: string
  users: string
  description: string
  tech: string[]
  icon: React.ReactNode
}

const featuredProjects: Project[] = [
  {
    title: "E-Commerce Platform",
    status: "Live",
    revenue: "+245% increase",
    users: "50K+ active",
    description: "Modern e-commerce solution with AI-powered recommendations and secure payment processing.",
    tech: ["React", "Node.js", "MongoDB"],
    icon: <ShoppingCart className="h-4 w-4 text-green-400" />,
  },
  {
    title: "AI-Powered Analytics",
    status: "Live",
    revenue: "+180% efficiency",
    users: "25K+ users",
    description: "Advanced analytics platform with machine learning insights and real-time data visualization.",
    tech: ["Python", "TensorFlow", "React"],
    icon: <BarChart className="h-4 w-4 text-blue-400" />,
  },
  {
    title: "Mobile Banking App",
    status: "Live",
    revenue: "+320% adoption",
    users: "100K+ users",
    description: "Secure mobile banking application with biometric authentication and real-time transactions.",
    tech: ["React Native", "Node.js", "PostgreSQL"],
    icon: <Smartphone className="h-4 w-4 text-purple-400" />,
  },
  {
    title: "Real Estate Platform",
    status: "Live",
    revenue: "+156% growth",
    users: "35K+ agents",
    description: "Comprehensive real estate management system with virtual tours and client management.",
    tech: ["Vue.js", "Laravel", "MySQL"],
    icon: <Building className="h-4 w-4 text-amber-400" />,
  },
  {
    title: "Healthcare Management",
    status: "Live",
    revenue: "+200% efficiency",
    users: "15K+ doctors",
    description: "HIPAA-compliant healthcare management system with patient data and appointment scheduling.",
    tech: ["Angular", "Java", "Oracle"],
    icon: <Activity className="h-4 w-4 text-red-400" />,
  },
]

export default function DynamicProjectShowcase() {
  const [currentProjectIndex, setCurrentProjectIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentProjectIndex((prevIndex) => (prevIndex + 1) % featuredProjects.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  const currentProject = featuredProjects[currentProjectIndex]

  return (
    <div className="relative z-10 bg-gray-900/40 backdrop-blur-xl p-5 rounded-xl border border-gray-700/50 shadow-[0_0_20px_rgba(0,0,0,0.3)] hover:shadow-[0_0_25px_rgba(200,0,0,0.2)] transition-all duration-500">
      {/* Header with title */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-br from-yellow-600 to-yellow-900 rounded-md flex items-center justify-center mr-3 shadow-lg">
            <Code className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-white">Featured Projects</h3>
            <p className="text-gray-400 text-xs">Recent successful deliveries</p>
          </div>
        </div>
        <div className="flex space-x-1.5">
          <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
          <div className="w-1.5 h-1.5 bg-amber-400 rounded-full"></div>
          <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
        </div>
      </div>

      {/* Enhanced visualization */}
      <div className="grid grid-cols-1 gap-3 mb-3">
        {/* Dynamic Project Showcase */}
        <div className="bg-gray-800/30 p-3 rounded-lg border border-gray-700/50 relative overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentProjectIndex}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center space-x-2">
                  {currentProject.icon}
                  <span className="text-gray-300 text-xs font-medium">{currentProject.title}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 text-xs">{currentProject.status}</span>
                  <div className="ml-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                </div>
              </div>

              {/* Project metrics */}
              <div className="grid grid-cols-2 gap-2 mb-2">
                <div className="bg-gray-800/50 p-1.5 rounded border border-gray-700/50">
                  <div className="flex items-center mb-0.5">
                    <TrendingUp className="h-2.5 w-2.5 text-green-400 mr-1" />
                    <span className="text-gray-300 text-[11px]">Revenue</span>
                  </div>
                  <div className="text-gray-400 text-[11px]">{currentProject.revenue}</div>
                </div>
                <div className="bg-gray-800/50 p-1.5 rounded border border-gray-700/50">
                  <div className="flex items-center mb-0.5">
                    <Users className="h-2.5 w-2.5 text-blue-400 mr-1" />
                    <span className="text-gray-300 text-[11px]">Users</span>
                  </div>
                  <div className="text-gray-400 text-[11px]">{currentProject.users}</div>
                </div>
              </div>

              <div className="text-gray-400 text-[11px] mb-2">
                {currentProject.description}
              </div>

              <div className="flex space-x-1">
                {currentProject.tech.map((tech, index) => (
                  <motion.span
                    key={tech}
                    className="bg-yellow-900/50 text-yellow-400 text-[10px] px-2 py-1 rounded"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    {tech}
                  </motion.span>
                ))}
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Project indicator dots */}
          <div className="flex justify-center space-x-1 mt-3">
            {featuredProjects.map((_, index) => (
              <motion.div
                key={index}
                className={`w-1.5 h-1.5 rounded-full transition-colors duration-300 ${
                  index === currentProjectIndex ? 'bg-yellow-500' : 'bg-gray-600'
                }`}
                whileHover={{ scale: 1.2 }}
                onClick={() => setCurrentProjectIndex(index)}
              />
            ))}
          </div>
        </div>

        {/* Project Stats - Enhanced */}
        <div className="bg-gray-800/30 p-3 rounded-lg border border-gray-700/50 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-300 text-xs font-medium">Project Portfolio</span>
            <span className="text-green-400 text-[11px]">Active</span>
          </div>

          <div className="grid grid-cols-2 gap-3 mb-2">
            <div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-[11px]">Web Apps</span>
                <span className="text-blue-400 text-[11px]">45+</span>
              </div>
              <div className="w-full bg-gray-700/50 h-1 rounded-full mt-1">
                <motion.div
                  className="bg-gradient-to-r from-blue-600 to-blue-400 h-1 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "90%" }}
                  transition={{ duration: 1.5, delay: 1 }}
                ></motion.div>
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-[11px]">Mobile Apps</span>
                <span className="text-purple-400 text-[11px]">32+</span>
              </div>
              <div className="w-full bg-gray-700/50 h-1 rounded-full mt-1">
                <motion.div
                  className="bg-gradient-to-r from-purple-600 to-purple-400 h-1 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "85%" }}
                  transition={{ duration: 1.5, delay: 1.2 }}
                ></motion.div>
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-[11px]">AI Solutions</span>
                <span className="text-amber-400 text-[11px]">28+</span>
              </div>
              <div className="w-full bg-gray-700/50 h-1 rounded-full mt-1">
                <motion.div
                  className="bg-gradient-to-r from-amber-600 to-amber-400 h-1 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "78%" }}
                  transition={{ duration: 1.5, delay: 1.3 }}
                ></motion.div>
              </div>
            </div>
            <div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-[11px]">E-commerce</span>
                <span className="text-green-400 text-[11px]">15+</span>
              </div>
              <div className="w-full bg-gray-700/50 h-1 rounded-full mt-1">
                <motion.div
                  className="bg-gradient-to-r from-green-600 to-green-400 h-1 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: "65%" }}
                  transition={{ duration: 1.5, delay: 1.4 }}
                ></motion.div>
              </div>
            </div>
          </div>

          <div className="text-gray-400 text-[11px]">
            Delivered 120+ successful projects across web, mobile, AI, and e-commerce domains.
          </div>
        </div>
      </div>

      {/* Project categories - Enhanced */}
      <div className="grid grid-cols-3 gap-2">
        {[
          {
            label: "Web Dev",
            status: "45+",
            icon: <Code className="h-2.5 w-2.5 text-blue-500" />,
            description: "Projects",
          },
          {
            label: "Mobile",
            status: "32+",
            icon: <Smartphone className="h-2.5 w-2.5 text-green-500" />,
            description: "Apps",
          },
          {
            label: "AI/ML",
            status: "28+",
            icon: <Brain className="h-2.5 w-2.5 text-purple-500" />,
            description: "Solutions",
          },
        ].map((item, index) => (
          <motion.div
            key={index}
            className="flex flex-col bg-gray-800/20 p-2 rounded-md border border-gray-700/30 hover:bg-gray-800/40 transition-colors"
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.15 + 1.5 }}
          >
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center">
                <div className="w-5 h-5 rounded-full bg-gray-800/80 flex items-center justify-center mr-1.5">
                  {item.icon}
                </div>
                <span className="text-gray-300 text-[11px]">{item.label}</span>
              </div>
              <span className="text-[10px] text-yellow-400 font-medium">{item.status}</span>
            </div>
            <span className="text-gray-400 text-[10px]">{item.description}</span>
          </motion.div>
        ))}
      </div>

      {/* Decorative elements */}
      <div className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-500/30 rounded-full blur-sm"></div>
      <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-blue-500/30 rounded-full blur-sm"></div>
    </div>
  )
} 