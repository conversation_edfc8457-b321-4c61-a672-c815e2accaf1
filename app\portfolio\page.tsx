"use client"

import Image from "next/image"
import Link from "next/link"
import { ArrowRight, ExternalLink } from "lucide-react"
import { motion } from "framer-motion"

export default function PortfolioPage() {
  // Portfolio projects data - inspired by TechloSet's style
  const portfolioProjects = [
    {
      title: "AI-Powered E-commerce Platform",
      description: "Scalable web and mobile app solutions tailored for businesses, ensuring performance, security, and seamless user experience with cutting-edge AI integration.",
      category: "Web & Mobile App Development",
      image: "https://images.unsplash.com/photo-**********-ec7c0e9f34b1?q=80&w=800&auto=format&fit=crop",
      logo: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?q=80&w=200&auto=format&fit=crop",
      link: "/portfolio/ai-ecommerce-platform",
    },
    {
      title: "Smart Healthcare Management System",
      description: "Comprehensive healthcare platform with AI-powered patient management, telemedicine capabilities, and secure data handling.",
      category: "Healthcare Technology",
      image: "https://images.unsplash.com/photo-*************-2173dba999ef?q=80&w=800&auto=format&fit=crop",
      logo: "https://images.unsplash.com/photo-**********-5c350d0d3c56?q=80&w=200&auto=format&fit=crop",
      link: "/portfolio/healthcare-management",
    },
    {
      title: "FinTech Mobile Banking App",
      description: "Secure and intuitive mobile banking solution with advanced fraud detection and personalized financial insights.",
      category: "Mobile App Development",
      image: "https://images.unsplash.com/photo-**********-824ae1b704d3?q=80&w=800&auto=format&fit=crop",
      logo: "https://images.unsplash.com/photo-*************-9c2a0a7236a3?q=80&w=200&auto=format&fit=crop",
      link: "/portfolio/fintech-banking-app",
    },
    {
      title: "Enterprise Resource Planning System",
      description: "Comprehensive ERP solution designed to streamline business operations with AI-powered analytics and automation.",
      category: "Enterprise Solutions",
      image: "https://images.unsplash.com/photo-*************-afdab827c52f?q=80&w=800&auto=format&fit=crop",
      logo: "https://images.unsplash.com/photo-*************-ac291c95aaa9?q=80&w=200&auto=format&fit=crop",
      link: "/portfolio/erp-system",
    },
    {
      title: "Real-time Logistics Platform",
      description: "Advanced logistics management platform with real-time tracking, route optimization, and predictive analytics.",
      category: "Web & Mobile App Development",
      image: "https://images.unsplash.com/photo-*************-ad8dd3c8310d?q=80&w=800&auto=format&fit=crop",
      logo: "https://images.unsplash.com/photo-*************-48f60103fc96?q=80&w=200&auto=format&fit=crop",
      link: "/portfolio/logistics-platform",
    },
    {
      title: "AI-Driven Learning Management System",
      description: "Intelligent educational platform with personalized learning paths, progress tracking, and adaptive content delivery.",
      category: "AI Based Solution",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=800&auto=format&fit=crop",
      logo: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=200&auto=format&fit=crop",
      link: "/portfolio/ai-learning-system",
    },
  ]

  return (
    <main className="pt-24 bg-gray-950">
      {/* Hero Section */}
      <section className="py-24 bg-gray-950 relative">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
              Grand Portfolio
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              At Lunar Studio our work reflects excellence, driving measurable growth & success for forward-thinking enterprises.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Portfolio Projects */}
      <section className="py-24 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-32">
            {portfolioProjects.map((project, index) => (
              <motion.div
                key={index}
                className="relative"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                {/* Project Image */}
                <div className="relative rounded-2xl overflow-hidden mb-8">
                  <Image
                    src={project.image}
                    alt={project.title}
                    width={1200}
                    height={600}
                    className="w-full h-auto object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                </div>

                {/* Project Logo */}
                <div className="flex justify-center mb-8">
                  <div className="relative w-24 h-24 rounded-full overflow-hidden bg-white p-2">
                    <Image
                      src={project.logo}
                      alt={`${project.title} Logo`}
                      fill
                      className="object-contain"
                    />
                  </div>
                </div>

                {/* Project Content */}
                <div className="text-center max-w-4xl mx-auto">
                  <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                    {project.title}
                  </h2>
                  <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                    {project.description}
                  </p>
                  <div className="inline-flex items-center px-6 py-3 bg-yellow-500/10 border border-yellow-500/30 rounded-full">
                    <span className="text-yellow-400 font-medium">{project.category}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="mb-8">
              <svg className="w-16 h-16 text-yellow-400 mx-auto mb-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Turning ideas into impactful solutions.
              </h2>
            </div>

            <Link
              href="/contact"
              className="inline-flex items-center px-10 py-5 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-bold text-lg rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-yellow-500/25"
            >
              Get Started
              <ArrowRight className="ml-3 h-6 w-6" />
            </Link>
          </motion.div>
        </div>
      </section>
    </main>
  )
}
