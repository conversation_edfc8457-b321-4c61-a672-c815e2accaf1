"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON><PERSON>, Star, Sparkles } from "lucide-react"
import { motion } from "framer-motion"
import MoonPhaseIndicator from "@/components/moon-phase-indicator"
import LunarGlowCard from "@/components/lunar-glow-card"

export default function PortfolioPage() {
  // Featured projects data
  const featuredProjects = [
    {
      title: "Autonomous Security Operations Center",
      client: "Global Financial Institution",
      category: "Financial Services",
      description:
        "An AI-powered security operations center that autonomously detects, analyzes, and responds to threats across the organization's global infrastructure.",
      results: [
        "98% reduction in false positives",
        "85% faster threat detection and response",
        "24/7 autonomous monitoring without human intervention",
      ],
      image: "https://images.unsplash.com/photo-1601597111158-2fceff292cdc?q=80&w=800&auto=format&fit=crop",
      link: "/portfolio/autonomous-soc",
    },
    {
      title: "Zero Trust Implementation",
      client: "Healthcare Network",
      category: "Healthcare",
      description:
        "A comprehensive zero trust security architecture for a network of hospitals and healthcare providers, protecting sensitive patient data and critical systems.",
      results: [
        "100% compliance with HIPAA regulations",
        "Zero security breaches since implementation",
        "90% reduction in unauthorized access attempts",
      ],
      image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=800&auto=format&fit=crop",
      link: "/portfolio/zero-trust-healthcare",
    },
    {
      title: "Agentic AI for Intelligence Analysis",
      client: "Government Agency",
      category: "Government",
      description:
        "A sophisticated multi-agent AI system that processes vast amounts of intelligence data to identify patterns, connections, and potential threats.",
      results: [
        "85% faster intelligence processing",
        "73% increase in threat identification accuracy",
        "Significant reduction in analyst workload",
      ],
      image: "https://images.unsplash.com/photo-1575517111839-3a3843ee7f5d?q=80&w=800&auto=format&fit=crop",
      link: "/portfolio/intelligence-ai",
    },
  ]

  // Before/After projects
  const beforeAfterProjects = [
    {
      title: "Legacy Security System Modernization",
      description:
        "Transformation of an outdated security infrastructure into an AI-enhanced protection system for a critical infrastructure provider.",
      beforeImage: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?q=80&w=700&auto=format&fit=crop",
      afterImage: "https://images.unsplash.com/photo-**********-ef010cbdcc31?q=80&w=700&auto=format&fit=crop",
    },
    {
      title: "Manual to Autonomous Threat Response",
      description:
        "Evolution from manual security operations to an autonomous threat detection and response system for a financial institution.",
      beforeImage: "https://images.unsplash.com/photo-**********-e076c223a692?q=80&w=700&auto=format&fit=crop",
      afterImage: "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=700&auto=format&fit=crop",
    },
  ]

  // Portfolio categories
  const categories = [
    "All Projects",
    "Agentic AI",
    "Cybersecurity",
    "Financial Services",
    "Healthcare",
    "Government",
    "Critical Infrastructure",
  ]

  return (
    <main className="pt-24">
      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop"
            alt="Portfolio header background"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <MoonPhaseIndicator className="mx-auto mb-6" size="lg" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Portfolio</h1>
            <p className="text-gray-300 text-lg">
              Explore our collection of advanced AI systems and security solutions implemented for organizations with
              the most demanding requirements.
            </p>
          </div>
        </div>
      </section>

      {/* Portfolio Categories */}
      <section className="py-8 bg-gray-950 border-b border-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category, index) => (
              <button
                key={index}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  index === 0
                    ? "bg-gradient-to-r from-red-600 to-red-700 text-white"
                    : "bg-gray-900 text-gray-300 hover:bg-gray-800"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1920&auto=format&fit=crop"
            alt="Featured projects background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Featured Projects
              </span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Our Signature Work</h2>
            <p className="text-gray-300">
              Discover how we've helped prestigious brands transform their digital presence with bespoke solutions.
            </p>
          </div>

          <div className="space-y-20">
            {featuredProjects.map((project, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center"
                style={{ direction: index % 2 === 1 ? "rtl" : "ltr" }}
              >
                <div style={{ direction: "ltr" }}>
                  <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-3 py-1 rounded-full mb-4 backdrop-blur-sm">
                    <span className="text-red-300 text-xs font-medium">{project.category}</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white">{project.title}</h3>
                  <p className="text-gray-300 mb-6">{project.description}</p>
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold mb-3 text-white">Results:</h4>
                    <ul className="space-y-2">
                      {project.results.map((result, i) => (
                        <li key={i} className="flex items-start">
                          <span className="bg-gradient-to-br from-red-600/80 to-red-800/80 rounded-full p-1 mr-3 mt-1 shadow-sm">
                            <svg className="h-2 w-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                              <circle cx="4" cy="4" r="3" />
                            </svg>
                          </span>
                          <span className="text-gray-300">{result}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Link
                    href={project.link}
                    className="inline-flex items-center text-red-400 font-medium hover:text-red-300 transition-colors"
                  >
                    View Case Study
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
                <LunarGlowCard glowColor="silver" intensity="medium">
                  <div className="relative h-80 md:h-96 rounded-lg overflow-hidden">
                    <Image
                      src={project.image || "/placeholder.svg"}
                      alt={project.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                </LunarGlowCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Before/After Comparisons */}
      <section className="py-20 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1920&auto=format&fit=crop"
            alt="Before/After background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Transformations
              </span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Before & After</h2>
            <p className="text-gray-300">
              Witness the dramatic transformations we've achieved for our clients' digital presence.
            </p>
          </div>

          <div className="space-y-16">
            {beforeAfterProjects.map((project, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <h3 className="text-2xl font-bold mb-4 text-white text-center">{project.title}</h3>
                <p className="text-gray-300 mb-8 text-center max-w-3xl mx-auto">{project.description}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="text-xl font-semibold mb-4 text-white text-center">Before</h4>
                    <LunarGlowCard glowColor="silver" intensity="low">
                      <div className="relative h-80 rounded-lg overflow-hidden">
                        <div className="absolute top-2 left-2 bg-black text-white text-xs px-2 py-1 rounded z-10">
                          Before
                        </div>
                        <Image
                          src={project.beforeImage || "/placeholder.svg"}
                          alt={`${project.title} Before`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </LunarGlowCard>
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold mb-4 text-white text-center">After</h4>
                    <LunarGlowCard glowColor="silver" intensity="low">
                      <div className="relative h-80 rounded-lg overflow-hidden">
                        <div className="absolute top-2 left-2 bg-gradient-to-r from-red-600 to-red-700 text-white text-xs px-2 py-1 rounded z-10">
                          After
                        </div>
                        <Image
                          src={project.afterImage || "/placeholder.svg"}
                          alt={`${project.title} After`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </LunarGlowCard>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Secure Your Organization?</h2>
            <p className="text-gray-300 text-lg mb-8">
              Let's discuss how Lunar Studio can help protect your digital assets and enhance your operations with our
              advanced AI and security solutions.
            </p>
            <Link
              href="/contact"
              className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors shadow-lg shadow-red-600/20"
            >
              Start Your Project
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>
    </main>
  )
}
