"use client"

import { useEffect, useRef, useState } from "react"

export default function LunarHeroBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [moonPhase, setMoonPhase] = useState(0) // 0 = full moon
  const [isLowPerfDevice, setIsLowPerfDevice] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  // Calculate current moon phase (simplified)
  useEffect(() => {
    // Check if device is likely low performance
    const checkPerformance = () => {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      const isOlderDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4
      setIsLowPerfDevice(isMobile || isOlderDevice)
    }

    checkPerformance()

    // Use Intersection Observer to only animate when visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting)
        })
      },
      { threshold: 0.1 },
    )

    if (canvasRef.current) {
      observer.observe(canvasRef.current)
    }

    // Delay animation start to prioritize content loading
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 500)

    // Get real moon phase data (simplified calculation)
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    const day = now.getDate()

    // Simple algorithm to approximate moon phase (0-29.5)
    // This is a simplified version - real calculations are more complex
    const phase = ((year - 2000) % 19) * 11 + month + day
    const normalizedPhase = phase % 29.5

    // Convert to 0-1 range where 0.5 is full moon
    const phaseNormalized = normalizedPhase / 29.5
    setMoonPhase(phaseNormalized)

    return () => {
      observer.disconnect()
      clearTimeout(timer)
    }
  }, [])

  useEffect(() => {
    // Only start animation when element is visible and page has loaded
    if (!isVisible || !isLoaded) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d", { alpha: true, desynchronized: true })
    if (!ctx) return

    // Set canvas dimensions - use much lower resolution
    const resizeCanvas = () => {
      const scale = isLowPerfDevice ? 0.25 : 0.5
      canvas.width = window.innerWidth * scale
      canvas.height = window.innerHeight * scale
      canvas.style.width = `${window.innerWidth}px`
      canvas.style.height = `${window.innerHeight}px`
    }

    resizeCanvas()

    // Debounce resize events
    let resizeTimer: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimer)
      resizeTimer = setTimeout(resizeCanvas, 200)
    }

    window.addEventListener("resize", handleResize)

    // Create a static background instead of animated one for low performance devices
    if (isLowPerfDevice) {
      // Create gradient background
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
      gradient.addColorStop(0, "#0a0a20")
      gradient.addColorStop(1, "#1a1a40")

      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Draw static stars
      const starCount = 50
      for (let i = 0; i < starCount; i++) {
        const x = Math.random() * canvas.width
        const y = Math.random() * canvas.height
        const size = Math.random() * 1.5 + 0.5
        const opacity = Math.random() * 0.8 + 0.2

        ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`
        ctx.beginPath()
        ctx.arc(x, y, size, 0, Math.PI * 2)
        ctx.fill()
      }

      // Draw moon
      const moonX = canvas.width * 0.8
      const moonY = canvas.height * 0.3
      const moonRadius = Math.min(canvas.width, canvas.height) * 0.1

      // Simple moon
      ctx.fillStyle = "rgba(255, 255, 255, 0.9)"
      ctx.beginPath()
      ctx.arc(moonX, moonY, moonRadius, 0, Math.PI * 2)
      ctx.fill()

      // Simple moon shadow based on phase
      if (moonPhase !== 0.5) {
        const shadowDirection = moonPhase < 0.5 ? -1 : 1
        const phaseOffset = Math.abs(moonPhase - 0.5) * 2
        const shadowX = moonX + moonRadius * 2 * phaseOffset * shadowDirection

        ctx.fillStyle = "rgba(10, 10, 30, 0.95)"
        ctx.beginPath()
        ctx.arc(moonX, moonY, moonRadius, 0, Math.PI * 2)
        ctx.arc(shadowX, moonY, moonRadius, 0, Math.PI * 2, shadowDirection < 0)
        ctx.fill("evenodd")
      }

      // Draw simple mountains
      ctx.fillStyle = "rgba(20, 20, 40, 0.8)"
      ctx.beginPath()
      ctx.moveTo(0, canvas.height)

      const mountainPoints = 5
      const mountainHeight = canvas.height * 0.2

      for (let i = 0; i <= mountainPoints; i++) {
        const x = (canvas.width / mountainPoints) * i
        const heightVariation = Math.sin(i * 0.5) * mountainHeight * 0.5
        const y = canvas.height - mountainHeight - heightVariation

        ctx.lineTo(x, y)
      }

      ctx.lineTo(canvas.width, canvas.height)
      ctx.closePath()
      ctx.fill()

      return () => {
        window.removeEventListener("resize", handleResize)
      }
    }

    // For higher performance devices, use a simplified but still animated version
    // Star class - simplified
    class Star {
      x: number
      y: number
      size: number
      opacity: number

      constructor() {
        this.x = Math.random() * canvas.width
        this.y = Math.random() * canvas.height
        this.size = Math.random() * 1.5 + 0.5
        this.opacity = Math.random() * 0.8 + 0.2
      }

      draw() {
        ctx.fillStyle = `rgba(255, 255, 255, ${this.opacity})`
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fill()
      }
    }

    // Create stars - significantly reduced count
    const stars: Star[] = []
    const starCount = 75

    for (let i = 0; i < starCount; i++) {
      stars.push(new Star())
    }

    // Draw moon - simplified
    const drawMoon = (x: number, y: number, radius: number, phase: number) => {
      // Full moon base
      ctx.save()

      // Simple white circle for moon
      ctx.fillStyle = "rgba(255, 255, 255, 0.9)"
      ctx.beginPath()
      ctx.arc(x, y, radius, 0, Math.PI * 2)
      ctx.fill()

      // Apply moon phase (shadow)
      if (phase !== 0.5) {
        ctx.beginPath()

        // Calculate shadow parameters based on phase
        const shadowDirection = phase < 0.5 ? -1 : 1
        const phaseOffset = Math.abs(phase - 0.5) * 2 // 0 to 1
        const shadowX = x + radius * 2 * phaseOffset * shadowDirection

        ctx.arc(x, y, radius, 0, Math.PI * 2)
        ctx.arc(shadowX, y, radius, 0, Math.PI * 2, shadowDirection < 0)

        ctx.fillStyle = "rgba(10, 10, 30, 0.95)"
        ctx.fill("evenodd")
      }

      ctx.restore()
    }

    // Animation loop with very low frequency updates
    let lastTime = 0
    const frameInterval = 500 // Very low fps (2fps)
    let animationFrame: number

    const animate = (timestamp: number) => {
      const deltaTime = timestamp - lastTime

      if (deltaTime > frameInterval) {
        lastTime = timestamp - (deltaTime % frameInterval)

        // Create gradient background
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
        gradient.addColorStop(0, "#0a0a20")
        gradient.addColorStop(1, "#1a1a40")

        ctx.fillStyle = gradient
        ctx.fillRect(0, 0, canvas.width, canvas.height)

        // Draw stars
        stars.forEach((star) => {
          star.draw()
        })

        // Draw moon
        const moonX = canvas.width * 0.8
        const moonY = canvas.height * 0.3
        const moonRadius = Math.min(canvas.width, canvas.height) * 0.1

        drawMoon(moonX, moonY, moonRadius, moonPhase)

        // Draw distant mountains
        ctx.fillStyle = "rgba(20, 20, 40, 0.8)"
        ctx.beginPath()
        ctx.moveTo(0, canvas.height)

        // Create jagged mountain silhouette - simplified
        const mountainPoints = 5
        const mountainHeight = canvas.height * 0.2

        for (let i = 0; i <= mountainPoints; i++) {
          const x = (canvas.width / mountainPoints) * i
          const heightVariation = Math.sin(i * 0.5) * mountainHeight * 0.5
          const y = canvas.height - mountainHeight - heightVariation

          ctx.lineTo(x, y)
        }

        ctx.lineTo(canvas.width, canvas.height)
        ctx.closePath()
        ctx.fill()
      }

      animationFrame = requestAnimationFrame(animate)
    }

    animationFrame = requestAnimationFrame(animate)

    return () => {
      window.removeEventListener("resize", handleResize)
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [moonPhase, isLowPerfDevice, isVisible, isLoaded])

  return (
    <div className="absolute inset-0 overflow-hidden">
      <canvas ref={canvasRef} className="absolute inset-0 bg-black" style={{ opacity: 0.9 }} />

      {/* Overlay gradient for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/70" />
    </div>
  )
}
