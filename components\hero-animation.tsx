"use client"

import { useEffect, useRef, useState } from "react"

export default function HeroAnimation() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isLowPerfDevice, setIsLowPerfDevice] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Check if device is likely low performance
    const checkPerformance = () => {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      const isOlderDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4
      setIsLowPerfDevice(isMobile || isOlderDevice)
    }

    checkPerformance()

    // Use Intersection Observer to only animate when visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting)
        })
      },
      { threshold: 0.1 },
    )

    if (canvasRef.current) {
      observer.observe(canvasRef.current)
    }

    // Delay animation start to prioritize content loading
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 500)

    return () => {
      observer.disconnect()
      clearTimeout(timer)
    }
  }, [])

  useEffect(() => {
    // Only start animation when element is visible and page has loaded
    if (!isVisible || !isLoaded) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d", { alpha: true, desynchronized: true })
    if (!ctx) return

    // Set canvas dimensions - use much lower resolution for low performance devices
    const resizeCanvas = () => {
      const scale = isLowPerfDevice ? 0.3 : 0.6
      canvas.width = window.innerWidth * scale
      canvas.height = window.innerHeight * scale
      canvas.style.width = `${window.innerWidth}px`
      canvas.style.height = `${window.innerHeight}px`
    }

    resizeCanvas()

    // Debounce resize events
    let resizeTimer: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimer)
      resizeTimer = setTimeout(resizeCanvas, 200)
    }

    window.addEventListener("resize", handleResize)

    // Extremely simplified particle system for low performance devices
    const particlesArray: { x: number; y: number; size: number }[] = []

    // Create a static grid of particles instead of dynamic ones
    const gridSpacing = isLowPerfDevice ? 100 : 70
    const cols = Math.floor(canvas.width / gridSpacing)
    const rows = Math.floor(canvas.height / gridSpacing)

    for (let i = 0; i < cols; i++) {
      for (let j = 0; j < rows; j++) {
        const x = i * gridSpacing + (Math.random() * 20 - 10)
        const y = j * gridSpacing + (Math.random() * 20 - 10)
        particlesArray.push({
          x,
          y,
          size: Math.random() * 1.5 + 0.5,
        })
      }
    }

    // Pre-calculate connections for static particles
    const connections: { a: number; b: number; opacity: number }[] = []
    const maxDistance = isLowPerfDevice ? 120 : 100

    for (let a = 0; a < particlesArray.length; a++) {
      for (let b = a + 1; b < particlesArray.length; b++) {
        const dx = particlesArray[a].x - particlesArray[b].x
        const dy = particlesArray[a].y - particlesArray[b].y
        const distSquared = dx * dx + dy * dy

        if (distSquared < maxDistance * maxDistance) {
          const distance = Math.sqrt(distSquared)
          const opacity = 1 - distance / maxDistance
          connections.push({ a, b, opacity })
        }
      }
    }

    // Draw function - simplified to just draw static particles and connections
    function draw() {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw particles
      for (let i = 0; i < particlesArray.length; i++) {
        const p = particlesArray[i]
        ctx.fillStyle = "rgba(255, 255, 255, 0.5)"
        ctx.beginPath()
        ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2)
        ctx.fill()
      }

      // Draw connections
      for (const conn of connections) {
        ctx.strokeStyle = `rgba(234,179,8, ${conn.opacity * 0.2})`
        ctx.lineWidth = 1
        ctx.beginPath()
        ctx.moveTo(particlesArray[conn.a].x, particlesArray[conn.a].y)
        ctx.lineTo(particlesArray[conn.b].x, particlesArray[conn.b].y)
        ctx.stroke()
      }
    }

    // Draw once for static effect
    draw()

    // Only animate on higher-end devices
    let animationFrame: number

    if (!isLowPerfDevice) {
      // Mouse interaction
      let mouseX: number | undefined
      let mouseY: number | undefined

      canvas.addEventListener("mousemove", (e) => {
        const rect = canvas.getBoundingClientRect()
        const scaleX = canvas.width / rect.width
        const scaleY = canvas.height / rect.height
        mouseX = (e.clientX - rect.left) * scaleX
        mouseY = (e.clientY - rect.top) * scaleY
      })

      canvas.addEventListener("mouseout", () => {
        mouseX = undefined
        mouseY = undefined
      })

      // Simple animation loop with very low frequency updates
      let lastTime = 0
      const frameInterval = 100 // Very low fps (10fps)

      function animate(timestamp: number) {
        const deltaTime = timestamp - lastTime

        if (deltaTime > frameInterval) {
          lastTime = timestamp - (deltaTime % frameInterval)

          ctx.clearRect(0, 0, canvas.width, canvas.height)

          // Draw particles
          for (let i = 0; i < particlesArray.length; i++) {
            const p = particlesArray[i]

            // Very subtle movement
            if (mouseX !== undefined && mouseY !== undefined) {
              const dx = mouseX - p.x
              const dy = mouseY - p.y
              const distance = Math.sqrt(dx * dx + dy * dy)

              if (distance < 100) {
                p.x -= dx * 0.02
                p.y -= dy * 0.02
              } else {
                // Slowly return to original position
                p.x += ((i % gridSpacing) - p.x) * 0.01
                p.y += ((Math.floor(i / gridSpacing) % gridSpacing) - p.y) * 0.01
              }
            }

            ctx.fillStyle = "rgba(255, 255, 255, 0.5)"
            ctx.beginPath()
            ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2)
            ctx.fill()
          }

          // Draw connections
          for (const conn of connections) {
            ctx.strokeStyle = `rgba(234,179,8, ${conn.opacity * 0.2})`
            ctx.lineWidth = 1
            ctx.beginPath()
            ctx.moveTo(particlesArray[conn.a].x, particlesArray[conn.a].y)
            ctx.lineTo(particlesArray[conn.b].x, particlesArray[conn.b].y)
            ctx.stroke()
          }
        }

        animationFrame = requestAnimationFrame(animate)
      }

      animationFrame = requestAnimationFrame(animate)
    }

    return () => {
      window.removeEventListener("resize", handleResize)
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [isVisible, isLoaded, isLowPerfDevice])

  return <canvas ref={canvasRef} className="absolute inset-0 bg-black" style={{ opacity: 0.8 }} />
}
