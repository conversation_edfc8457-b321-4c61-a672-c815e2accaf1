"use client"

import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Briefcase, FileText, Phone, Menu, Info, Settings, Building, Mail } from "lucide-react"

export default function MobileNavigation() {
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)

  const isActive = (path: string) => {
    if (path === "/" && pathname === "/") return true
    if (path !== "/" && pathname.startsWith(path)) return true
    return false
  }

  const navigationItems = [
    { href: "/", label: "Home", icon: <Home className="h-4 w-4" /> },
    { href: "/about", label: "About", icon: <Info className="h-4 w-4" /> },
    { href: "/services", label: "Services", icon: <Settings className="h-4 w-4" /> },
    { href: "/industries", label: "Industries", icon: <Building className="h-4 w-4" /> },
    { href: "/portfolio", label: "Portfolio", icon: <Briefcase className="h-4 w-4" /> },
    { href: "/blog", label: "Blog", icon: <FileText className="h-4 w-4" /> },
    { href: "/contact", label: "Contact", icon: <Mail className="h-4 w-4" /> },
  ]

  return (
    <>
      {/* Mobile Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 z-50 w-full h-16 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 md:hidden">
        <div className="grid h-full grid-cols-5 mx-auto">
          <NavItem href="/" icon={<Home className="w-6 h-6" />} label="Home" isActive={isActive("/")} />
          <NavItem
            href="/services"
            icon={<Briefcase className="w-6 h-6" />}
            label="Services"
            isActive={isActive("/services")}
          />
          <NavItem
            href="/portfolio"
            icon={<FileText className="w-6 h-6" />}
            label="Portfolio"
            isActive={isActive("/portfolio")}
          />
          <NavItem
            href="/contact"
            icon={<Phone className="w-6 h-6" />}
            label="Contact"
            isActive={isActive("/contact")}
          />
          <button
            onClick={() => setIsMenuOpen(true)}
            className="inline-flex flex-col items-center justify-center px-5 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <Menu className="w-6 h-6 text-gray-500 dark:text-gray-400" />
            <span className="text-xs text-gray-500 dark:text-gray-400">More</span>
          </button>
        </div>
      </nav>

      {/* Full Screen Menu */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-50 bg-white dark:bg-gray-900 p-6 flex flex-col md:hidden">
          <div className="flex justify-end">
            <button
              onClick={() => setIsMenuOpen(false)}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="flex-1 overflow-y-auto py-8">
            <div className="grid gap-6">
              {/* Contact Number */}
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Contact Us</p>
                    <a
                      href="tel:+923194821372"
                      className="text-lg font-bold text-yellow-600 dark:text-yellow-400"
                    >
                      +92 ************
                    </a>
                  </div>
                </div>
              </div>

              <MenuSection title="Company">
                <MenuItem href="/about" label="About Us" />
                <MenuItem href="/leadership" label="Leadership" />
                <MenuItem href="/careers" label="Careers" />
              </MenuSection>

              <MenuSection title="Services">
                <MenuItem href="/services/ai-solutions" label="AI Solutions" />
                <MenuItem href="/services/cybersecurity" label="Cybersecurity" />
                <MenuItem href="/services/web-app-development" label="Web Development" />
                <MenuItem href="/services/digital-transformation" label="Digital Transformation" />
                <MenuItem href="/services/mvp-development" label="MVP Development" />
                <MenuItem href="/services/saas-development" label="SaaS Development" />
              </MenuSection>

              <MenuSection title="Resources">
                <MenuItem href="/blog" label="Blog" />
                <MenuItem href="/portfolio" label="Portfolio" />
                <MenuItem href="/industries" label="Industries" />
              </MenuSection>

              <MenuSection title="Legal">
                <MenuItem href="/privacy-policy" label="Privacy Policy" />
                <MenuItem href="/terms-of-service" label="Terms of Service" />
                <MenuItem href="/cookie-policy" label="Cookie Policy" />
                <MenuItem href="/accessibility" label="Accessibility" />
              </MenuSection>

              <nav className="flex flex-col space-y-2">
                {navigationItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                      isActive(item.href)
                        ? "bg-yellow-600/20 text-yellow-500 border border-yellow-500/30"
                        : "text-gray-300 hover:bg-gray-800/50 hover:text-white"
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.icon}
                    <span className="font-medium">{item.label}</span>
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

function NavItem({
  href,
  icon,
  label,
  isActive,
}: { href: string; icon: React.ReactNode; label: string; isActive: boolean }) {
  return (
    <Link
      href={href}
      className={`inline-flex flex-col items-center justify-center px-5 ${
        isActive
          ? "text-yellow-500 dark:text-yellow-400"
          : "text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800"
      }`}
    >
      {icon}
      <span className="text-xs">{label}</span>
      {isActive && (
        <span className="absolute bottom-0 left-1/2 w-1.5 h-1.5 bg-yellow-500 dark:bg-yellow-400 rounded-full transform -translate-x-1/2"></span>
      )}
    </Link>
  )
}

function MenuSection({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <div>
      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">{title}</h3>
      <div className="space-y-2">{children}</div>
    </div>
  )
}

function MenuItem({ href, label }: { href: string; label: string }) {
  const pathname = usePathname()
  const isActive = pathname === href || pathname.startsWith(`${href}/`)

  return (
    <Link
      href={href}
      className={`block px-3 py-2 rounded-lg ${
        isActive
          ? "bg-yellow-50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400"
          : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
      }`}
    >
      {label}
    </Link>
  )
}
