"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON>, Check, Brain, Bot, Network, Cpu, Database, Code } from "lucide-react"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"

export default function AISolutionsPage() {
  return (
    <main className="pt-24 relative">
      <AnimatedBackground particleCount={50} className="opacity-30" />

      {/* Hero Section */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">Enterprise AI Solutions</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Transform Your Business with Advanced AI"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg mb-8">
              Leverage the power of artificial intelligence to drive innovation, enhance decision-making, and automate
              complex processes across your organization.
            </p>
            <div className="mt-8 relative h-64 w-full md:h-80 md:w-3/4 mx-auto rounded-lg overflow-hidden">
              <Image
                src="https://images.unsplash.com/photo-1677442135136-760c813a7a9c?q=80&w=2832&auto=format&fit=crop"
                alt="AI Solutions"
                fill
                className="object-cover rounded-lg border border-yellow-800/30"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
              <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm px-3 py-1 rounded text-xs text-gray-300">
                Advanced AI systems transforming business operations
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/contact"
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center transition-colors group"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Link>
              <Link
                href="#ai-solutions"
                className="border border-gray-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center hover:bg-gray-800 transition-colors"
              >
                Explore AI Solutions
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* AI Solutions Overview */}
      <section id="ai-solutions" className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Our AI Solutions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We offer a comprehensive suite of AI solutions designed to address specific business challenges and drive
              measurable outcomes.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {[
              {
                title: "Agentic AI Systems",
                image: "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=2940&auto=format&fit=crop",
                description: "Autonomous AI agents that can perceive, decide, and act independently",
              },
              {
                title: "RAG-Based Solutions",
                image: "https://images.unsplash.com/photo-1655720828018-edd2daec9349?q=80&w=2832&auto=format&fit=crop",
                description: "Retrieval-Augmented Generation systems with proprietary data integration",
              },
              {
                title: "Predictive Analytics",
                image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop",
                description: "Advanced analytics leveraging machine learning for actionable insights",
              },
            ].map((item, index) => (
              <motion.div
                key={index}
                className="relative overflow-hidden rounded-lg group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image
                    src={item.image || "/placeholder.svg"}
                    alt={item.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <h3 className="text-lg font-bold text-white mb-1">{item.title}</h3>
                  <p className="text-gray-300 text-sm">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Agentic AI Systems",
                description:
                  "Autonomous AI agents that can perceive, decide, and act independently to accomplish complex tasks with minimal human supervision.",
                icon: <Bot className="h-8 w-8" />,
                features: [
                  "Goal-oriented autonomous systems",
                  "Multi-agent collaboration frameworks",
                  "Adaptive learning capabilities",
                  "Human-in-the-loop oversight",
                ],
              },
              {
                title: "RAG-Based AI Solutions",
                description:
                  "Retrieval-Augmented Generation systems that combine the power of large language models with your proprietary data for accurate, context-aware responses.",
                icon: <Network className="h-8 w-8" />,
                features: [
                  "Knowledge base integration",
                  "Context-aware responses",
                  "Factual accuracy verification",
                  "Domain-specific customization",
                ],
              },
              {
                title: "Predictive Analytics",
                description:
                  "Advanced analytics solutions that leverage machine learning to forecast trends, identify patterns, and provide actionable insights.",
                icon: <Brain className="h-8 w-8" />,
                features: [
                  "Demand forecasting",
                  "Customer behavior prediction",
                  "Risk assessment and mitigation",
                  "Operational optimization",
                ],
              },
              {
                title: "Computer Vision",
                description:
                  "AI-powered image and video analysis solutions for object detection, classification, and visual inspection across various industries.",
                icon: <Cpu className="h-8 w-8" />,
                features: [
                  "Object detection and recognition",
                  "Quality control automation",
                  "Visual inspection systems",
                  "Video analytics and surveillance",
                ],
              },
              {
                title: "Natural Language Processing",
                description:
                  "Text and speech analysis solutions that enable machines to understand, interpret, and generate human language for various applications.",
                icon: <Database className="h-8 w-8" />,
                features: [
                  "Sentiment analysis",
                  "Document classification",
                  "Chatbots and virtual assistants",
                  "Automated content generation",
                ],
              },
              {
                title: "AI Workflow Automation",
                description:
                  "Intelligent systems that automate complex business processes, enhancing efficiency and reducing operational costs.",
                icon: <Code className="h-8 w-8" />,
                features: [
                  "Process analysis and optimization",
                  "Custom workflow design",
                  "Integration with existing systems",
                  "Continuous improvement mechanisms",
                ],
              },
            ].map((solution, index) => (
              <motion.div
                key={solution.title}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="p-6">
                  <div className="w-16 h-16 bg-yellow-900/30 rounded-lg flex items-center justify-center mb-6 text-yellow-400">
                    {solution.icon}
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-white">{solution.title}</h3>
                  <p className="text-gray-300 mb-6">{solution.description}</p>

                  <div className="bg-gray-800 p-4 rounded-lg mb-6">
                    <h4 className="text-white font-semibold mb-2">Key Features:</h4>
                    <ul className="space-y-2">
                      {solution.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start">
                          <Check className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link
                    href={`/contact?solution=${encodeURIComponent(solution.title)}`}
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    Learn more
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">Success Stories</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">AI Implementation Case Studies</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover how our AI solutions have helped organizations across various industries achieve their business
              objectives.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "AI-Powered Fraud Detection for Global Bank",
                category: "Banking & Finance",
                image: "https://images.unsplash.com/photo-**********-824ae1b704d3?q=80&w=2940&auto=format&fit=crop",
                results: "Reduced fraud by 87% and saved $4.5M annually",
              },
              {
                title: "AI Diagnostic Platform for Healthcare Network",
                category: "Healthcare",
                image: "https://images.unsplash.com/photo-*************-112ba8d25d1d?q=80&w=2940&auto=format&fit=crop",
                results: "Improved diagnostic accuracy by 35% and reduced patient wait times by 50%",
              },
              {
                title: "Predictive Maintenance for Manufacturing",
                category: "Manufacturing",
                image: "https://images.unsplash.com/photo-*************-8f6e74349ca1?q=80&w=2940&auto=format&fit=crop",
                results: "Reduced equipment downtime by 45% and maintenance costs by 30%",
              },
            ].map((study, index) => (
              <motion.div
                key={study.title}
                className="bg-gray-900 rounded-lg overflow-hidden border border-gray-800 hover:border-yellow-500 transition-colors group"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-48">
                  <Image src={study.image || "/placeholder.svg"} alt={study.title} fill className="object-cover" />
                  <div className="absolute top-0 left-0 bg-yellow-600 text-white text-xs px-3 py-1">
                    {study.category}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-white">{study.title}</h3>
                  <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3 mb-4">
                    <div className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-400 font-medium">{study.results}</span>
                    </div>
                  </div>
                  <Link
                    href={`/case-studies/${study.title
                      .toLowerCase()
                      .replace(/\s+/g, "-")
                      .replace(/[^a-z0-9-]/g, "")}`}
                    className="text-yellow-400 inline-flex items-center font-medium hover:text-yellow-300 transition-colors"
                  >
                    Read case study
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/case-studies"
              className="bg-transparent border border-yellow-500 text-yellow-400 hover:bg-yellow-900/20 px-8 py-3 rounded-md font-medium inline-flex items-center transition-colors"
            >
              View All Case Studies
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20 bg-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-6 text-white">Our AI Implementation Process</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              We follow a structured approach to implementing AI solutions tailored to your specific business needs.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "1",
                title: "Discovery & Assessment",
                description:
                  "We analyze your current processes, identify opportunities for AI implementation, and define clear objectives.",
              },
              {
                step: "2",
                title: "Solution Design",
                description:
                  "We design a custom AI solution tailored to your specific requirements and business goals.",
              },
              {
                step: "3",
                title: "Development & Integration",
                description:
                  "Our team builds and integrates the AI solution with your existing systems and data sources.",
              },
              {
                step: "4",
                title: "Deployment & Optimization",
                description:
                  "We deploy the solution, provide training, and continuously optimize performance based on feedback and results.",
              },
            ].map((process, index) => (
              <motion.div
                key={process.step}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="bg-yellow-900/30 w-12 h-12 rounded-full flex items-center justify-center mb-6 text-white font-bold text-xl">
                  {process.step}
                </div>
                <h3 className="text-xl font-bold mb-3 text-white">{process.title}</h3>
                <p className="text-gray-300">{process.description}</p>
              </motion.div>
            ))}
          </div>

          <div className="mt-12 relative h-64 md:h-80 w-full md:w-3/4 mx-auto rounded-lg overflow-hidden">
            <Image
              src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?q=80&w=2940&auto=format&fit=crop"
              alt="AI Implementation Process"
              fill
              className="object-cover rounded-lg border border-gray-800"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
            <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm px-3 py-1 rounded text-xs text-gray-300">
              Our team implementing AI solutions for enterprise clients
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="mb-6 inline-block bg-yellow-900/30 px-4 py-1 rounded-full">
              <span className="text-yellow-400 text-sm font-medium">FAQ</span>
            </div>
            <h2 className="text-3xl font-bold mb-6 text-white">Frequently Asked Questions</h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Get answers to common questions about our AI solutions and implementation process.
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto">
            {[
              {
                question: "How can AI benefit my business?",
                answer:
                  "AI can benefit your business in multiple ways, including automating repetitive tasks, enhancing decision-making with data-driven insights, improving customer experiences through personalization, optimizing operations, and identifying new business opportunities through pattern recognition and predictive analytics.",
              },
              {
                question: "Do I need large amounts of data to implement AI solutions?",
                answer:
                  "While having quality data is important for AI implementation, you don't necessarily need massive datasets to get started. Our approach includes data assessment and, if needed, strategies for data collection and enrichment. We can also leverage transfer learning and pre-trained models to reduce data requirements for certain applications.",
              },
              {
                question: "How long does it take to implement an AI solution?",
                answer:
                  "Implementation timelines vary based on the complexity of the solution, the state of your existing systems and data, and the scope of the project. Simple AI implementations can be completed in a few weeks, while more complex enterprise-wide solutions may take several months. We provide detailed timelines during the discovery and planning phase.",
              },
              {
                question: "How do you ensure the security and privacy of our data?",
                answer:
                  "We implement robust security measures throughout the AI development lifecycle, including data encryption, secure access controls, and compliance with relevant regulations like GDPR and CCPA. We also provide transparency in how data is used, processed, and stored, and can implement on-premises solutions when required for sensitive data.",
              },
              {
                question: "How do you measure the success of AI implementations?",
                answer:
                  "We establish clear, measurable KPIs at the beginning of each project, aligned with your business objectives. These might include metrics like cost reduction, revenue increase, productivity improvements, error rate reduction, or customer satisfaction scores. We provide regular reporting on these metrics and continuously optimize the solution to improve results.",
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                className="bg-gray-900 p-6 rounded-lg border border-gray-800 mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="text-xl font-bold mb-3 text-white">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-b from-gray-950 to-black">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-yellow-900/30 to-yellow-800/30 rounded-lg p-8 md:p-12 border border-yellow-800/50">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Transform Your Business with AI?
                </h2>
                <p className="text-gray-300 text-lg">
                  Contact us today to discuss how our AI solutions can help you achieve your business objectives.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg whitespace-nowrap"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
