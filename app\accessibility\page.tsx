"use client"

import { motion } from "framer-motion"
import AnimatedText from "@/components/animated-text"
import AnimatedBackground from "@/components/animated-background"
import Link from "next/link"

export default function AccessibilityPage() {
  return (
    <main className="pt-24 relative">
      <AnimatedBackground particleCount={30} className="opacity-20" />

      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Accessibility Statement"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">Last updated: March 14, 2025</p>
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-950">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto bg-gray-900/70 p-8 rounded-xl border border-gray-800 shadow-xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="prose prose-lg prose-invert max-w-none"
            >
              <h2>Our Commitment to Accessibility</h2>
              <p>
                .Lunar is committed to ensuring digital accessibility for people with disabilities. We are continually
                improving the user experience for everyone and applying the relevant accessibility standards.
              </p>

              <h2>Conformance Status</h2>
              <p>
                The Web Content Accessibility Guidelines (WCAG) define requirements for designers and developers to
                improve accessibility for people with disabilities. It defines three levels of conformance: Level A,
                Level AA, and Level AAA.
              </p>
              <p>
                .Lunar's website is partially conformant with WCAG 2.1 level AA. Partially conformant means that some
                parts of the content do not fully conform to the accessibility standard.
              </p>

              <h2>Accessibility Features</h2>
              <p>Our website includes the following accessibility features:</p>
              <ul>
                <li>Semantic HTML structure for better screen reader navigation</li>
                <li>Keyboard navigation support for all interactive elements</li>
                <li>Sufficient color contrast for text and important graphics</li>
                <li>Text alternatives for non-text content</li>
                <li>Resizable text without loss of functionality</li>
                <li>Clear and consistent navigation</li>
                <li>Form labels and error messages</li>
              </ul>

              <h2>Limitations and Alternatives</h2>
              <p>
                Despite our best efforts to ensure accessibility of .Lunar's website, there may be some limitations.
                Below is a description of known limitations, and potential solutions. Please contact us if you observe
                an issue not listed below.
              </p>
              <p>
                <strong>Known limitations:</strong>
              </p>
              <ul>
                <li>
                  Some older PDF documents may not be fully accessible. We are working to remediate these documents or
                  provide alternative formats upon request.
                </li>
                <li>
                  Some third-party content that appears on our website may not be fully accessible. We are working with
                  our partners to improve the accessibility of these components.
                </li>
                <li>
                  Some interactive features may not be fully accessible via keyboard navigation. We are actively working
                  to improve these features.
                </li>
              </ul>

              <h2>Feedback</h2>
              <p>
                We welcome your feedback on the accessibility of .Lunar's website. Please let us know if you encounter
                accessibility barriers:
              </p>
              <ul>
                <li>Phone: +****************</li>
                <li>E-mail: <EMAIL></li>
                <li>Postal address: 1234 Tech Avenue, San Francisco, CA 94107</li>
              </ul>
              <p>We try to respond to feedback within 3 business days.</p>

              <h2>Assessment Approach</h2>
              <p>.Lunar assessed the accessibility of our website by the following approaches:</p>
              <ul>
                <li>Self-evaluation</li>
                <li>External evaluation by accessibility experts</li>
                <li>User testing with assistive technologies</li>
              </ul>

              <h2>Formal Approval</h2>
              <p>
                This accessibility statement was created on March 14, 2025 using the{" "}
                <a href="https://www.w3.org/WAI/planning/statements/" className="text-red-400 hover:text-red-300">
                  W3C Accessibility Statement Generator Tool
                </a>
                .
              </p>

              <h2>Contact Us</h2>
              <p>If you have any questions or concerns about our accessibility statement, please contact us:</p>
              <p>
                .Lunar
                <br />
                1234 Tech Avenue
                <br />
                San Francisco, CA 94107
                <br />
                <EMAIL>
                <br />
                +****************
              </p>

              <div className="mt-8">
                <Link href="/contact" className="text-red-400 hover:text-red-300 font-medium">
                  Contact Us for Accessibility Support
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </main>
  )
}
