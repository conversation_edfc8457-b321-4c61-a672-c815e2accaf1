"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
  <PERSON>Right,
  MessageSquare,
  Bot,
  Shield,
  Check,
  Sparkles,
  Star,
  Zap,
  Users,
  Globe,
  Code,
  Lock,
  Server,
  Layers,
} from "lucide-react"
import AnimatedText from "@/components/animated-text"
import ThreeDCard from "@/components/3d-card"
import LunarGlowCard from "@/components/lunar-glow-card"

export default function EnterpriseChatbotsPage() {
  return (
    <main className="pt-24 relative">
      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1531746790731-6c087fecd65a?q=80&w=1920&auto=format&fit=crop"
            alt="Enterprise Chatbots header background"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <MessageSquare className="h-4 w-4 mr-2" />
                Conversational AI
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Enterprise Chatbots"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              Sophisticated conversational AI solutions that enhance customer experience and streamline internal
              operations.
            </p>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=1920&auto=format&fit=crop"
            alt="Enterprise Chatbots overview background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
                <span className="text-red-300 text-sm font-medium flex items-center">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Intelligent Conversations
                </span>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
                Transform Customer and Employee Experiences
              </h2>
              <p className="text-gray-300 text-lg mb-6">
                Our enterprise chatbots deliver sophisticated, natural conversations that enhance engagement, streamline
                operations, and provide valuable insights across your organization.
              </p>
              <p className="text-gray-300 mb-8">
                Each solution is meticulously crafted to reflect your brand's voice and values while providing the
                intelligence and security required for enterprise applications.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Omnichannel Deployment</span>
                  </div>
                  <p className="text-gray-300 text-sm">
                    Consistent experiences across web, mobile, and messaging platforms
                  </p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Natural Language Understanding</span>
                  </div>
                  <p className="text-gray-300 text-sm">Advanced AI that comprehends complex queries and context</p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Seamless Human Handoff</span>
                  </div>
                  <p className="text-gray-300 text-sm">Intelligent escalation to human agents when needed</p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Enterprise Security</span>
                  </div>
                  <p className="text-gray-300 text-sm">Robust protection for sensitive conversations and data</p>
                </div>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              <ThreeDCard className="p-6">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800">
                  <div className="relative h-[400px] rounded-lg overflow-hidden">
                    <Image
                      src="https://images.unsplash.com/photo-1596524430615-b46475ddff6e?q=80&w=800&auto=format&fit=crop"
                      alt="Chatbot Interface"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6">
                      <div className="bg-gray-900/80 backdrop-blur-md p-4 rounded-lg border border-gray-700">
                        <div className="flex items-center mb-3">
                          <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center mr-3">
                            <MessageSquare className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="text-white font-bold">LunarAssist</h3>
                            <p className="text-gray-300 text-xs">Enterprise Support • Available 24/7</p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="bg-gray-800/50 p-2 rounded-lg text-gray-300 text-sm">
                            Hello! How can I assist you with our enterprise solutions today?
                          </div>
                          <div className="bg-blue-600/20 p-2 rounded-lg text-gray-200 text-sm">
                            I need to upgrade our security package for our international offices.
                          </div>
                          <div className="bg-gray-800/50 p-2 rounded-lg text-gray-300 text-sm">
                            I'd be happy to help with upgrading your security package for international offices. Our
                            Enterprise Shield Pro would be ideal for your needs. It includes advanced threat protection,
                            compliance controls for multiple jurisdictions, and 24/7 global support. Would you like me
                            to connect you with a security specialist to discuss specific requirements?
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ThreeDCard>
              <div className="absolute -bottom-10 -right-10 z-10">
                <div className="bg-gray-900/80 backdrop-blur-lg p-4 rounded-lg border border-gray-800 shadow-xl">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-green-600 flex items-center justify-center mr-2">
                      <Users className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-white text-sm">95% Resolution Rate</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Chatbot Types Section */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1563986768609-322da13575f3?q=80&w=1920&auto=format&fit=crop"
            alt="Chatbot Types background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                Chatbot Solutions
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Tailored Chatbot Offerings
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our enterprise chatbots are designed to address specific business needs, from customer service to internal
              support and beyond.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Customer Service Chatbot Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <ThreeDCard className="p-6 h-full">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full border border-gray-800">
                  <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-blue-600/80 to-blue-800/80 shadow-lg">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white">Customer Service Chatbots</h3>
                  <p className="text-gray-300 mb-6">
                    Intelligent virtual assistants that provide exceptional customer support across all digital
                    channels.
                  </p>

                  <h4 className="text-lg font-semibold text-white mb-4">Key Features:</h4>
                  <div className="space-y-4 mb-6">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Globe className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Multilingual Support</h5>
                        <p className="text-gray-300 text-sm">Natural conversations in multiple languages</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Zap className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Instant Resolution</h5>
                        <p className="text-gray-300 text-sm">Immediate answers to common questions and issues</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Users className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Intelligent Escalation</h5>
                        <p className="text-gray-300 text-sm">Seamless handoff to human agents when needed</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-blue-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Layers className="h-4 w-4 text-blue-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Omnichannel Integration</h5>
                        <p className="text-gray-300 text-sm">Consistent experience across web, mobile, and messaging</p>
                      </div>
                    </div>
                  </div>

                  <Link
                    href="/contact"
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors shadow-lg shadow-blue-600/20"
                  >
                    Discuss Customer Service Solutions
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </ThreeDCard>
            </motion.div>

            {/* Internal Support Chatbot Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <ThreeDCard className="p-6 h-full">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full border border-gray-800">
                  <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-green-600/80 to-green-800/80 shadow-lg">
                    <Server className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white">Internal Support Chatbots</h3>
                  <p className="text-gray-300 mb-6">
                    AI-powered assistants that enhance employee productivity and streamline internal operations.
                  </p>

                  <h4 className="text-lg font-semibold text-white mb-4">Key Features:</h4>
                  <div className="space-y-4 mb-6">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-green-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Code className="h-4 w-4 text-green-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">System Integration</h5>
                        <p className="text-gray-300 text-sm">Seamless connection with internal tools and platforms</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-green-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Bot className="h-4 w-4 text-green-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Knowledge Base Access</h5>
                        <p className="text-gray-300 text-sm">Instant retrieval of company information and procedures</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-green-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Lock className="h-4 w-4 text-green-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Role-Based Access</h5>
                        <p className="text-gray-300 text-sm">Secure information delivery based on user permissions</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-green-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Shield className="h-4 w-4 text-green-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Enterprise Security</h5>
                        <p className="text-gray-300 text-sm">Robust protection for sensitive internal communications</p>
                      </div>
                    </div>
                  </div>

                  <Link
                    href="/contact"
                    className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors shadow-lg shadow-green-600/20"
                  >
                    Discuss Internal Support Solutions
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </ThreeDCard>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=1920&auto=format&fit=crop"
            alt="Use Cases background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Applications
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Strategic Use Cases
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover how our enterprise chatbots can transform your operations and enhance customer experiences.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "24/7 Customer Support",
                description:
                  "Provide instant, consistent support to customers around the clock, reducing wait times and improving satisfaction.",
                icon: <Users className="h-6 w-6 text-red-500" />,
                metrics: "85% reduction in response time",
              },
              {
                title: "IT Help Desk",
                description:
                  "Streamline internal IT support with automated troubleshooting and resolution for common technical issues.",
                icon: <Server className="h-6 w-6 text-red-500" />,
                metrics: "70% of tickets resolved automatically",
              },
              {
                title: "Sales Assistance",
                description:
                  "Guide prospects through the sales process, answer product questions, and qualify leads before human engagement.",
                icon: <Zap className="h-6 w-6 text-red-500" />,
                metrics: "32% increase in qualified leads",
              },
              {
                title: "HR & Employee Onboarding",
                description:
                  "Simplify HR processes and provide new employees with instant access to information and resources.",
                icon: <Users className="h-6 w-6 text-red-500" />,
                metrics: "45% faster onboarding process",
              },
              {
                title: "Secure Transactions",
                description:
                  "Enable secure, conversational interfaces for financial transactions and sensitive information exchange.",
                icon: <Lock className="h-6 w-6 text-red-500" />,
                metrics: "100% compliance with security standards",
              },
              {
                title: "Knowledge Management",
                description: "Provide employees with instant access to company knowledge, policies, and procedures.",
                icon: <Layers className="h-6 w-6 text-red-500" />,
                metrics: "78% reduction in information search time",
              },
            ].map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <LunarGlowCard glowColor="blue" intensity="low">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800 h-full">
                    <div className="w-12 h-12 rounded-xl mb-6 flex items-center justify-center bg-gradient-to-br from-blue-600/30 to-blue-800/30">
                      {useCase.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-white">{useCase.title}</h3>
                    <p className="text-gray-300 mb-4">{useCase.description}</p>
                    <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3">
                      <div className="flex items-center">
                        <Check className="h-5 w-5 text-green-500 mr-2" />
                        <span className="text-green-400 font-medium">{useCase.metrics}</span>
                      </div>
                    </div>
                  </div>
                </LunarGlowCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-gradient-to-r from-blue-900/30 to-blue-800/30 rounded-lg p-8 md:p-12 border border-blue-800/50 backdrop-blur-sm">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Transform Your Digital Interactions?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how our enterprise chatbots can elevate your customer experience and streamline your
                  operations.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg shadow-lg shadow-blue-600/20"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
