"use client"

import { useState, useRef } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { Zap, Users, Settings, Clock, Shield, Globe, Award, Layers, Lightbulb, type LucideIcon } from "lucide-react"

interface ParallaxFeatureCardProps {
  title: string
  description: string
  icon: string
  stats?: { value: string; label: string }[]
  index?: number
}

export default function ParallaxFeatureCard({ title, description, icon, stats, index = 0 }: ParallaxFeatureCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  // For scroll-based parallax
  const { scrollYProgress } = useScroll({
    target: cardRef,
    offset: ["start end", "end start"],
  })

  // Parallax effect on scroll
  const y = useTransform(scrollYProgress, [0, 1], [50, -50])
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.6, 1, 1, 0.6])

  // Get icon component
  const getIcon = (): LucideIcon => {
    switch (icon) {
      case "Zap":
        return Zap
      case "Users":
        return Users
      case "Settings":
        return Settings
      case "Clock":
        return Clock
      case "Shield":
        return Shield
      case "Globe":
        return Globe
      case "Award":
        return Award
      case "Layers":
        return Layers
      case "Lightbulb":
        return Lightbulb
      default:
        return Zap
    }
  }

  const Icon = getIcon()

  return (
    <motion.div
      ref={cardRef}
      className="relative"
      style={{ y, opacity }}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-100px" }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="relative bg-gradient-to-br from-gray-800 to-gray-900 dark:from-gray-900 dark:to-black p-8 rounded-2xl overflow-hidden">
        {/* Animated background accent */}
        <div
          className="absolute -inset-0.5 bg-gradient-to-r from-gray-500 to-white rounded-2xl blur opacity-20 transition-opacity duration-300 -z-10"
          style={{ opacity: isHovered ? 0.4 : 0.2 }}
        />

        {/* Animated lines in background */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute h-px bg-gradient-to-r from-transparent via-white/20 to-transparent w-full"
              style={{
                top: `${30 + i * 30}%`,
                x: `-100%`,
                opacity: 0.15,
              }}
              animate={{
                x: isHovered ? "200%" : "-100%",
                transition: {
                  duration: isHovered ? 1.5 + i * 0.5 : 0,
                  ease: "easeInOut",
                  repeat: isHovered ? Number.POSITIVE_INFINITY : 0,
                  repeatType: "loop",
                },
              }}
            />
          ))}
        </div>

        <div className="relative z-10">
          <motion.div
            className="text-white mb-4 relative inline-flex"
            animate={{
              scale: isHovered ? 1.1 : 1,
              rotate: isHovered ? 5 : 0,
            }}
            transition={{ type: "spring", stiffness: 300, damping: 15 }}
          >
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-white to-gray-300 flex items-center justify-center">
              <Icon className="h-6 w-6 text-black" />
            </div>

            {/* Glowing effect */}
            <div
              className="absolute -inset-0.5 bg-gradient-to-r from-indigo-500 to-cyan-500 rounded-xl blur opacity-60 -z-10"
              style={{ opacity: isHovered ? 0.8 : 0.3 }}
            />
          </motion.div>

          <h3 className="text-xl font-bold mb-2 text-white">{title}</h3>
          <p className="text-gray-300">{description}</p>

          {stats && (
            <div className="grid grid-cols-2 gap-4 mt-6">
              {stats.map((stat, idx) => (
                <motion.div
                  key={idx}
                  className="bg-white/5 backdrop-filter backdrop-blur-sm p-3 rounded-lg"
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  <p className="text-xl sm:text-2xl font-bold text-white">{stat.value}</p>
                  <p className="text-xs text-gray-400">{stat.label}</p>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* Border glow on hover */}
        <motion.div
          className="absolute inset-0 border-2 border-transparent rounded-2xl z-0"
          animate={{
            borderColor: isHovered ? "rgba(255, 255, 255, 0.5)" : "rgba(255, 255, 255, 0)",
          }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </motion.div>
  )
}
