"use client"

import type React from "react"

import { useEffect, useRef, useState } from "react"

type SoundType = "hover" | "click" | "success" | "error"

interface AudioFeedbackProps {
  children: React.ReactNode
  soundType?: SoundType
  volume?: number
  disabled?: boolean
}

export default function AudioFeedback({
  children,
  soundType = "hover",
  volume = 0.2,
  disabled = false,
}: AudioFeedbackProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Create audio element
    const audio = new Audio()
    audio.volume = volume

    // Set audio source based on sound type
    switch (soundType) {
      case "hover":
        audio.src = "/sounds/hover.mp3" // This would be a placeholder path
        break
      case "click":
        audio.src = "/sounds/click.mp3" // This would be a placeholder path
        break
      case "success":
        audio.src = "/sounds/success.mp3" // This would be a placeholder path
        break
      case "error":
        audio.src = "/sounds/error.mp3" // This would be a placeholder path
        break
    }

    // Create sounds programmatically since we don't have actual sound files
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

    // Function to create and play a sound
    const createSound = (type: SoundType) => {
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      // Configure sound based on type
      switch (type) {
        case "hover":
          oscillator.type = "sine"
          oscillator.frequency.setValueAtTime(2000, audioContext.currentTime)
          oscillator.frequency.exponentialRampToValueAtTime(1500, audioContext.currentTime + 0.1)
          gainNode.gain.setValueAtTime(volume * 0.1, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1)
          oscillator.start()
          oscillator.stop(audioContext.currentTime + 0.1)
          break

        case "click":
          oscillator.type = "square"
          oscillator.frequency.setValueAtTime(1000, audioContext.currentTime)
          oscillator.frequency.exponentialRampToValueAtTime(500, audioContext.currentTime + 0.1)
          gainNode.gain.setValueAtTime(volume * 0.2, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1)
          oscillator.start()
          oscillator.stop(audioContext.currentTime + 0.1)
          break

        case "success":
          // First note
          const osc1 = audioContext.createOscillator()
          const gain1 = audioContext.createGain()
          osc1.connect(gain1)
          gain1.connect(audioContext.destination)
          osc1.type = "sine"
          osc1.frequency.setValueAtTime(1000, audioContext.currentTime)
          gain1.gain.setValueAtTime(volume * 0.2, audioContext.currentTime)
          gain1.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.2)
          osc1.start()
          osc1.stop(audioContext.currentTime + 0.2)

          // Second note
          const osc2 = audioContext.createOscillator()
          const gain2 = audioContext.createGain()
          osc2.connect(gain2)
          gain2.connect(audioContext.destination)
          osc2.type = "sine"
          osc2.frequency.setValueAtTime(1500, audioContext.currentTime + 0.1)
          gain2.gain.setValueAtTime(0.001, audioContext.currentTime + 0.1)
          gain2.gain.exponentialRampToValueAtTime(volume * 0.2, audioContext.currentTime + 0.15)
          gain2.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3)
          osc2.start(audioContext.currentTime + 0.1)
          osc2.stop(audioContext.currentTime + 0.3)
          break

        case "error":
          oscillator.type = "sawtooth"
          oscillator.frequency.setValueAtTime(400, audioContext.currentTime)
          oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.2)
          gainNode.gain.setValueAtTime(volume * 0.2, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.2)
          oscillator.start()
          oscillator.stop(audioContext.currentTime + 0.2)
          break
      }
    }

    // Function to play the sound
    const playSound = () => {
      if (disabled) return

      // Use the programmatic sound instead of audio file
      createSound(soundType)
    }

    // Store the audio element and play function
    audioRef.current = audio

    // Add event listeners to children
    const childElement = document.getElementById("audio-feedback-child")
    if (childElement) {
      if (soundType === "hover") {
        childElement.addEventListener("mouseenter", playSound)
      } else if (soundType === "click") {
        childElement.addEventListener("click", playSound)
      }
    }

    setIsLoaded(true)

    // Cleanup
    return () => {
      if (childElement) {
        if (soundType === "hover") {
          childElement.removeEventListener("mouseenter", playSound)
        } else if (soundType === "click") {
          childElement.removeEventListener("click", playSound)
        }
      }
    }
  }, [soundType, volume, disabled])

  // Wrap children with a div that has event listeners
  return (
    <div id="audio-feedback-child" className="inline-block">
      {children}
    </div>
  )
}
