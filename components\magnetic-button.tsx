"use client"

import type React from "react"

import { useState, useRef, type ReactNode } from "react"
import { motion, useMotionValue, useSpring } from "framer-motion"

interface MagneticButtonProps {
  children: ReactNode
  className?: string
  strength?: number
  radius?: number
  as?: "button" | "a"
  href?: string
  onClick?: () => void
}

export default function MagneticButton({
  children,
  className = "",
  strength = 30,
  radius = 150,
  as = "button",
  href,
  onClick,
}: MagneticButtonProps) {
  const buttonRef = useRef<HTMLButtonElement | HTMLAnchorElement>(null)
  const [isMagnetic, setIsMagnetic] = useState(true)

  // Motion values for the magnetic effect
  const mouseX = useMotionValue(0)
  const mouseY = useMotionValue(0)

  // Add spring physics for smoother movement
  const springConfig = { damping: 15, stiffness: 150 }
  const x = useSpring(mouseX, springConfig)
  const y = useSpring(mouseY, springConfig)

  // Handle mouse move
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!buttonRef.current || !isMagnetic) return

    const rect = buttonRef.current.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    const distanceX = e.clientX - centerX
    const distanceY = e.clientY - centerY
    const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY)

    // Only apply magnetic effect within the defined radius
    if (distance < radius) {
      // Calculate strength based on distance from center (stronger when closer)
      const magneticStrength = strength * (1 - distance / radius)
      mouseX.set((distanceX * magneticStrength) / 10)
      mouseY.set((distanceY * magneticStrength) / 10)
    } else {
      // Reset position when outside radius
      mouseX.set(0)
      mouseY.set(0)
    }
  }

  // Reset position on mouse leave
  const handleMouseLeave = () => {
    mouseX.set(0)
    mouseY.set(0)
  }

  // Disable magnetic effect during click to prevent button from moving away
  const handleMouseDown = () => {
    setIsMagnetic(false)
  }

  const handleMouseUp = () => {
    setIsMagnetic(true)
  }

  // Determine which element to render
  const Component = motion[as as keyof typeof motion]

  return (
    <Component
      ref={buttonRef as any}
      className={className}
      style={{ x, y }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      href={as === "a" ? href : undefined}
      onClick={onClick}
      whileTap={{ scale: 0.95 }}
      whileHover={{ scale: 1.05 }}
    >
      {children}
    </Component>
  )
}
