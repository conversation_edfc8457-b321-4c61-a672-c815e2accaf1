"use client"

import { useEffect, useRef, useState } from "react"

interface ParticleTextEffectProps {
  text: string
  className?: string
  particleColor?: string
  particleCount?: number
  fontSize?: number
  interactive?: boolean
}

export default function ParticleTextEffect({
  text,
  className = "",
  particleColor = "#ffffff",
  particleCount = 1000,
  fontSize = 100,
  interactive = true,
}: ParticleTextEffectProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isHovering, setIsHovering] = useState(false)
  const particlesRef = useRef<Particle[]>([])
  const animationRef = useRef<number>(0)

  // Particle class
  class Particle {
    originX: number
    originY: number
    x: number
    y: number
    size: number
    color: string
    baseX: number
    baseY: number
    density: number
    distance: number

    constructor(x: number, y: number, color: string, size: number) {
      this.x = Math.random() * dimensions.width
      this.y = Math.random() * dimensions.height
      this.originX = x
      this.originY = y
      this.color = color
      this.size = size
      this.baseX = this.x
      this.baseY = this.y
      this.density = Math.random() * 30 + 1
      this.distance = 0
    }

    draw(ctx: CanvasRenderingContext2D) {
      ctx.fillStyle = this.color
      ctx.beginPath()
      ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
      ctx.closePath()
      ctx.fill()
    }

    update(mouseX: number, mouseY: number, isHovering: boolean) {
      // Check if mouse is hovering and interactive mode is enabled
      if (interactive && isHovering) {
        // Calculate distance between particle and mouse
        const dx = mouseX - this.x
        const dy = mouseY - this.y
        const distance = Math.sqrt(dx * dx + dy * dy)
        this.distance = distance

        const forceDirectionX = dx / distance
        const forceDirectionY = dy / distance
        const maxDistance = 100
        const force = (maxDistance - distance) / maxDistance

        // Apply force if within range
        if (distance < maxDistance) {
          this.x -= forceDirectionX * force * this.density
          this.y -= forceDirectionY * force * this.density
        } else {
          // Return to original position
          if (this.x !== this.baseX) {
            const dx = this.x - this.baseX
            this.x -= dx / 10
          }
          if (this.y !== this.baseY) {
            const dy = this.y - this.baseY
            this.y -= dy / 10
          }
        }
      } else {
        // Return to original position with easing
        const dx = this.originX - this.x
        const dy = this.originY - this.y
        this.x += dx * 0.05
        this.y += dy * 0.05
      }
    }
  }

  useEffect(() => {
    const canvas = canvasRef.current
    const container = containerRef.current
    if (!canvas || !container) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    const updateDimensions = () => {
      if (container) {
        const { width, height } = container.getBoundingClientRect()
        setDimensions({ width, height })
        canvas.width = width
        canvas.height = height
      }
    }

    updateDimensions()
    window.addEventListener("resize", updateDimensions)

    // Create text particles
    const createTextParticles = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      ctx.font = `bold ${fontSize}px Arial`
      ctx.fillStyle = "white"
      ctx.textAlign = "center"
      ctx.textBaseline = "middle"
      ctx.fillText(text, canvas.width / 2, canvas.height / 2)

      // Get image data
      const pixels = ctx.getImageData(0, 0, canvas.width, canvas.height).data
      particlesRef.current = []

      // Sample pixels to create particles
      for (let i = 0; i < particleCount; i++) {
        // Random position within canvas
        const x = Math.floor(Math.random() * canvas.width)
        const y = Math.floor(Math.random() * canvas.height)

        // Get pixel index
        const index = (y * canvas.width + x) * 4

        // Check if pixel is part of the text (not transparent)
        if (pixels[index + 3] > 128) {
          const size = Math.random() * 2 + 1
          const particle = new Particle(x, y, particleColor, size)
          particlesRef.current.push(particle)
        }
      }

      // If we didn't get enough particles, try again with more sampling
      if (particlesRef.current.length < particleCount * 0.5) {
        // Sample more systematically
        const step = Math.max(1, Math.floor((canvas.width * canvas.height) / (particleCount * 10)))
        for (let i = 0; i < pixels.length; i += step * 4) {
          if (pixels[i + 3] > 128) {
            const y = Math.floor(i / (canvas.width * 4))
            const x = (i / 4) % canvas.width

            const size = Math.random() * 2 + 1
            const particle = new Particle(x, y, particleColor, size)
            particlesRef.current.push(particle)

            if (particlesRef.current.length >= particleCount) break
          }
        }
      }

      // Clear the canvas after getting the pixel data
      ctx.clearRect(0, 0, canvas.width, canvas.height)
    }

    createTextParticles()

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      particlesRef.current.forEach((particle) => {
        particle.update(mousePosition.x, mousePosition.y, isHovering)
        particle.draw(ctx)
      })

      animationRef.current = requestAnimationFrame(animate)
    }

    animate()

    // Mouse events
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect()
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      })
    }

    const handleMouseEnter = () => setIsHovering(true)
    const handleMouseLeave = () => setIsHovering(false)

    canvas.addEventListener("mousemove", handleMouseMove)
    canvas.addEventListener("mouseenter", handleMouseEnter)
    canvas.addEventListener("mouseleave", handleMouseLeave)

    // Cleanup
    return () => {
      window.removeEventListener("resize", updateDimensions)
      canvas.removeEventListener("mousemove", handleMouseMove)
      canvas.removeEventListener("mouseenter", handleMouseEnter)
      canvas.removeEventListener("mouseleave", handleMouseLeave)
      cancelAnimationFrame(animationRef.current)
    }
  }, [text, particleColor, particleCount, fontSize, dimensions, interactive])

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      <canvas ref={canvasRef} className="absolute inset-0" />
    </div>
  )
}
