"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { motion, type PanInfo, useAnimation } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface MobileSwipeCarouselProps {
  children: React.ReactNode[]
  showDots?: boolean
  showArrows?: boolean
  autoPlay?: boolean
  interval?: number
  className?: string
}

export default function MobileSwipeCarousel({
  children,
  showDots = true,
  showArrows = true,
  autoPlay = false,
  interval = 5000,
  className = "",
}: MobileSwipeCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const controls = useAnimation()
  const containerRef = useRef<HTMLDivElement>(null)

  const childCount = children.length

  // Handle auto play
  useEffect(() => {
    if (!autoPlay) return

    const timer = setInterval(() => {
      const nextIndex = (currentIndex + 1) % childCount
      setCurrentIndex(nextIndex)
      controls.start({ x: `${-nextIndex * 100}%` })
    }, interval)

    return () => clearInterval(timer)
  }, [autoPlay, interval, currentIndex, childCount, controls])

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false)

    const threshold = 50 // minimum distance to trigger a slide change
    const velocity = 0.5 // minimum velocity to trigger a slide change

    if (Math.abs(info.offset.x) > threshold || Math.abs(info.velocity.x) > velocity) {
      if (info.offset.x > 0 && currentIndex > 0) {
        // Swiped right, go to previous
        setCurrentIndex(currentIndex - 1)
      } else if (info.offset.x < 0 && currentIndex < childCount - 1) {
        // Swiped left, go to next
        setCurrentIndex(currentIndex + 1)
      }
    }

    // Animate to the current index
    controls.start({ x: `${-currentIndex * 100}%` })
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    controls.start({ x: `${-index * 100}%` })
  }

  const nextSlide = () => {
    if (currentIndex < childCount - 1) {
      goToSlide(currentIndex + 1)
    }
  }

  const prevSlide = () => {
    if (currentIndex > 0) {
      goToSlide(currentIndex - 1)
    }
  }

  return (
    <div className={`relative overflow-hidden ${className}`} ref={containerRef}>
      <motion.div
        className="flex w-full h-full"
        drag="x"
        dragConstraints={{ left: -((childCount - 1) * 100), right: 0 }}
        dragElastic={0.1}
        onDragStart={() => setIsDragging(true)}
        onDragEnd={handleDragEnd}
        animate={controls}
        initial={{ x: 0 }}
        transition={{ type: "spring", damping: 30, stiffness: 300 }}
        style={{ width: `${childCount * 100}%` }}
      >
        {children.map((child, index) => (
          <div key={index} className="flex-shrink-0" style={{ width: "100%" }}>
            {child}
          </div>
        ))}
      </motion.div>

      {/* Navigation dots */}
      {showDots && childCount > 1 && (
        <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
          {Array.from({ length: childCount }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2 h-2 rounded-full transition-all ${
                currentIndex === index ? "bg-yellow-500 w-4" : "bg-gray-400/50"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Navigation arrows */}
      {showArrows && childCount > 1 && (
        <>
          <button
            onClick={prevSlide}
            className={`absolute top-1/2 left-2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center text-white ${
              currentIndex === 0 ? "opacity-30 cursor-not-allowed" : "opacity-70 hover:opacity-100"
            }`}
            disabled={currentIndex === 0}
            aria-label="Previous slide"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <button
            onClick={nextSlide}
            className={`absolute top-1/2 right-2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center text-white ${
              currentIndex === childCount - 1 ? "opacity-30 cursor-not-allowed" : "opacity-70 hover:opacity-100"
            }`}
            disabled={currentIndex === childCount - 1}
            aria-label="Next slide"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </>
      )}
    </div>
  )
}
