"use client"

import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import {
  <PERSON>R<PERSON>,
  Brain,
  Bot,
  Shield,
  Check,
  Sparkles,
  Star,
  Zap,
  Users,
  Database,
  FileText,
  Code,
  Lock,
  Search,
} from "lucide-react"
import AnimatedText from "@/components/animated-text"
import ThreeDCard from "@/components/3d-card"
import LunarGlowCard from "@/components/lunar-glow-card"

export default function CustomGPTsPage() {
  return (
    <main className="pt-24 relative">
      {/* Header */}
      <section className="bg-black text-white py-20 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <Image
            src="https://images.unsplash.com/photo-1589254065878-42c9da997008?q=80&w=1920&auto=format&fit=crop"
            alt="Custom GPTs header background"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black to-transparent"></div>
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center relative z-10">
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Brain className="h-4 w-4 mr-2" />
                Advanced AI Solutions
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <AnimatedText
                text="Custom GPTs & AI Assistants"
                className="bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300"
                delay={0.2}
              />
            </h1>
            <p className="text-gray-300 text-lg">
              Tailored AI assistants and custom GPTs designed specifically for your organization's unique knowledge base
              and workflows.
            </p>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=1920&auto=format&fit=crop"
            alt="Custom GPTs overview background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
                <span className="text-red-300 text-sm font-medium flex items-center">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Tailored Intelligence
                </span>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
                AI Assistants Designed Exclusively for Your Organization
              </h2>
              <p className="text-gray-300 text-lg mb-6">
                Our custom GPTs and AI assistants are meticulously crafted to embody your brand's voice, expertise, and
                knowledge, delivering exceptional value to your team and customers.
              </p>
              <p className="text-gray-300 mb-8">
                Each solution is built with your proprietary data, specific use cases, and security requirements in
                mind, ensuring a perfect fit for your organization's unique needs.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Knowledge Integration</span>
                  </div>
                  <p className="text-gray-300 text-sm">Seamlessly incorporate your proprietary data and expertise</p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Brand Alignment</span>
                  </div>
                  <p className="text-gray-300 text-sm">AI assistants that embody your brand's voice and values</p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Enterprise Security</span>
                  </div>
                  <p className="text-gray-300 text-sm">Robust security measures to protect sensitive information</p>
                </div>
                <div className="bg-gray-900/50 backdrop-blur-sm p-4 rounded-lg border border-gray-800">
                  <div className="flex items-center mb-2">
                    <Check className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-white font-medium">Continuous Learning</span>
                  </div>
                  <p className="text-gray-300 text-sm">Systems that evolve and improve with usage over time</p>
                </div>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              <ThreeDCard className="p-6">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800">
                  <div className="relative h-[400px] rounded-lg overflow-hidden">
                    <Image
                      src="https://images.unsplash.com/photo-1677442135145-6a87cceb0d77?q=80&w=800&auto=format&fit=crop"
                      alt="AI Assistant Interface"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6">
                      <div className="bg-gray-900/80 backdrop-blur-md p-4 rounded-lg border border-gray-700">
                        <div className="flex items-center mb-3">
                          <div className="w-10 h-10 rounded-full bg-purple-600 flex items-center justify-center mr-3">
                            <Brain className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="text-white font-bold">LunarGPT</h3>
                            <p className="text-gray-300 text-xs">Enterprise Assistant • Secure Mode</p>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="bg-gray-800/50 p-2 rounded-lg text-gray-300 text-sm">
                            How can I assist with your enterprise AI needs today?
                          </div>
                          <div className="bg-purple-600/20 p-2 rounded-lg text-gray-200 text-sm">
                            Can you analyze our quarterly security report and highlight key vulnerabilities?
                          </div>
                          <div className="bg-gray-800/50 p-2 rounded-lg text-gray-300 text-sm">
                            I've analyzed your quarterly security report. Key vulnerabilities include: 1) Outdated
                            authentication protocols in the legacy system, 2) Potential API exposure in the new cloud
                            deployment, and 3) Insufficient encryption for data at rest. Would you like detailed
                            recommendations for addressing each issue?
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ThreeDCard>
              <div className="absolute -bottom-10 -right-10 z-10">
                <div className="bg-gray-900/80 backdrop-blur-lg p-4 rounded-lg border border-gray-800 shadow-xl">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-green-600 flex items-center justify-center mr-2">
                      <Lock className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-white text-sm">Enterprise-Grade Security</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Solution Types Section */}
      <section className="py-24 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1563986768609-322da13575f3?q=80&w=1920&auto=format&fit=crop"
            alt="Solution Types background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-2" />
                AI Solutions
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Comprehensive AI Offerings
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Our suite of AI solutions is designed to address a wide range of business needs, from customer engagement
              to internal knowledge management.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Custom GPTs Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <ThreeDCard className="p-6 h-full">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full border border-gray-800">
                  <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-purple-600/80 to-purple-800/80 shadow-lg">
                    <Brain className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white">Custom GPTs</h3>
                  <p className="text-gray-300 mb-6">
                    Specialized GPT models tailored to your organization's specific domain, knowledge base, and use
                    cases.
                  </p>

                  <h4 className="text-lg font-semibold text-white mb-4">Key Features:</h4>
                  <div className="space-y-4 mb-6">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-purple-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Database className="h-4 w-4 text-purple-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Knowledge Integration</h5>
                        <p className="text-gray-300 text-sm">Incorporate your proprietary data and domain expertise</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-purple-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Code className="h-4 w-4 text-purple-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Custom Capabilities</h5>
                        <p className="text-gray-300 text-sm">Specialized functions and tools tailored to your needs</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-purple-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Users className="h-4 w-4 text-purple-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Brand Voice Alignment</h5>
                        <p className="text-gray-300 text-sm">
                          AI that communicates with your organization's unique tone and style
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-purple-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Shield className="h-4 w-4 text-purple-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Enterprise Security</h5>
                        <p className="text-gray-300 text-sm">
                          Robust security measures to protect sensitive information
                        </p>
                      </div>
                    </div>
                  </div>

                  <Link
                    href="/contact"
                    className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors shadow-lg shadow-purple-600/20"
                  >
                    Discuss Your Custom GPT
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </ThreeDCard>
            </motion.div>

            {/* Enterprise AI Assistants Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <ThreeDCard className="p-6 h-full">
                <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg h-full border border-gray-800">
                  <div className="w-16 h-16 rounded-2xl mb-6 flex items-center justify-center bg-gradient-to-br from-red-600/80 to-red-800/80 shadow-lg">
                    <Bot className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-white">Enterprise AI Assistants</h3>
                  <p className="text-gray-300 mb-6">
                    Comprehensive AI solutions that integrate with your existing systems to enhance productivity and
                    decision-making.
                  </p>

                  <h4 className="text-lg font-semibold text-white mb-4">Key Features:</h4>
                  <div className="space-y-4 mb-6">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-red-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Zap className="h-4 w-4 text-red-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">System Integration</h5>
                        <p className="text-gray-300 text-sm">
                          Seamless connection with your existing enterprise systems
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-red-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <FileText className="h-4 w-4 text-red-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Document Intelligence</h5>
                        <p className="text-gray-300 text-sm">
                          Advanced processing and understanding of complex documents
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-red-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Search className="h-4 w-4 text-red-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Intelligent Search</h5>
                        <p className="text-gray-300 text-sm">
                          Enhanced knowledge retrieval across your organization's data
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-lg bg-red-600/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <Lock className="h-4 w-4 text-red-400" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">Compliance Controls</h5>
                        <p className="text-gray-300 text-sm">Built-in guardrails to ensure regulatory compliance</p>
                      </div>
                    </div>
                  </div>

                  <Link
                    href="/contact"
                    className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-md font-medium inline-flex items-center transition-colors shadow-lg shadow-red-600/20"
                  >
                    Discuss Your AI Assistant
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </ThreeDCard>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-24 bg-gray-950 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1551434678-e076c223a692?q=80&w=1920&auto=format&fit=crop"
            alt="Use Cases background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div className="inline-block bg-gradient-to-r from-red-900/50 to-red-600/50 px-4 py-1 rounded-full mb-4 backdrop-blur-sm">
              <span className="text-red-300 text-sm font-medium flex items-center">
                <Star className="h-4 w-4 mr-2" />
                Applications
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
              Strategic Use Cases
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover how our custom GPTs and AI assistants can transform your operations and enhance decision-making
              across your organization.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Knowledge Management",
                description:
                  "Transform how your organization accesses and utilizes institutional knowledge with AI-powered retrieval and insights.",
                icon: <Database className="h-6 w-6 text-red-500" />,
                metrics: "Reduce search time by 78%",
              },
              {
                title: "Customer Support",
                description:
                  "Enhance customer experience with intelligent assistants that provide accurate, consistent responses across all channels.",
                icon: <Users className="h-6 w-6 text-red-500" />,
                metrics: "Improve resolution rates by 65%",
              },
              {
                title: "Executive Assistance",
                description:
                  "Provide leadership with AI assistants that summarize information, prepare briefings, and support decision-making.",
                icon: <Brain className="h-6 w-6 text-red-500" />,
                metrics: "Save 15+ hours per week",
              },
              {
                title: "Compliance & Risk",
                description:
                  "Ensure adherence to regulations and identify potential risks with AI-powered analysis and monitoring.",
                icon: <Shield className="h-6 w-6 text-red-500" />,
                metrics: "99.8% compliance accuracy",
              },
              {
                title: "Research & Development",
                description:
                  "Accelerate innovation with AI assistants that analyze research, identify patterns, and generate insights.",
                icon: <Search className="h-6 w-6 text-red-500" />,
                metrics: "Reduce research time by 42%",
              },
              {
                title: "Employee Productivity",
                description:
                  "Empower your workforce with personalized AI assistants that enhance individual and team productivity.",
                icon: <Zap className="h-6 w-6 text-red-500" />,
                metrics: "Boost productivity by 37%",
              },
            ].map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <LunarGlowCard glowColor="purple" intensity="low">
                  <div className="bg-gray-900/70 backdrop-blur-sm p-6 rounded-lg border border-gray-800 h-full">
                    <div className="w-12 h-12 rounded-xl mb-6 flex items-center justify-center bg-gradient-to-br from-purple-600/30 to-purple-800/30">
                      {useCase.icon}
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-white">{useCase.title}</h3>
                    <p className="text-gray-300 mb-4">{useCase.description}</p>
                    <div className="bg-green-900/20 border border-green-900/50 rounded-md p-3">
                      <div className="flex items-center">
                        <Check className="h-5 w-5 text-green-500 mr-2" />
                        <span className="text-green-400 font-medium">{useCase.metrics}</span>
                      </div>
                    </div>
                  </div>
                </LunarGlowCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1920&auto=format&fit=crop"
            alt="CTA background"
            fill
            className="object-cover"
          />
        </div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-gradient-to-r from-purple-900/30 to-purple-800/30 rounded-lg p-8 md:p-12 border border-purple-800/50 backdrop-blur-sm">
            <div className="flex flex-col md:flex-row items-center justify-between gap-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                  Ready to Transform Your Organization with AI?
                </h2>
                <p className="text-gray-300 text-lg">
                  Let's discuss how our custom GPTs and AI assistants can elevate your operations and decision-making
                  capabilities.
                </p>
              </div>
              <Link
                href="/contact"
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-8 py-4 rounded-md font-medium flex items-center justify-center transition-colors group text-lg shadow-lg shadow-purple-600/20"
              >
                Schedule a Consultation
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
