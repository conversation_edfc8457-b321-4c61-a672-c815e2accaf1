"use client"

import { useEffect, useRef, useState } from "react"
import * as THREE from "three"

export default function WebGLBackground() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLowPerfDevice, setIsLowPerfDevice] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Check if device is likely low performance
    const checkPerformance = () => {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      const isOlderDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4
      setIsLowPerfDevice(isMobile || isOlderDevice)
    }

    checkPerformance()

    // Use Intersection Observer to only animate when visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting)
        })
      },
      { threshold: 0.1 },
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    // Delay animation start to prioritize content loading
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, 1000)

    return () => {
      observer.disconnect()
      clearTimeout(timer)
    }
  }, [])

  useEffect(() => {
    // Only start animation when element is visible and page has loaded
    if (!isVisible || !isLoaded) return

    if (!containerRef.current) return

    // For extremely low performance devices, use a static div with a gradient background
    if (isLowPerfDevice) {
      const staticBackground = document.createElement("div")
      staticBackground.style.position = "absolute"
      staticBackground.style.inset = "0"
      staticBackground.style.background = "radial-gradient(circle at 50% 50%, #1a1a4a, #000000)"
      staticBackground.style.opacity = "0.7"

      containerRef.current.appendChild(staticBackground)

      return () => {
        if (containerRef.current && staticBackground) {
          containerRef.current.removeChild(staticBackground)
        }
      }
    }

    // For higher performance devices, use a simplified THREE.js scene
    // Scene setup
    const scene = new THREE.Scene()

    // Camera setup
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
    camera.position.z = 5

    // Renderer setup - use lower pixel ratio and no antialias
    const renderer = new THREE.WebGLRenderer({
      antialias: false,
      alpha: true,
      powerPreference: "low-power",
    })
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(1) // Force 1x pixel ratio
    renderer.setClearColor(0x000000, 0)
    containerRef.current.appendChild(renderer.domElement)

    // Create particles - extremely reduced count
    const particlesGeometry = new THREE.BufferGeometry()
    const particlesCount = 200 // Drastically reduced

    const posArray = new Float32Array(particlesCount * 3)

    // Fill the arrays with random positions
    for (let i = 0; i < particlesCount * 3; i += 3) {
      // Position in a sphere
      const radius = 15 + Math.random() * 10
      const theta = Math.random() * Math.PI * 2
      const phi = Math.random() * Math.PI

      posArray[i] = radius * Math.sin(phi) * Math.cos(theta) // x
      posArray[i + 1] = radius * Math.sin(phi) * Math.sin(theta) // y
      posArray[i + 2] = radius * Math.cos(phi) // z
    }

    particlesGeometry.setAttribute("position", new THREE.BufferAttribute(posArray, 3))

    // Particle material - simplified
    const particlesMaterial = new THREE.PointsMaterial({
      size: 0.05,
      sizeAttenuation: true,
      color: 0xffffff,
      transparent: true,
      opacity: 0.6,
    })

    // Create the particle system
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial)
    scene.add(particlesMesh)

    // Add ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.3)
    scene.add(ambientLight)

    // Animation with extremely low frequency updates
    const clock = new THREE.Clock()
    let lastTime = 0
    const frameInterval = 500 // Very low fps (2fps)
    let animationFrame: number

    const animate = (timestamp: number) => {
      const deltaTime = timestamp - lastTime

      if (deltaTime > frameInterval) {
        lastTime = timestamp - (deltaTime % frameInterval)

        const elapsedTime = clock.getElapsedTime()

        // Very slow rotation
        particlesMesh.rotation.x = elapsedTime * 0.01
        particlesMesh.rotation.y = elapsedTime * 0.005

        // Render
        renderer.render(scene, camera)
      }

      // Call animate again on the next frame
      animationFrame = requestAnimationFrame(animate)
    }

    animationFrame = requestAnimationFrame(animate)

    // Handle window resize - debounced
    let resizeTimer: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimer)
      resizeTimer = setTimeout(() => {
        // Update camera
        camera.aspect = window.innerWidth / window.innerHeight
        camera.updateProjectionMatrix()

        // Update renderer
        renderer.setSize(window.innerWidth, window.innerHeight)
      }, 200)
    }

    window.addEventListener("resize", handleResize)

    // Cleanup
    return () => {
      window.removeEventListener("resize", handleResize)
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement)
      }
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
      scene.remove(particlesMesh)
      particlesGeometry.dispose()
      particlesMaterial.dispose()
    }
  }, [isLowPerfDevice, isVisible, isLoaded])

  return <div ref={containerRef} className="absolute inset-0" />
}
